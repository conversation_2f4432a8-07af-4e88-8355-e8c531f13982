@props([
    'name' => 'data-table local csv'
    'description' => 'A local data table generated from CSV data',
    'type' => 'csv',
    'headers' => {{ $headers }},
    'rows' => {{ $rows }}
])
@php
{{-- Data table template --}}
{{-- This template will be used to generate data table views --}}
// Check if we have valid data
if (empty($headers) || !is_array($headers)) {
    echo '<div class="p-4 text-gray-500">No data available - please upload a CSV file or paste CSV data.</div>';
    return;
}

// Transform headers into the format expected by data-table component
$columns = [];
foreach ($headers as $header) {
    $columns[] = [
        'label' => $header,
        'field' => $header,
        'sortable' => true
    ];
}

// Transform rows into associative arrays using headers as keys
$items = [];
foreach ($rows as $row) {
    $item = [];
    foreach ($headers as $index => $header) {
        $item[$header] = $row[$index] ?? '';
    }
    $items[] = $item;
}
@endphp

<x-data-table
    :columns="$columns"
    :items="$items"
    striped="true"
    hover="true"
    just_body="false"
    just_rows="false"
    items_per_page="50"
    current_page="1"
    sort_column=""
    sort_direction="asc"
    callback=""
    id_count="1"
    class=""
    :rows="[
        'class_postfix' => '',
        'id_prefix' => 'row_',
        'id_field' => array_key_exists(0, $headers) ? $headers[0] : 'id',
        'id_postfix' => '',
        'extra_parameters' => ''
    ]"
/>