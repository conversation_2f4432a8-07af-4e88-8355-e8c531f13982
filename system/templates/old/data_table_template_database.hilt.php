@props([
    'name' => 'data-table database-driven',
    'description' => 'A database-driven data table using hilt system',
    'type' => 'hilt',
    'table_name' => '{{ $table_name }}',
    'route_key' => '{{ $route_key }}'
])

@php
{{-- Database-driven data table template --}}
{{-- This template uses the hilt system to display data from database --}}

use system\hilt;
use system\database;

// Get table name and route key
$table_name = $table_name ?? 'autobooks_data';
$route_key = $route_key ?? 'default';

// Get query parameters for filtering, pagination, etc.
$search = $_GET['search'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$per_page = (int)($_GET['per_page'] ?? 50);
$sort_column = $_GET['sort_column'] ?? '';
$sort_direction = $_GET['sort_direction'] ?? 'asc';

// Build criteria for data retrieval
$criteria = [
    'limit' => $per_page,
    'offset' => ($page - 1) * $per_page
];

if (!empty($search)) {
    $criteria['search'] = $search;
}

// Get data from hilt table
$data_result = hilt::get_table_data($table_name, $criteria);

if (isset($data_result['error'])) {
    echo '<div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">';
    echo '<h2 class="text-lg font-bold mb-2">Data Error</h2>';
    echo '<p>' . htmlspecialchars($data_result['error']) . '</p>';
    echo '<p class="mt-2 text-sm">Table: ' . htmlspecialchars($table_name) . '</p>';
    echo '</div>';
    return;
}

$items = $data_result['data'] ?? [];

if (empty($items)) {
    echo '<div class="p-4 text-gray-500">';
    echo '<h2 class="text-lg font-bold mb-2">No Data Available</h2>';
    echo '<p>No data found in table: ' . htmlspecialchars($table_name) . '</p>';
    echo '<p class="mt-2">Please upload CSV data or check the settings page to configure data import.</p>';
    echo '<a href="' . APP_ROOT . $route_key . '.settings" class="mt-2 inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Configure Settings</a>';
    echo '</div>';
    return;
}

// Extract column headers from first item (excluding system fields)
$system_fields = ['id', 'created_at', 'updated_at'];
$first_item = $items[0];
$headers = array_keys(array_diff_key($first_item, array_flip($system_fields)));

// Transform headers into the format expected by data-table component
$columns = [];
foreach ($headers as $header) {
    $columns[] = [
        'label' => ucwords(str_replace('_', ' ', $header)),
        'field' => $header,
        'sortable' => true
    ];
}

// Add system columns if needed
$columns[] = [
    'label' => 'Created',
    'field' => 'created_at',
    'sortable' => true
];

// Get total count for pagination
try {
    $total_count = database::table($table_name)->count();
} catch (Exception $e) {
    $total_count = count($items);
}

$total_pages = ceil($total_count / $per_page);
@endphp

<div class="hilt-data-table" data-table="{{ $table_name }}" data-route="{{ $route_key }}">
    {{-- Header with search and controls --}}
    <div class="mb-4 flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <h2 class="text-xl font-bold">{{ ucwords(str_replace('_', ' ', $route_key)) }} Data</h2>
            <span class="text-sm text-gray-500">{{ $total_count }} records</span>
        </div>
        
        <div class="flex items-center space-x-2">
            {{-- Search form --}}
            <form method="GET" class="flex items-center space-x-2">
                <input type="text" 
                       name="search" 
                       value="{{ $search }}" 
                       placeholder="Search data..." 
                       class="px-3 py-1 border rounded">
                <button type="submit" class="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">
                    Search
                </button>
                @if(!empty($search))
                    <a href="?" class="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                        Clear
                    </a>
                @endif
            </form>
            
            {{-- Settings link --}}
            <a href="{{ APP_ROOT }}{{ $route_key }}.settings" 
               class="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600">
                Settings
            </a>
        </div>
    </div>

    {{-- Data table component --}}
    <x-data-table
        :columns="$columns"
        :items="$items"
        striped="true"
        hover="true"
        just_body="false"
        just_rows="false"
        items_per_page="{{ $per_page }}"
        current_page="{{ $page }}"
        sort_column="{{ $sort_column }}"
        sort_direction="{{ $sort_direction }}"
        callback=""
        id_count="1"
        class="hilt-table"
        :rows="[
            'class_postfix' => '',
            'id_prefix' => 'row_',
            'id_field' => 'id',
            'id_postfix' => '',
            'extra_parameters' => ''
        ]"
    />

    {{-- Pagination --}}
    @if($total_pages > 1)
        <div class="mt-4 flex justify-center">
            <nav class="flex items-center space-x-2">
                @if($page > 1)
                    <a href="?page={{ $page - 1 }}&search={{ urlencode($search) }}" 
                       class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">
                        Previous
                    </a>
                @endif
                
                @for($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++)
                    @if($i == $page)
                        <span class="px-3 py-1 bg-blue-500 text-white rounded">{{ $i }}</span>
                    @else
                        <a href="?page={{ $i }}&search={{ urlencode($search) }}" 
                           class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">
                            {{ $i }}
                        </a>
                    @endif
                @endfor
                
                @if($page < $total_pages)
                    <a href="?page={{ $page + 1 }}&search={{ urlencode($search) }}" 
                       class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">
                        Next
                    </a>
                @endif
            </nav>
        </div>
    @endif
</div>

{{-- JavaScript for enhanced functionality --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.hilt-data-table');
    if (table) {
        // Add any client-side functionality here
        console.log('Hilt data table initialized for:', table.dataset.route);
    }
});
</script>
