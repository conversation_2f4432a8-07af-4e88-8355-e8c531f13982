<?php
namespace api\data_table;
use data_table\data_table;
use edge\edge;

// Include enhanced data callback functions if they exist
if (file_exists(FS_SYS_FUNCTIONS . DS . 'enhanced_data_callbacks.php')) {
    require_once FS_SYS_FUNCTIONS . DS. 'enhanced_data_callbacks.php';
}

// Include logs callback functions if they exist
if (file_exists(FS_SYS_FUNCTIONS . DS . 'logs.fn.php')) {
    require_once FS_SYS_FUNCTIONS . DS . 'logs.fn.php';
}

function data_table_filter($p){
    $criteria = data_table::api_process_criteria($p);
    if(isset($p['callback']) && function_exists($p['callback'])) {
        print_rr("calling " . $p['callback'] . " criteria: " . print_r($criteria, true));
        return $p['callback'](criteria: $criteria);
    }
  print_rr("no callback found for " . $p['callback'] . " criteria: " . print_r($criteria, true));
    // If no callback, try to load data from data source
    if (isset($p['table_name'])) {
        $user_id = $_SESSION['user_id'] ?? null;
        $data_result = \data_table_storage::get_table_data($p['table_name'], [], $criteria, $user_id);

        if ($data_result['success']) {
            $items = $data_result['data'];
            $columns = $data_result['columns'] ?? [];
            $available_fields = [];

            // Auto-generate columns from data if we have data
            if (!empty($items)) {
                $first_item = reset($items);
                foreach (array_keys($first_item) as $field) {
                    $columns[] = [
                        'label' => ucwords(str_replace('_', ' ', $field)),
                        'field' => $field,
                        'filter' => false,
                        'extra_parameters' => ''
                    ];
                    $available_fields[] = $field;
                }
            }

            return edge::render('data-table', [
                'table_name' => $p['table_name'],
                'items' => $items,
                'columns' => $columns,
                'available_fields' => $available_fields,
                'just_body' => $p['just_body'] ?? false
            ]);
        }
    }

    return edge::render('data-table', ['table_name' => $p['table_name'] ?? '']);
}

/**
 * Handle data table pagination requests
 * This function processes pagination requests from data tables
 *
 * @param array $p Parameters from the request including page, callback, etc.
 * @return string HTML content of the updated data table
 */
function pagination($p) {
    // Extract pagination parameters
    $page = (int)($p['page'] ?? 1);
    $per_page = (int)($p['per_page'] ?? 30);
    $callback = $p['callback'] ?? '';
    $table_name = $p['table_name'] ?? '';

    // Build criteria for the callback function
    $criteria = [
        'limit' => $per_page,
        'offset' => ($page - 1) * $per_page,
        'page' => $page,
        'pagination_mode' => true  // Flag to indicate this is a pagination request
    ];

    // Add search terms if provided
    if (isset($p['search_terms']) && !empty($p['search_terms'])) {
        $criteria['search'] = $p['search_terms'];
    }

    // Add sorting if provided
    if (isset($p['sort_column']) && !empty($p['sort_column'])) {
        $criteria['order_by'] = $p['sort_column'];
        $criteria['order_direction'] = $p['sort_direction'] ?? 'asc';
    }

    // Call the appropriate callback function
    if (!empty($callback) && function_exists($callback)) {
        return $callback($criteria);
    } elseif (!empty($table_name)) {
        // Use enhanced data API for table-based requests
        return \api\enhanced_data\get_table_data(array_merge($p, [
            'page' => $page,
            'per_page' => $per_page,
            'just_body' => true
        ]));
    }

    return '<div class="error">Invalid pagination request</div>';
}