[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_Sketchup_data
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: id = 100
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: sold_to_number = 95
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: vendor_name = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: reseller_number = 95
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: reseller_vendor_id = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_address_1 = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_address_2 = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_address_3 = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_city = 94
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_state = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_account_type = 92
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_name = 94
[data_table_generator] [2025-08-07 09:52:19] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_phone = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: end_customer_industry_segment = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_number = 95
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_start_date = 86
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_end_date = 86
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_terms = 84
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_type = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_support_level = 84
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_days_due = 81
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: agreement_autorenew = 81
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_name = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_family = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_market_segment = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_release = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_type = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_deployment = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_sku = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_part = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_list_price = 89
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_serial_number = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_quantity = 89
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_start_date = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_end_date = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_contact_name = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_contact_email = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_level = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: subscription_days_due = 89
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_id = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_type = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_vendor_id = 100
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_deal_registration_number = 86
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_status = 92
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_resellerpo_previous = 84
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: quotation_due_date = 86
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: flaer_phase = 84
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:166] Column relevance score: updated = 86
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: id (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: end_customer_zip_code (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: end_customer_country (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: end_customer_contact_email (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:184] Showing column: agreement_program_name (score: 100)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_vendor_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: sold_to_number (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: reseller_number (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_number (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_city (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_contact_name (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: vendor_name (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: reseller_vendor_id (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_address_1 (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_address_2 (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_address_3 (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_state (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_account_type (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_contact_phone (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: end_customer_industry_segment (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_type (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_family (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_market_segment (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_release (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_deployment (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_sku (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_part (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_serial_number (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_start_date (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_end_date (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_contact_name (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_contact_email (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_level (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_id (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_type (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_status (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: product_list_price (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_quantity (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: subscription_days_due (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_start_date (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_end_date (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_deal_registration_number (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_due_date (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: updated (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_terms (score: 84, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_support_level (score: 84, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: quotation_resellerpo_previous (score: 84, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: flaer_phase (score: 84, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_days_due (score: 81, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:181] Hiding column: agreement_autorenew (score: 81, visible_count: 8)
[data_table_generator] [2025-08-07 09:52:20] [data_table_generator.class.php:190] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-07 10:23:52] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_Sketchup_data
[data_table_generator] [2025-08-07 10:52:02] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_sketchup_data
[data_table_generator] [2025-08-07 11:24:58] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_sketchup2_data
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_sketch3_data
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: id = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: sold_to_number = 98
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: vendor_name = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: reseller_number = 98
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: reseller_vendor_id = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_address_1 = 99
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_address_2 = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_address_3 = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_state = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_account_type = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_phone = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: end_customer_industry_segment = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_start_date = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_end_date = 91
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_name = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_family = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_market_segment = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_release = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_type = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_deployment = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_sku = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_part = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_list_price = 94
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_quantity = 92
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_start_date = 99
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_end_date = 97
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_contact_name = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_contact_email = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_vendor_id = 100
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_status = 95
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_resellerpo_previous = 85
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: quotation_due_date = 91
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:166] Column relevance score: updated = 89
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: id (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: end_customer_city (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: end_customer_zip_code (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: end_customer_country (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:184] Showing column: end_customer_contact_name (score: 100)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_vendor_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_address_1 (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_start_date (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: sold_to_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: reseller_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_end_date (score: 97, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: vendor_name (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: reseller_vendor_id (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_account_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_family (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_deployment (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_sku (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_status (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_list_price (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_address_2 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_address_3 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_state (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_contact_phone (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: end_customer_industry_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_start_date (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_market_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_release (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: product_part (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_contact_name (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_contact_email (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: subscription_quantity (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_end_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_due_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: quotation_resellerpo_previous (score: 85, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:181] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-07 11:27:11] [data_table_generator.class.php:190] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_sketch4_data
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: id = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: sold_to_number = 98
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: vendor_name = 95
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: reseller_number = 98
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: reseller_vendor_id = 95
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_address_1 = 99
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_address_2 = 93
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_address_3 = 93
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_state = 93
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_account_type = 95
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-07 11:33:40] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_phone = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: end_customer_industry_segment = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_start_date = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_end_date = 91
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_name = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_family = 95
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_market_segment = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_release = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_type = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_deployment = 95
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_sku = 95
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_part = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_list_price = 94
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_quantity = 92
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_start_date = 99
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_end_date = 97
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_contact_name = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_contact_email = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_vendor_id = 100
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_status = 95
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_resellerpo_previous = 85
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: quotation_due_date = 91
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:166] Column relevance score: updated = 89
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: id (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: end_customer_city (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: end_customer_zip_code (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: end_customer_country (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:184] Showing column: end_customer_contact_name (score: 100)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_vendor_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_address_1 (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_start_date (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: sold_to_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: reseller_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_end_date (score: 97, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: vendor_name (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: reseller_vendor_id (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_account_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_family (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_deployment (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_sku (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_status (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_list_price (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_address_2 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_address_3 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_state (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_contact_phone (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: end_customer_industry_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_start_date (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_market_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_release (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: product_part (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_contact_name (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_contact_email (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: subscription_quantity (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_end_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_due_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: quotation_resellerpo_previous (score: 85, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:181] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-07 11:33:41] [data_table_generator.class.php:190] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-07 11:59:56] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_sketch0708_data
[data_table_generator] [2025-08-07 11:59:56] [data_table_generator.class.php:166] Column relevance score: id = 100
[data_table_generator] [2025-08-07 11:59:56] [data_table_generator.class.php:166] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-07 11:59:56] [data_table_generator.class.php:166] Column relevance score: sold_to_number = 98
[data_table_generator] [2025-08-07 11:59:56] [data_table_generator.class.php:166] Column relevance score: vendor_name = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: reseller_number = 98
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: reseller_vendor_id = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_address_1 = 99
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_address_2 = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_address_3 = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_state = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_account_type = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_phone = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: end_customer_industry_segment = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_start_date = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_end_date = 91
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_name = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_family = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_market_segment = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_release = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_type = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_deployment = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_sku = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_part = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_list_price = 94
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_quantity = 92
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_start_date = 99
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_end_date = 97
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_contact_name = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_contact_email = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_vendor_id = 100
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_status = 95
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_resellerpo_previous = 85
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: quotation_due_date = 91
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:166] Column relevance score: updated = 89
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: id (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: end_customer_city (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: end_customer_zip_code (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: end_customer_country (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:184] Showing column: end_customer_contact_name (score: 100)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_vendor_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_address_1 (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_start_date (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: sold_to_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: reseller_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_end_date (score: 97, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: vendor_name (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: reseller_vendor_id (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_account_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_family (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_deployment (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_sku (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_status (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_list_price (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_address_2 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_address_3 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_state (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_contact_phone (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: end_customer_industry_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_start_date (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_market_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_release (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: product_part (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_contact_name (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_contact_email (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: subscription_quantity (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_end_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_due_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: quotation_resellerpo_previous (score: 85, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:181] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-07 11:59:57] [data_table_generator.class.php:190] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_sketchup1320_data
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: id = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: sold_to_number = 98
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: vendor_name = 95
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: reseller_number = 98
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: reseller_vendor_id = 95
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_address_1 = 99
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_address_2 = 93
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_address_3 = 93
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_state = 93
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_account_type = 95
[data_table_generator] [2025-08-07 12:20:12] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: end_customer_contact_phone = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: end_customer_industry_segment = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_start_date = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_end_date = 91
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_name = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_family = 95
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_market_segment = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_release = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_type = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_deployment = 95
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_sku = 95
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_part = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_list_price = 94
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_quantity = 92
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_start_date = 99
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_end_date = 97
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_contact_name = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_contact_email = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_vendor_id = 100
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_status = 95
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_resellerpo_previous = 85
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: quotation_due_date = 91
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:166] Column relevance score: updated = 89
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: id (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: end_customer_city (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: end_customer_zip_code (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: end_customer_country (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:184] Showing column: end_customer_contact_name (score: 100)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_vendor_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_address_1 (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_start_date (score: 99, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: sold_to_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: reseller_number (score: 98, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_end_date (score: 97, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: vendor_name (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: reseller_vendor_id (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_account_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_family (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_deployment (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_sku (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_status (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_list_price (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_address_2 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_address_3 (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_state (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_contact_phone (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: end_customer_industry_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_start_date (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_market_segment (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_release (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: product_part (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_contact_name (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_contact_email (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: subscription_quantity (score: 92, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_end_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_due_date (score: 91, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: quotation_resellerpo_previous (score: 85, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:181] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-07 12:20:13] [data_table_generator.class.php:190] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:147] Applying intelligent column selection for table: autobooks_Bluebeam_data
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: id = 100
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: serial_number = 95
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: name = 100
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: contract = 100
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: product_name = 100
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: quantity = 89
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: end_date = 93
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: order_shipping_city = 93
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: order_shipping_state_province = 99
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:166] Column relevance score: order_shipping_country = 95
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: id (score: 100)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: name (score: 100)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: contract (score: 100)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: order_shipping_state_province (score: 99)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:184] Showing column: serial_number (score: 95)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:181] Hiding column: order_shipping_country (score: 95, visible_count: 8)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:181] Hiding column: end_date (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:181] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:181] Hiding column: order_shipping_city (score: 93, visible_count: 8)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:181] Hiding column: quantity (score: 89, visible_count: 8)
[data_table_generator] [2025-08-07 13:28:21] [data_table_generator.class.php:190] Intelligent column selection complete. Showing 8 columns, hiding 7
