[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:241] Table autobooks_Bluebeam1514_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/05/2026 -> 295 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/05/2026 -> 289 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/05/2026 -> 288 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/05/2026 -> 287 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/05/2026 -> 286 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/05/2026 -> 284 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/05/2026 -> 281 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/05/2026 -> 279 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/05/2026 -> 270 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/05/2026 -> 267 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/04/2026 -> 265 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/04/2026 -> 265 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/04/2026 -> 264 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/04/2026 -> 263 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/04/2026 -> 263 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/04/2026 -> 260 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/04/2026 -> 260 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/04/2026 -> 260 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/04/2026 -> 259 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/04/2026 -> 258 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/04/2026 -> 257 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/04/2026 -> 250 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/04/2026 -> 249 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/04/2026 -> 249 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/04/2026 -> 249 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 11/04/2026 -> 247 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/04/2026 -> 246 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/04/2026 -> 246 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/04/2026 -> 245 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/04/2026 -> 244 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/04/2026 -> 244 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/04/2026 -> 244 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/04/2026 -> 244 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/04/2026 -> 243 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/04/2026 -> 243 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/04/2026 -> 242 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/04/2026 -> 240 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/04/2026 -> 240 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/04/2026 -> 239 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/04/2026 -> 239 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/04/2026 -> 239 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/04/2026 -> 239 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/04/2026 -> 238 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/03/2026 -> 232 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/03/2026 -> 232 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/03/2026 -> 232 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/03/2026 -> 232 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/03/2026 -> 231 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/03/2026 -> 231 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/03/2026 -> 231 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/03/2026 -> 231 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/03/2026 -> 230 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/03/2026 -> 229 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/03/2026 -> 228 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/03/2026 -> 225 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/03/2026 -> 224 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/03/2026 -> 224 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/03/2026 -> 224 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/03/2026 -> 224 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/03/2026 -> 223 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/03/2026 -> 223 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/03/2026 -> 223 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/03/2026 -> 222 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/03/2026 -> 222 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/03/2026 -> 221 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/03/2026 -> 218 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/03/2026 -> 218 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/03/2026 -> 218 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/03/2026 -> 217 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/03/2026 -> 217 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/03/2026 -> 217 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/03/2026 -> 217 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 11/03/2026 -> 216 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/03/2026 -> 215 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/03/2026 -> 209 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/03/2026 -> 209 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/03/2026 -> 209 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/03/2026 -> 209 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/02/2026 -> 205 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/02/2026 -> 204 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/02/2026 -> 204 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/02/2026 -> 204 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/02/2026 -> 204 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/02/2026 -> 203 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/02/2026 -> 202 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/02/2026 -> 201 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/02/2026 -> 201 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/02/2026 -> 200 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/02/2026 -> 197 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/02/2026 -> 196 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/02/2026 -> 196 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/02/2026 -> 193 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/02/2026 -> 193 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/02/2026 -> 191 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/02/2026 -> 191 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/02/2026 -> 191 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/02/2026 -> 190 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/02/2026 -> 189 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/02/2026 -> 189 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 11/02/2026 -> 188 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 11/02/2026 -> 188 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/02/2026 -> 187 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/02/2026 -> 184 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/02/2026 -> 184 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/02/2026 -> 184 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/02/2026 -> 182 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/02/2026 -> 179 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/02/2026 -> 179 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 31/01/2026 -> 177 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 30/01/2026 -> 176 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 30/01/2026 -> 176 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/01/2026 -> 175 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/01/2026 -> 174 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/01/2026 -> 174 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/01/2026 -> 174 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/01/2026 -> 174 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/01/2026 -> 174 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/01/2026 -> 173 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/01/2026 -> 173 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/01/2026 -> 172 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/01/2026 -> 172 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/01/2026 -> 170 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/01/2026 -> 169 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/01/2026 -> 168 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/01/2026 -> 164 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/01/2026 -> 161 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/01/2026 -> 161 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/01/2026 -> 158 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/01/2026 -> 158 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/01/2026 -> 155 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/01/2026 -> 155 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/01/2026 -> 154 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/01/2026 -> 153 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/01/2026 -> 153 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/01/2026 -> 152 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/01/2026 -> 148 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/01/2026 -> 147 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/12/2025 -> 132 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/12/2025 -> 132 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/12/2025 -> 131 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/12/2025 -> 130 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/12/2025 -> 130 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/12/2025 -> 128 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/12/2025 -> 125 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/12/2025 -> 125 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/12/2025 -> 123 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/12/2025 -> 120 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/12/2025 -> 120 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/12/2025 -> 119 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/12/2025 -> 119 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/12/2025 -> 119 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/12/2025 -> 118 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/12/2025 -> 118 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/12/2025 -> 118 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/12/2025 -> 118 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/12/2025 -> 118 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/12/2025 -> 117 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/12/2025 -> 116 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/12/2025 -> 116 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 30/11/2025 -> 115 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/11/2025 -> 113 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/11/2025 -> 113 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/11/2025 -> 113 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/11/2025 -> 113 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/11/2025 -> 113 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/11/2025 -> 112 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/11/2025 -> 112 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/11/2025 -> 110 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/11/2025 -> 110 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/11/2025 -> 109 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/11/2025 -> 106 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/11/2025 -> 106 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/11/2025 -> 106 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/11/2025 -> 106 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/11/2025 -> 105 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/11/2025 -> 104 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/11/2025 -> 103 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/11/2025 -> 102 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/11/2025 -> 102 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/11/2025 -> 101 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/11/2025 -> 100 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/11/2025 -> 99 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/11/2025 -> 99 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/11/2025 -> 98 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/11/2025 -> 98 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/11/2025 -> 97 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/11/2025 -> 97 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 11/11/2025 -> 96 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/11/2025 -> 95 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/11/2025 -> 92 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/11/2025 -> 89 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/11/2025 -> 89 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/11/2025 -> 88 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/11/2025 -> 88 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 30/10/2025 -> 84 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/10/2025 -> 83 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/10/2025 -> 81 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 27/10/2025 -> 81 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/10/2025 -> 79 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/10/2025 -> 79 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/10/2025 -> 79 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/10/2025 -> 79 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/10/2025 -> 78 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/10/2025 -> 78 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/10/2025 -> 76 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/10/2025 -> 76 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/10/2025 -> 76 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/10/2025 -> 75 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/10/2025 -> 75 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/10/2025 -> 75 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/10/2025 -> 75 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/10/2025 -> 74 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/10/2025 -> 74 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/10/2025 -> 74 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/10/2025 -> 70 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/10/2025 -> 70 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/10/2025 -> 69 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/10/2025 -> 69 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/10/2025 -> 68 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/10/2025 -> 68 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/10/2025 -> 68 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/10/2025 -> 64 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/10/2025 -> 64 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/10/2025 -> 63 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/10/2025 -> 63 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/10/2025 -> 61 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/10/2025 -> 60 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/10/2025 -> 60 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/10/2025 -> 59 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/10/2025 -> 56 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/10/2025 -> 56 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/10/2025 -> 55 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/09/2025 -> 53 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/09/2025 -> 53 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/09/2025 -> 53 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/09/2025 -> 53 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/09/2025 -> 50 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/09/2025 -> 50 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/09/2025 -> 50 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/09/2025 -> 48 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/09/2025 -> 48 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/09/2025 -> 48 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/09/2025 -> 42 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/09/2025 -> 42 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 18/09/2025 -> 42 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/09/2025 -> 38 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 11/09/2025 -> 35 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 10/09/2025 -> 34 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/09/2025 -> 33 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/09/2025 -> 32 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/09/2025 -> 32 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/09/2025 -> 30 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/09/2025 -> 29 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/09/2025 -> 28 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/09/2025 -> 28 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/09/2025 -> 28 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/09/2025 -> 25 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 29/08/2025 -> 22 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/08/2025 -> 21 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/08/2025 -> 18 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 25/08/2025 -> 18 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/08/2025 -> 16 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/08/2025 -> 15 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 21/08/2025 -> 14 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/08/2025 -> 13 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/08/2025 -> 13 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 16/08/2025 -> 9 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 15/08/2025 -> 8 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/08/2025 -> -2 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/08/2025 -> -3 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/08/2025 -> -3 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/08/2025 -> -5 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 30/07/2025 -> -8 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 28/07/2025 -> -10 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/07/2025 -> -12 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/07/2025 -> -14 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 24/07/2025 -> -14 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/07/2025 -> -15 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/07/2025 -> -15 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 22/07/2025 -> -16 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/07/2025 -> -21 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/07/2025 -> -21 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/07/2025 -> -30 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/07/2025 -> -30 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/07/2025 -> -30 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/07/2025 -> -31 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/07/2025 -> -32 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 05/07/2025 -> -33 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 04/07/2025 -> -34 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 02/07/2025 -> -36 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 01/07/2025 -> -37 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/06/2025 -> -42 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 26/06/2025 -> -42 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/06/2025 -> -48 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/06/2025 -> -48 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 20/06/2025 -> -48 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 17/06/2025 -> -51 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/06/2025 -> -54 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 14/06/2025 -> -54 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 13/06/2025 -> -55 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/06/2025 -> -56 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/06/2025 -> -60 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 07/06/2025 -> -61 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 06/06/2025 -> -62 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 03/06/2025 -> -65 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 23/05/2025 -> -76 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/05/2025 -> -80 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/05/2025 -> -80 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/05/2025 -> -80 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 19/05/2025 -> -80 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 12/05/2025 -> -87 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/05/2025 -> -90 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 09/05/2025 -> -90 days remaining
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:125] Normalizing entry from autobooks_Bluebeam1514_data with 15 fields
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:138] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-07 21:12:45] [unified_field_mapper.class.php:178] Date processing: 08/05/2025 -> -91 days remaining
