[alias_debug] [2025-08-01 10:31:02] [data_sources.api.php:1591] Array\n(\n    [function] => column_selection_fragment\n    [joins_raw] => [{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.endCustomer_csn","right_table":"autodesk_accounts","right_column":"autodesk_accounts.account_csn","left_alias":"","right_alias":"endcust"},{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.soldTo_csn","right_table":"autodesk_accounts","right_column":"autodesk_accounts.account_csn","left_alias":"","right_alias":"soldto"},{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.solutionProvider_csn","right_table":"autodesk_accounts","right_column":"autodesk_accounts.account_csn","left_alias":"","right_alias":"solpro"},{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.nurtureReseller_csn","right_table":"autodesk_accounts","right_column":"autodesk_accounts.account_csn","left_alias":"","right_alias":"resell"}]\n    [joins_parsed] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [selected_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n)\n
[alias_debug] [2025-08-01 10:31:02] [data_sources.api.php:1612] Array\n(\n    [function] => column_selection_fragment\n    [table_aliases_map] => Array\n        (\n            [autodesk_accounts] => Array\n                (\n                    [0] => endcust\n                    [1] => soldto\n                    [2] => solpro\n                    [3] => resell\n                )\n\n        )\n\n)\n
