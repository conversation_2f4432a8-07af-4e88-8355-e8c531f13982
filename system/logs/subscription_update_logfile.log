[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 00:11:38
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bfdb7c49-174b-422a-958f-7ee3a4ac2d9e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75368953253895\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-30T23:51:33.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T00:11:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 00:11:38] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75368953253895', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75368953253895', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 06:11:35
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => db372f0d-9d84-46a7-8064-0c9c4fe9ea48\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75127504067843\n            [status] => Expired\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-31T05:56:30.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T06:11:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 06:11:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75127504067843', status = 'Expired' ON DUPLICATE KEY UPDATE subscriptionId = '75127504067843', status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 07:39:26
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d04eb8d8-8e9a-4576-9620-be64539ec333\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62826288139585\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-07-31T07:09:20.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T07:39:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 07:39:26] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62826288139585', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '62826288139585', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 09:07:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 09:07:37
[subscription_update] [2025-07-31 09:07:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 09:07:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 18731ac7-447a-43e7-92fa-43bc39806820\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56927469919167\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-07-31T08:52:32.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T09:07:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 09:07:37] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-07-31 09:07:41] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 56927469919167\n            [subscriptionReferenceNumber] => 566-85727259\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 2\n            [status] => Active\n            [startDate] => 2019-09-25\n            [endDate] => 2026-07-16\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => OFF\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => JONATHAN LEES ARCHITECTS LLP\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => JONATHAN LEES ARCHITECTS LLP\n                            [type] => End Customer\n                            [address1] => Unit D Baptist Mills Court\n                            [address2] => \n                            [address3] => \n                            [city] => Bristol\n                            [stateProvince] => \n                            [postalCode] => BS5 0FJ\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => Rebecca\n                            [primaryAdminLastName] => Blackwell\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8222214\n                            [teamName] => Jonathan Lees Architects - 2214\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Rebecca\n                            [last] => Blackwell\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-07-31 09:07:41] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 09:08:10
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 08ebb49c-b7d4-4189-87e5-7c678c74c992\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73633647978810\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-07-31T08:53:05.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T09:08:08.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 09:08:10] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73633647978810', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73633647978810', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 12:07:18
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2d531938-2193-45af-b90b-81ab32be7cd2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56805113260413\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-08-08\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-31T11:32:14.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T12:07:16.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 12:07:18] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56805113260413', status = 'Active', quantity = 2, endDate = '2026-08-08', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56805113260413', status = 'Active', quantity = 2, endDate = '2026-08-08', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 14:41:01
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e38939a5-fde4-421c-9174-c4e1138200d4\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69175115173686\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-10\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-31T14:20:53.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T14:40:58.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 14:41:01] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69175115173686', status = 'Active', quantity = 1, endDate = '2026-08-10', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69175115173686', status = 'Active', quantity = 1, endDate = '2026-08-10', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 15:11:22
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b868f6f7-7803-4801-a842-cc404ffc176f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69529918880411\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-31T14:56:17.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T15:11:19.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 15:11:22] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69529918880411', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69529918880411', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 15:37:43
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 99f07402-197c-4a84-bfa9-197edfeb77c2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56805113260413\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-31T15:07:33.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T15:37:40.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 15:37:43] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56805113260413', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '56805113260413', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 15:39:47
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ec7f9ec1-bdb8-4747-989d-b14532cb30a6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69175115173686\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-31T15:09:36.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T15:39:45.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 15:39:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69175115173686', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69175115173686', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 16:37:44
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7e7e45da-7484-4f0b-abca-56ddc1773e1b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68190330941508\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-07-31T16:12:39.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T16:37:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 16:37:44] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68190330941508', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '68190330941508', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-31 19:39:09
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 282e46ef-5b39-481a-aa7a-9da6e1f9d79d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74169544390614\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-07-31T19:03:21.000+0000\n        )\n\n    [publishedAt] => 2025-07-31T19:39:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-31 19:39:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74169544390614', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '74169544390614', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 00:06:57
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => cc44b3a3-d8ad-4783-82c2-7a3cb7601534\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75014667058610\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-31T23:41:52.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T00:06:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75014667058610', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75014667058610', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 00:06:57
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d812da0c-4abc-46e8-b97e-53e3dff0ffd8\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75006472719688\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-31T23:41:53.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T00:06:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 00:06:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75006472719688', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75006472719688', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 00:10:43
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 060da2fe-4b35-45df-89de-2689f8789eb9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68915687453814\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-31T23:40:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T00:10:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 00:10:43] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68915687453814', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '68915687453814', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 00:11:22
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 693fd46b-e840-43a0-906a-07673c2ee718\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71887756008038\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-31T23:41:16.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T00:11:20.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 00:11:22] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71887756008038', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '71887756008038', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 00:11:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 00:11:23
[subscription_update] [2025-08-01 00:11:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 00:11:23] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ab003ae8-9d5c-414b-b8be-bc8583b9c63f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55508693518307\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-31T23:41:18.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T00:11:21.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 00:11:23] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-01 00:11:26] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55508693518307\n            [subscriptionReferenceNumber] => 562-96510504\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Suspended\n            [startDate] => 2019-04-14\n            [endDate] => 2026-07-03\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => CHEMOXY\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => CHEMOXY\n                            [type] => End Customer\n                            [address1] => Haverton Hill Road\n                            [address2] => Road\n                            [address3] => \n                            [city] => Billingham\n                            [stateProvince] => CLEVELAND\n                            [postalCode] => TS23 1PY\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => MFG\n                            [parentIndustrySegment] => Process Manufacturing\n                            [primaryAdminFirstName] => Peter\n                            [primaryAdminLastName] => McArthur\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7050254\n                            [teamName] => David Pearson - 0254\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Peter\n                            [last] => McArthur\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-01 00:11:26] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 06:06:07
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e8bded2b-47c7-40b3-b648-897f955044fa\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75136903143720\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-01T05:36:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T06:06:05.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 06:06:07] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75136903143720', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75136903143720', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 07:09:21
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 43c52483-8b52-4119-97d0-767fc09578ba\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69148544128710\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T06:54:16.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T07:09:19.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 07:09:21] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69148544128710', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69148544128710', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 07:10:54
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e973a56c-8d80-4f93-92d5-e9f2a9209f67\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71932610877074\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-01T06:45:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T07:10:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 07:10:54] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71932610877074', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71932610877074', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 07:12:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 07:12:45
[subscription_update] [2025-08-01 07:12:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 07:12:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7d7ef65c-8f56-42d9-b5b7-0784afad5bbb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56942318578860\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T06:57:39.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T07:12:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 07:12:45] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-01 07:12:50] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 56942318578860\n            [subscriptionReferenceNumber] => 564-51915786\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 2\n            [status] => Active\n            [startDate] => 2019-09-27\n            [endDate] => 2026-07-02\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => TRITON CONSTRUCTION Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => TRITON CONSTRUCTION Ltd\n                            [type] => End Customer\n                            [address1] => Hare Park Mills Hare Park Lane\n                            [address2] => \n                            [address3] => \n                            [city] => Liversedge\n                            [stateProvince] => WEST YORKSHIRE\n                            [postalCode] => WF15 8EP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Roger\n                            [primaryAdminLastName] => Swift\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8242108\n                            [teamName] => Roger Swift - 2108\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Graig\n                            [last] => Newsome\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-01 07:12:50] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 09:07:19
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 891a94cd-e56b-4816-ad16-65cfeeb8836d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 57615947812131\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-01T08:52:12.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T09:07:15.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 09:07:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '57615947812131', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '57615947812131', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 11:42:15
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 93891827-9790-4ac2-86c2-6a2e50bff3a1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56700593929570\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-08-27\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-01T11:27:09.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T11:42:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 11:42:15] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56700593929570', status = 'Active', quantity = 1, endDate = '2028-08-27', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '56700593929570', status = 'Active', quantity = 1, endDate = '2028-08-27', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 11:42:28
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5ea6d16a-a5b8-4269-bba5-6a90c6875d66\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56716427656767\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-08-29\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-01T11:27:23.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T11:42:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 11:42:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56716427656767', status = 'Active', quantity = 1, endDate = '2028-08-29', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '56716427656767', status = 'Active', quantity = 1, endDate = '2028-08-29', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 12:44:18
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 413e7f3f-28ad-46b9-a110-221ee27d690c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59861408333647\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-27\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-01T12:29:13.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T12:44:15.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 12:44:18] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59861408333647', status = 'Active', quantity = 1, endDate = '2026-08-27', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59861408333647', status = 'Active', quantity = 1, endDate = '2026-08-27', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 13:38:07
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1a900fa6-d4f9-4dd8-9811-9040b9352a3d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56577398248377\n            [status] => Active\n            [quantity] => 4\n            [endDate] => 2026-08-13\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-01T13:18:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T13:38:05.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 13:38:07] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56577398248377', status = 'Active', quantity = 4, endDate = '2026-08-13', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56577398248377', status = 'Active', quantity = 4, endDate = '2026-08-13', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 15:36:46
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0dc8b960-aed2-473f-a9b9-3a19b728e1bb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59861408333647\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T15:06:20.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T15:36:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 15:36:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59861408333647', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59861408333647', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 15:40:50
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1a38d6a0-8ce6-4d9b-839f-cb8171444e49\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56577398248377\n            [quantity] => 4\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T15:05:41.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T15:40:47.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 15:40:50] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56577398248377', quantity = 4 ON DUPLICATE KEY UPDATE subscriptionId = '56577398248377', quantity = 4;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 15:40:59
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 44f5ccba-42ae-40ca-ad04-01bafaac1fcf\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56700593929570\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T15:05:51.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T15:40:57.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 15:40:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56700593929570', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56700593929570', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 15:41:06
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7c9c1c9d-4bf5-4e24-83d5-314272282cef\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56716427656767\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T15:05:51.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T15:41:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 15:41:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56716427656767', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56716427656767', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 18:10:10
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 039a06f6-f2eb-46ed-aa5b-790b836f6c47\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56533871230169\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-08-08\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-01T17:40:05.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T18:10:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 18:10:10] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56533871230169', status = 'Active', quantity = 1, endDate = '2028-08-08', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '56533871230169', status = 'Active', quantity = 1, endDate = '2028-08-08', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-01 19:36:35
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e1f17dda-42e9-4d4b-965a-f2431cdbdcbc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56533871230169\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-01T19:11:23.000+0000\n        )\n\n    [publishedAt] => 2025-08-01T19:36:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-01 19:36:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56533871230169', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56533871230169', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 00:06:31
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 161fd91d-83aa-4913-a8dc-38b43adc4e06\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72258911056117\n            [status] => Expired\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-01T23:51:26.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T00:06:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 00:06:31] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72258911056117', status = 'Expired' ON DUPLICATE KEY UPDATE subscriptionId = '72258911056117', status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 06:08:32
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ac355e98-d9fe-45b9-8d03-05640123652d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65701794230087\n            [status] => Expired\n            [quantity] => 4\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-02T05:43:26.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T06:08:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 06:08:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65701794230087', status = 'Expired', quantity = 4 ON DUPLICATE KEY UPDATE subscriptionId = '65701794230087', status = 'Expired', quantity = 4;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 06:10:23
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f7f74bb1-fe85-4da2-a107-0e202a2a3890\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75146432676914\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-02T05:40:19.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T06:10:21.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 06:10:23] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75146432676914', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75146432676914', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 07:08:06
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 161d92f1-9789-43d4-8dad-389dd7f798ee\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65701794230087\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-02T06:48:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T07:08:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 07:08:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65701794230087', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '65701794230087', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 07:08:27
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9653edff-236d-4334-a931-cc4f89b4afdd\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72018003980494\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-02T06:43:23.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T07:08:25.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 07:08:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72018003980494', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '72018003980494', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 07:11:01
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e9f64d5f-a065-4164-83cc-ba5e9e21e6b5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72258911056117\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-02T06:50:56.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T07:10:58.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 07:11:01] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72258911056117', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '72258911056117', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-02 15:13:43
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d8e0a480-f63d-4c6a-861a-2e3252176196\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72864095179347\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-02T14:58:37.000+0000\n        )\n\n    [publishedAt] => 2025-08-02T15:13:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-02 15:13:43] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72864095179347', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '72864095179347', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 06:08:51
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 66129f82-23e8-4535-b506-c8717133f47b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56018211103443\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-03T05:38:44.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T06:08:48.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 06:08:51] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56018211103443', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56018211103443', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 07:46:13
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => aeee4c20-9d4e-415a-aa25-d3e22fe03fe3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63170833754989\n            [quantity] => 1\n            [endDate] => 2026-09-14\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-03T07:31:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T07:46:10.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 07:46:13] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63170833754989', quantity = 1, endDate = '2026-09-14' ON DUPLICATE KEY UPDATE subscriptionId = '63170833754989', quantity = 1, endDate = '2026-09-14';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 11:39:30
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 26bf65db-ac9b-4696-952a-cf86193f66b5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63170833754989\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-03T11:19:20.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T11:39:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 11:39:30] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63170833754989', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63170833754989', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 12:08:21
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 403bc316-0ffd-4e90-99f9-b93604026cc3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72648778789850\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-03T11:48:15.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T12:08:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 12:08:21] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72648778789850', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '72648778789850', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 12:10:51
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 535ed5d5-b57a-45fb-b998-60db33585206\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75006472719688\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-03T11:40:44.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T12:10:48.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75006472719688', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '75006472719688', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 12:10:51
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 976fd034-c4c1-40f0-be19-eb45e0deacb2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68915687453814\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-03T11:40:46.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T12:10:48.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 12:10:51] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68915687453814', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '68915687453814', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 12:10:52
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 91603999-96bc-4b5f-ba96-75e1d3567c25\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71887756008038\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-03T11:40:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T12:10:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-03 12:10:52] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71887756008038', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71887756008038', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-03 12:10:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-03 12:10:53
[subscription_update] [2025-08-03 12:10:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-03 12:10:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 29929270-d4a0-4b4f-8eaf-75ec42aaacac\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55508693518307\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-03T11:40:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-03T12:10:51.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-03 12:10:53] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-03 12:10:58] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55508693518307\n            [subscriptionReferenceNumber] => 562-96510504\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-04-14\n            [endDate] => 2026-07-03\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => CHEMOXY\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => CHEMOXY\n                            [type] => End Customer\n                            [address1] => Haverton Hill Road\n                            [address2] => Road\n                            [address3] => \n                            [city] => Billingham\n                            [stateProvince] => CLEVELAND\n                            [postalCode] => TS23 1PY\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => MFG\n                            [parentIndustrySegment] => Process Manufacturing\n                            [primaryAdminFirstName] => Peter\n                            [primaryAdminLastName] => McArthur\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7050254\n                            [teamName] => David Pearson - 0254\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Peter\n                            [last] => McArthur\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-03 12:10:58] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 06:10:36
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 79df6998-019d-45e6-a39f-435e9f815600\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62521806254194\n            [status] => Expired\n            [quantity] => 2\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-08-04T05:35:32.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T06:10:34.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 06:10:36] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62521806254194', status = 'Expired', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '62521806254194', status = 'Expired', quantity = 2;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 07:05:51
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d1842ee9-5739-4335-93ac-a9c215173213\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56440318707446\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-28\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T06:35:46.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T07:05:48.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 07:05:51] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56440318707446', status = 'Active', quantity = 1, endDate = '2026-07-28', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56440318707446', status = 'Active', quantity = 1, endDate = '2026-07-28', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 07:38:19
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => fa85c01d-bb87-4eca-b3ea-625da333a8d5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56440318707446\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T07:08:11.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T07:38:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 07:38:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56440318707446', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56440318707446', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 08:45:59
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 73242d1c-3e14-4de8-9358-99ba502cc779\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66126167207160\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-01\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T08:30:54.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T08:45:56.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 08:45:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66126167207160', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '66126167207160', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 09:10:09
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 56d1aab8-f732-4f51-94cf-53ec2de64753\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55595157491612\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-04T08:55:05.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T09:10:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 09:10:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55595157491612', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '55595157491612', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 09:43:19
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b564ca54-3c7b-4b8c-be9c-8914c9d8f331\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 54341697443157\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-04T09:28:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T09:43:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 09:43:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '54341697443157', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '54341697443157', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 10:09:35
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => efe5ced5-2222-4a8a-911a-b74ad53700ba\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56743625158902\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-01\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T09:39:30.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T10:09:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 10:09:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56743625158902', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56743625158902', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 11:13:06
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3176b2c6-ebf6-45d1-966d-761bfa650ff2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69381769773020\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-03\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T10:53:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T11:13:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 11:13:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69381769773020', status = 'Active', quantity = 1, endDate = '2026-09-03', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69381769773020', status = 'Active', quantity = 1, endDate = '2026-09-03', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 11:37:40
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5f6787f5-f539-497c-8507-6e4342f897f5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56743625158902\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T11:10:55.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T11:37:38.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 11:37:40] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56743625158902', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56743625158902', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 11:44:39
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ec14cf25-a591-4efe-af21-4c71da8e55f4\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66126167207160\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T11:13:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T11:44:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 11:44:39] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66126167207160', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66126167207160', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 11:48:59
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d08ee09f-86fc-48b8-8638-cad2a146fd59\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69381769773020\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T11:13:55.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T11:48:57.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 11:48:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69381769773020', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69381769773020', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-04 11:53:12] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 11:53:12
[subscription_update] [2025-08-04 11:53:12] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 11:53:12] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => df080430-ea86-4ec7-bb7c-d2bdc811d0ae\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75429970383863\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T11:17:33.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T11:53:10.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 11:53:12] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-04 11:53:16] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75429970383863\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-04\n            [endDate] => 2025-12-14\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => jba Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => jba Ltd\n                            [type] => End Customer\n                            [address1] => Unit 17 Evolution Wynyard Avenue\n                            [address2] => Wynyard\n                            [address3] => \n                            [city] => Billingham\n                            [stateProvince] => CLEVELAND\n                            [postalCode] => TS22 5TB\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Paul\n                            [primaryAdminLastName] => Cockrill\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8120819\n                            [teamName] => Paul Cockrill - 0819\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Paul\n                            [last] => Cockrill\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-04 11:53:16] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 16:11:52
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => eca43743-e2fe-4b37-99fd-c476bffad8ed\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 70169625842721\n            [quantity] => 1\n            [autoRenew] => ON\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-04T15:56:47.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T16:11:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 16:11:52] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '70169625842721', quantity = 1, autoRenew = 'ON' ON DUPLICATE KEY UPDATE subscriptionId = '70169625842721', quantity = 1, autoRenew = 'ON';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 16:37:36
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 16b6e9db-6427-4f8a-b07b-56808b5d362c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65969915309711\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-04\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T16:07:32.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T16:37:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 16:37:36] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65969915309711', status = 'Active', quantity = 1, endDate = '2026-08-04', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65969915309711', status = 'Active', quantity = 1, endDate = '2026-08-04', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 16:41:18
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d065fc45-4449-4322-a072-6cc85262f7bd\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56987383140626\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-09\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T16:11:13.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T16:41:15.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 16:41:18] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56987383140626', status = 'Active', quantity = 1, endDate = '2026-07-09', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56987383140626', status = 'Active', quantity = 1, endDate = '2026-07-09', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 19:37:38
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5580ab76-777c-4e4b-9892-a767f790e529\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62851504013355\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-04T19:12:33.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T19:37:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 19:37:38] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62851504013355', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '62851504013355', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 19:37:39
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b15a52df-c258-4d2b-8fd9-d8dc73a48917\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62851791696292\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-04T19:12:34.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T19:37:36.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 19:37:39] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62851791696292', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '62851791696292', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 19:40:21
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6f2c4316-fe5d-4a16-8a7a-4bab273af78d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56987383140626\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T19:10:09.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T19:40:19.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 19:40:21] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56987383140626', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56987383140626', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 19:41:23
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 59cb8147-3a5b-4ab5-b058-20030752973c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65969915309711\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T19:11:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T19:41:20.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 19:41:23] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65969915309711', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65969915309711', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 19:42:21
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c8e5bbe0-75df-4d1b-8f27-aed238e87314\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63091949302189\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-05\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-04T19:27:16.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T19:42:19.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 19:42:21] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63091949302189', status = 'Active', quantity = 1, endDate = '2026-09-05', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '63091949302189', status = 'Active', quantity = 1, endDate = '2026-09-05', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-04 23:40:16
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => eeea9e82-eb2d-4686-9a3c-c98b5fd5ddf1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63091949302189\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-04T23:05:09.000+0000\n        )\n\n    [publishedAt] => 2025-08-04T23:40:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-04 23:40:16] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63091949302189', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63091949302189', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 07:09:24
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bedcf3ce-1944-4920-8d3d-7770eecb3a01\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75337738787712\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T06:44:19.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T07:09:22.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 07:09:24] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75337738787712', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75337738787712', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 07:09:30
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 44ae5ec2-c8a2-411d-9d9d-c6457ceec0f6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75146432676914\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-05T06:44:25.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T07:09:28.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 07:09:30] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75146432676914', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '75146432676914', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 07:09:42
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 94393bb5-0e0e-4bfd-92a2-f09ea91a61c4\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75136903143720\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-05T06:54:38.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T07:09:40.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 07:09:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75136903143720', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '75136903143720', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 07:37:35
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5cc5ad86-b425-474f-b54b-fd4ed816854d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75205764633108\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T07:02:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T07:37:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 07:37:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75205764633108', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75205764633108', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 09:05:12
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f283a654-06fa-42e8-b3ff-031ebf38d68f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66065226907420\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-08-15\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-05T08:35:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T09:05:10.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 09:05:12] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66065226907420', status = 'Active', quantity = 1, endDate = '2028-08-15', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '66065226907420', status = 'Active', quantity = 1, endDate = '2028-08-15', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 09:39:41
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 02186d2e-161c-4d79-8dd2-b9b5d11ff236\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62867699489049\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-10\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-05T09:09:36.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T09:39:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 09:39:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62867699489049', status = 'Active', quantity = 1, endDate = '2026-08-10', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62867699489049', status = 'Active', quantity = 1, endDate = '2026-08-10', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 09:40:15
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ece7561f-4ac1-4f8f-872c-96bbc6efdfb2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55554012159617\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-05T09:05:10.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T09:40:13.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 09:40:15] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55554012159617', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '55554012159617', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 11:39:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 11:39:14
[subscription_update] [2025-08-05 11:39:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 11:39:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 09295e4a-984b-45fb-b347-113891241575\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75438658522256\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T11:24:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T11:39:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 11:39:14] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-05 11:39:19] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75438658522256\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-05\n            [endDate] => 2026-08-04\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000027\n            [offeringCode] => ACDIST\n            [offeringName] => AutoCAD - including specialized toolsets\n            [marketingName] => AutoCAD - including specialized toolsets\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Thermal Earth Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Thermal Earth Ltd\n                            [type] => End Customer\n                            [address1] => Unit B1 Capel Hendre Ind Est\n                            [address2] => \n                            [address3] => \n                            [city] => Ammanford\n                            [stateProvince] => \n                            [postalCode] => SA18 3SJ\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => MFG\n                            [parentIndustrySegment] => Industrial Machinery\n                            [primaryAdminFirstName] => Thermal\n                            [primaryAdminLastName] => Earth\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Thermal Earth - 3483\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Thermal\n                            [last] => Earth\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-05 11:39:19] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 11:39:58
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b2595929-d620-4429-80e8-2027cb302c37\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62867699489049\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T11:19:50.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T11:39:56.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 11:39:58] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62867699489049', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62867699489049', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 11:40:29
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 615e75a4-3595-4f8a-a180-dd5f89ceadbe\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66065226907420\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T11:20:20.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T11:40:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 11:40:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66065226907420', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66065226907420', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 12:10:48
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4ab71e6f-94f7-4f0a-b6e9-a00d26ca1ad7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995599209008\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-07\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-05T11:50:43.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T12:10:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 12:10:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995599209008', status = 'Active', quantity = 1, endDate = '2026-08-07', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65995599209008', status = 'Active', quantity = 1, endDate = '2026-08-07', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 12:37:25
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0f16795c-dce9-4846-bf1c-d8766167f4ea\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55787120285135\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-08-08\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-05T12:12:09.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T12:37:22.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 12:37:25] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55787120285135', status = 'Active', quantity = 2, endDate = '2026-08-08', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '55787120285135', status = 'Active', quantity = 2, endDate = '2026-08-08', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 13:42:12
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 14017d1a-bf59-4395-8b91-82d0ccc4fdbb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69054087093103\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-05T13:07:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T13:42:09.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 13:42:12] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69054087093103', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '69054087093103', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 14:38:13
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 56697628-648a-457f-a7aa-b40002a170f8\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56805113260413\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T14:13:08.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T14:38:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 14:38:13] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56805113260413', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '56805113260413', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:07:34
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 510782e8-efd1-4e0e-a561-700b60b1b32c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68510614450605\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-05T14:47:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:07:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:07:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68510614450605', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '68510614450605', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:07:35
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 960682ac-7f86-4bf6-b3b0-de903be54db6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 64198805055631\n            [quantity] => 3\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-05T14:47:30.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:07:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:07:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '64198805055631', quantity = 3, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '64198805055631', quantity = 3, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:10:40
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 039e920c-280b-4886-b4e4-649e6a44904d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72359337050211\n            [status] => Active\n            [quantity] => 5\n            [endDate] => 2026-08-12\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-05T14:55:35.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:10:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:10:40] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72359337050211', status = 'Active', quantity = 5, endDate = '2026-08-12', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72359337050211', status = 'Active', quantity = 5, endDate = '2026-08-12', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:36:46
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 38e703cd-0cce-479f-9952-427884025bd9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995599209008\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T15:11:36.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:36:43.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:36:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995599209008', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65995599209008', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:37:40
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bec2d0bb-3b4c-405e-b2cc-8f13dcb4ca2c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995907157636\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-07\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-05T15:22:34.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:37:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:37:40] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995907157636', status = 'Active', quantity = 1, endDate = '2026-08-07', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65995907157636', status = 'Active', quantity = 1, endDate = '2026-08-07', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:38:19
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8242eeed-15b3-44be-8aee-a30b1a8d267e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72359337050211\n            [quantity] => 5\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T15:13:10.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:38:17.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:38:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72359337050211', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '72359337050211', quantity = 5;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:39:18
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 005d5619-3e1d-4712-956c-0fd6a776b76b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55787120285135\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T15:09:09.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:39:16.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:39:18] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55787120285135', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '55787120285135', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 15:41:19
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b06671fb-005d-4e4f-b601-f1bfa33f6aa9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72359337050211\n            [quantity] => 5\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T15:26:14.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T15:41:16.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 15:41:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72359337050211', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '72359337050211', quantity = 5;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 16:37:27
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 35c10583-4d92-476a-a70e-72f7c5a28541\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55553297758399\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-05T16:22:22.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T16:37:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 16:37:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55553297758399', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '55553297758399', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-05 19:39:42
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 70294a09-8bb9-4cc9-b1b2-46335e9837eb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995907157636\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-05T19:14:34.000+0000\n        )\n\n    [publishedAt] => 2025-08-05T19:39:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-05 19:39:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995907157636', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65995907157636', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 07:10:47
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 47800476-58b3-44a0-bc66-784c027bb93f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74110746359805\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-06T06:45:42.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T07:10:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 07:10:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74110746359805', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '74110746359805', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 08:39:11
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2152a6b9-96e8-438a-9e79-d51ab0477d6f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995913110554\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-07\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,term changed.\n            [modifiedAt] => 2025-08-06T08:09:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T08:39:08.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 08:39:11] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995913110554', status = 'Active', quantity = 1, endDate = '2026-08-07', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65995913110554', status = 'Active', quantity = 1, endDate = '2026-08-07', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 08:40:56
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b9ed0d8b-52fd-48b6-ba66-79027f9a22ec\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995913110554\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-08-06T08:15:51.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T08:40:53.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 08:40:56] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995913110554', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '65995913110554', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 09:38:52
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6f433523-20e5-42cd-9ea7-ae02154b04cb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527238809770\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-01\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-06T09:03:46.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T09:38:49.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 09:38:52] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527238809770', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72527238809770', status = 'Active', quantity = 1, endDate = '2026-09-01', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 11:37:05
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8fa2163f-b159-4780-8114-2874957e85b9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65995913110554\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-06T11:11:54.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T11:37:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 11:37:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65995913110554', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65995913110554', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 11:38:33
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 96184633-a33f-4af5-abc1-e3d81404b848\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527238809770\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-06T11:13:24.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T11:38:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 11:38:33] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527238809770', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72527238809770', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 11:43:57
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b4e4f7a9-461d-42e8-bba5-3aa0c4e3e98a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73583050280801\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-06T11:28:53.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T11:43:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 11:43:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73583050280801', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73583050280801', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 15:37:56
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 69225417-0b5c-49ea-afb4-f9253f4e7b81\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62945623792724\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-19\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-06T15:02:42.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T15:37:53.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 15:37:56] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62945623792724', status = 'Active', quantity = 1, endDate = '2026-08-19', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62945623792724', status = 'Active', quantity = 1, endDate = '2026-08-19', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-06 19:39:55
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => cc8fd7a6-43fe-4c13-9d54-34e98cbd1c31\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62945623792724\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-06T19:14:41.000+0000\n        )\n\n    [publishedAt] => 2025-08-06T19:39:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-06 19:39:55] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62945623792724', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62945623792724', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-07 09:07:50
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6771bda3-064e-4f8a-aa90-55b08e27654e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65468891106219\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-07T08:47:42.000+0000\n        )\n\n    [publishedAt] => 2025-08-07T09:07:48.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-07 09:07:50] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65468891106219', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '65468891106219', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-07 09:08:11
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => baeb6884-3a20-463c-8696-4c92c455e840\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72527238809770\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-08-07T08:48:06.000+0000\n        )\n\n    [publishedAt] => 2025-08-07T09:08:09.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-07 09:08:11] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72527238809770', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '72527238809770', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
