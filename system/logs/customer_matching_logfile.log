[customer_matching] [2025-08-08 08:58:31] [customers.api.php:160] Customer matching - Found 478 CSV entries to check
[customer_matching] [2025-08-08 08:58:31] [customers.api.php:161] Customer data: email=none, name=none
[customer_matching] [2025-08-08 08:58:31] [customers.api.php:169] CSV entries by source table: {"autobooks_sketchup_data":158,"autobooks_bluebeam_data":317,"manual_entries":3}
[customer_matching] [2025-08-08 08:58:33] [customers.api.php:245] Final result: Found 0 matching CSV subscriptions
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:160] Customer matching - Found 478 CSV entries to check
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:161] Customer data: email=<EMAIL>, name=BSBA TEES Ltd
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:169] CSV entries by source table: {"autobooks_sketchup_data":158,"autobooks_bluebeam_data":317,"manual_entries":3}
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba tees' (similarity: 100%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:227] COMPANY MATCH FOUND! Field: endcust_name, Confidence: 100%
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:241] Added matched entry: {"match_type":"company","confidence":100}
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dragonfly signs and display' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dragonfly signs and display' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dragonfly signs and display' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='david aldrich designs' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='david aldrich designs' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='david aldrich designs' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='quality by design' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='quality by design' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='quality by design' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mab design and development' (similarity: 22.857142857143%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mab design and development' (similarity: 22.857142857143%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mab design and development' (similarity: 22.857142857143%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='elena rowland' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='elena rowland' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='elena rowland' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='new british design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='new british design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='new british design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='estimating design services' (similarity: 11.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='estimating design services' (similarity: 11.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='estimating design services' (similarity: 11.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mad' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mad' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mad' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='richard bastow' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='richard bastow' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='richard bastow' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='the kennels cottage copgrove' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='the kennels cottage copgrove' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='the kennels cottage copgrove' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='open door project' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='open door project' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='open door project' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sculparc' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sculparc' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sculparc' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='north cumbria search and rescue' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='north cumbria search and rescue' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='north cumbria search and rescue' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='nina howden' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='nina howden' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='nina howden' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acbuckley architectural' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acbuckley architectural' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acbuckley architectural' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='parish of st helier' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='parish of st helier' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='parish of st helier' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ssp financing uk' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ssp financing uk' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ssp financing uk' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hardman structural engineers' (similarity: 32.432432432432%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hardman structural engineers' (similarity: 32.432432432432%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hardman structural engineers' (similarity: 32.432432432432%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='benjamin tindall architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='benjamin tindall architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='benjamin tindall architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='high rowantree' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='high rowantree' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='high rowantree' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='yorkshire cancer research' (similarity: 29.411764705882%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='yorkshire cancer research' (similarity: 29.411764705882%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='yorkshire cancer research' (similarity: 29.411764705882%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='marcon fitout' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='marcon fitout' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='marcon fitout' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='shell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='shell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='shell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dragon tv film studios wales' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dragon tv film studios wales' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dragon tv film studios wales' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rosie wilkins landscape design' (similarity: 25.641025641026%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rosie wilkins landscape design' (similarity: 25.641025641026%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rosie wilkins landscape design' (similarity: 25.641025641026%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mark webb consult' (similarity: 23.076923076923%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mark webb consult' (similarity: 23.076923076923%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mark webb consult' (similarity: 23.076923076923%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='shape the space architects' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='shape the space architects' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='shape the space architects' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='studio montagu' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='studio montagu' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='studio montagu' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sheffield teaching hospitals nhs found' (similarity: 21.276595744681%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sheffield teaching hospitals nhs found' (similarity: 21.276595744681%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sheffield teaching hospitals nhs found' (similarity: 21.276595744681%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='inside outside engineering' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='inside outside engineering' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='inside outside engineering' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='charlotte fausset architects' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='charlotte fausset architects' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='charlotte fausset architects' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='delta q consulting' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='delta q consulting' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='delta q consulting' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='treesaurus' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='treesaurus' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='treesaurus' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='james mackintosh architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='james mackintosh architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='james mackintosh architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='day cummins' (similarity: 10%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='day cummins' (similarity: 10%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='day cummins' (similarity: 10%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dlg architects leeds' (similarity: 34.48275862069%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dlg architects leeds' (similarity: 34.48275862069%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dlg architects leeds' (similarity: 34.48275862069%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='di oro interiors' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='di oro interiors' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='di oro interiors' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba tees' (similarity: 100%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:227] COMPANY MATCH FOUND! Field: endcust_name, Confidence: 100%
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:241] Added matched entry: {"match_type":"company","confidence":100}
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dragonfly signs and display' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dragonfly signs and display' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dragonfly signs and display' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='david aldrich designs' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='david aldrich designs' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='david aldrich designs' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='spectrum workplace llp' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='quality by design' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='quality by design' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='quality by design' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mab design and development' (similarity: 22.857142857143%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mab design and development' (similarity: 22.857142857143%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mab design and development' (similarity: 22.857142857143%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='elena rowland' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='elena rowland' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='elena rowland' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='new british design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='new british design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='new british design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='estimating design services' (similarity: 11.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='estimating design services' (similarity: 11.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='estimating design services' (similarity: 11.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mad' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mad' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mad' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='richard bastow' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='richard bastow' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='richard bastow' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='the kennels cottage copgrove' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='the kennels cottage copgrove' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='the kennels cottage copgrove' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='open door project' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='open door project' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='open door project' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sculparc' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sculparc' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sculparc' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='north cumbria search and rescue' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='north cumbria search and rescue' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='north cumbria search and rescue' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='nina howden' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='nina howden' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='nina howden' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acbuckley architectural' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acbuckley architectural' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acbuckley architectural' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='parish of st helier' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='parish of st helier' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='parish of st helier' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ssp financing uk' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ssp financing uk' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ssp financing uk' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hardman structural engineers' (similarity: 32.432432432432%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hardman structural engineers' (similarity: 32.432432432432%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hardman structural engineers' (similarity: 32.432432432432%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='benjamin tindall architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='benjamin tindall architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='benjamin tindall architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='high rowantree' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='high rowantree' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='high rowantree' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='yorkshire cancer research' (similarity: 29.411764705882%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='yorkshire cancer research' (similarity: 29.411764705882%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='yorkshire cancer research' (similarity: 29.411764705882%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='marcon fitout' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='marcon fitout' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='marcon fitout' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='shell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='shell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='shell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dragon tv film studios wales' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dragon tv film studios wales' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dragon tv film studios wales' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rosie wilkins landscape design' (similarity: 25.641025641026%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rosie wilkins landscape design' (similarity: 25.641025641026%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rosie wilkins landscape design' (similarity: 25.641025641026%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mark webb consult' (similarity: 23.076923076923%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mark webb consult' (similarity: 23.076923076923%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mark webb consult' (similarity: 23.076923076923%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='planstudio architecture' (similarity: 31.25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='shape the space architects' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='shape the space architects' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='shape the space architects' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='studio montagu' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='studio montagu' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='studio montagu' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sheffield teaching hospitals nhs found' (similarity: 21.276595744681%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sheffield teaching hospitals nhs found' (similarity: 21.276595744681%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sheffield teaching hospitals nhs found' (similarity: 21.276595744681%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='inside outside engineering' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='inside outside engineering' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='inside outside engineering' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alexander waterworth interiors' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='charlotte fausset architects' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='charlotte fausset architects' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='charlotte fausset architects' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='datacom cabling' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='delta q consulting' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='delta q consulting' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='delta q consulting' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='treesaurus' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='treesaurus' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='treesaurus' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='james mackintosh architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='james mackintosh architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='james mackintosh architects' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='day cummins' (similarity: 10%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='day cummins' (similarity: 10%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='day cummins' (similarity: 10%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dlg architects leeds' (similarity: 34.48275862069%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dlg architects leeds' (similarity: 34.48275862069%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dlg architects leeds' (similarity: 34.48275862069%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='di oro interiors' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='di oro interiors' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='di oro interiors' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acorn partitions storage systems' (similarity: 29.268292682927%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sensus architecture' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sensus architecture' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sensus architecture' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sapphire spaces' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sapphire spaces' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sapphire spaces' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='homes under the hannah' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='homes under the hannah' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='homes under the hannah' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='designs to build on' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='designs to build on' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='designs to build on' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='craig sheppard' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='craig sheppard' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='craig sheppard' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='innoscape' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='innoscape' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='innoscape' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='philip bingham associates' (similarity: 35.294117647059%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='philip bingham associates' (similarity: 35.294117647059%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='philip bingham associates' (similarity: 35.294117647059%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='matt barwell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='matt barwell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='matt barwell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dandelion seeds architects' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dandelion seeds architects' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dandelion seeds architects' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='joel trindade designs' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='joel trindade designs' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='joel trindade designs' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='russell jones' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='russell jones' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='russell jones' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='securebase' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='securebase' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='securebase' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='soda' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sensus architecture' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sensus architecture' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sensus architecture' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:37] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mjct' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sapphire spaces' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sapphire spaces' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sapphire spaces' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='homes under the hannah' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='homes under the hannah' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='homes under the hannah' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='designs to build on' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='designs to build on' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='designs to build on' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='craig sheppard' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='craig sheppard' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='craig sheppard' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='innoscape' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='innoscape' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='innoscape' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='philip bingham associates' (similarity: 35.294117647059%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='philip bingham associates' (similarity: 35.294117647059%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='philip bingham associates' (similarity: 35.294117647059%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='matt barwell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='matt barwell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='matt barwell' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dandelion seeds architects' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dandelion seeds architects' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dandelion seeds architects' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='joel trindade designs' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='joel trindade designs' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='joel trindade designs' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='russell jones' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='russell jones' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='russell jones' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='securebase' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='securebase' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='securebase' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba tees' (similarity: 100%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:227] COMPANY MATCH FOUND! Field: endcust_name, Confidence: 100%
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:241] Added matched entry: {"match_type":"company","confidence":100}
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bridgegate electrical' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bridgegate electrical' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bridgegate electrical' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='heat space and light' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='heat space and light' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='heat space and light' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='berts brickwork' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='berts brickwork' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='berts brickwork' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jng surveys' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jng surveys' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jng surveys' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mida civil engineering' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mida civil engineering' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mida civil engineering' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rhonda goodman' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rhonda goodman' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rhonda goodman' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='stortford interiors' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='stortford interiors' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='stortford interiors' (similarity: 35.714285714286%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sw building contractors' (similarity: 12.5%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sw building contractors' (similarity: 12.5%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sw building contractors' (similarity: 12.5%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='woodhurst construction' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='woodhurst construction' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='woodhurst construction' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dc flooring uk' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dc flooring uk' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dc flooring uk' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='unitherm heating systems' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='unitherm heating systems' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='unitherm heating systems' (similarity: 24.242424242424%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='naba designs' (similarity: 47.619047619048%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='naba designs' (similarity: 47.619047619048%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='naba designs' (similarity: 47.619047619048%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='quinn piling' (similarity: 9.5238095238095%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='quinn piling' (similarity: 9.5238095238095%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='quinn piling' (similarity: 9.5238095238095%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mila property group' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mila property group' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mila property group' (similarity: 21.428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='kelf civil engineering' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='kelf civil engineering' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='kelf civil engineering' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='pba solutions' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='pba solutions' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='pba solutions' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='a hammond sons' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='a hammond sons' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='a hammond sons' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='xp property' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='xp property' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='xp property' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='consilium consulting engineers' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='consilium consulting engineers' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='consilium consulting engineers' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='randburg electrical services' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='randburg electrical services' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='randburg electrical services' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='randburg electrical services' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='randburg electrical services' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='randburg electrical services' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='regency facades' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='regency facades' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='regency facades' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='247 group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='247 group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='247 group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='247 group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='247 group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='247 group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cem auty' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cem auty' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cem auty' (similarity: 23.529411764706%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='swanton consulting' (similarity: 29.62962962963%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mts' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mts' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mts' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rochbry projects' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rochbry projects' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rochbry projects' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='evans construction solutions' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='evans construction solutions' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='evans construction solutions' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='skidmores of hertford' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='skidmores of hertford' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='skidmores of hertford' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hunter partners' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hunter partners' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hunter partners' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='beckett rankine' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='beckett rankine' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='beckett rankine' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='beckett rankine' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='beckett rankine' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='beckett rankine' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sr surveying services' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sr surveying services' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sr surveying services' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cd engineering' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cd engineering' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cd engineering' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='planet partitioning south division' (similarity: 13.953488372093%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='planet partitioning south division' (similarity: 13.953488372093%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='planet partitioning south division' (similarity: 13.953488372093%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='interpro technology solutions' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='interpro technology solutions' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='interpro technology solutions' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='the banks group' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='the banks group' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='the banks group' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='the banks group' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='the banks group' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='the banks group' (similarity: 25%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mts' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mts' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mts' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='multicore' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='multicore' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='multicore' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='flash point design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='flash point design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='flash point design' (similarity: 37.037037037037%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cmics' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cmics' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cmics' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='isometrix lighting and design' (similarity: 26.315789473684%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='adrian gates' (similarity: 47.619047619048%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='adrian gates' (similarity: 47.619047619048%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='adrian gates' (similarity: 47.619047619048%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mida civil engineering' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mida civil engineering' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mida civil engineering' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ram building group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ram building group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ram building group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='247 fm' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='247 fm' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='247 fm' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='winchmore brickwork' (similarity: 7.1428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='winchmore brickwork' (similarity: 7.1428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='winchmore brickwork' (similarity: 7.1428571428571%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='solihull council' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='solihull council' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='solihull council' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='nephin consulting' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='nephin consulting' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='nephin consulting' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='kaybridge construction' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='kaybridge construction' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='kaybridge construction' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='renaissance associates' (similarity: 38.709677419355%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='demcom' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='demcom' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='demcom' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='chapel properties woodbridge' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='chapel properties woodbridge' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='chapel properties woodbridge' (similarity: 27.027027027027%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='enhanced solutions' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='enhanced solutions' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='enhanced solutions' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='tercon' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='tercon' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='tercon' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dna south west' (similarity: 43.478260869565%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dna south west' (similarity: 43.478260869565%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dna south west' (similarity: 43.478260869565%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='leclardo consulting' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='leclardo consulting' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='leclardo consulting' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='me7' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='me7' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='me7' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='castree' (similarity: 50%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='castree' (similarity: 50%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='castree' (similarity: 50%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='alufix facades' (similarity: 34.782608695652%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sweet structures' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sweet structures' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sweet structures' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sweet structures' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sweet structures' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sweet structures' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='arc engineers' (similarity: 45.454545454545%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='intelligent it' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='intelligent it' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='intelligent it' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='tercon' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='tercon' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='tercon' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bks groundworks' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bks groundworks' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bks groundworks' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='martin ralph group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='martin ralph group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='martin ralph group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='martin ralph group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='martin ralph group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='martin ralph group' (similarity: 14.814814814815%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:38] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='pls civil engineering' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='pls civil engineering' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='pls civil engineering' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='highall developments' (similarity: 6.8965517241379%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='highall developments' (similarity: 6.8965517241379%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='highall developments' (similarity: 6.8965517241379%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bks groundworks' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bks groundworks' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bks groundworks' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='leeda construction services' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='leeda construction services' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='leeda construction services' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='tprime' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='tprime' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='tprime' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bellendaine' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bellendaine' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bellendaine' (similarity: 30%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='loftus engineering services' (similarity: 27.777*********%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='loftus engineering services' (similarity: 27.777*********%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='loftus engineering services' (similarity: 27.777*********%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='adept consulting engineers' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='adept consulting engineers' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='adept consulting engineers' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rowlands civil and construction services' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rowlands civil and construction services' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rowlands civil and construction services' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='carmen flooring' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='carmen flooring' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='carmen flooring' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vida design' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='mucklow harris' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='mucklow harris' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='mucklow harris' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dc flooring uk' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dc flooring uk' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dc flooring uk' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='nexus phoenix consulting engineers' (similarity: 27.906976744186%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='nexus phoenix consulting engineers' (similarity: 27.906976744186%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='nexus phoenix consulting engineers' (similarity: 27.906976744186%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='s voase builders' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='s voase builders' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='s voase builders' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='park electrical distributors' (similarity: 10.810810810811%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='craft interiors' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='craft interiors' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='craft interiors' (similarity: 41.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='osl consulting engineers' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='michael brady' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='artium construction' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='dublin airport airside safety' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='dublin airport airside safety' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='dublin airport airside safety' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hgce' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hgce' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hgce' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hughes bros construction' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hughes bros construction' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hughes bros construction' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='taft consultants' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='taft consultants' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='taft consultants' (similarity: 32%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='nic' (similarity: 0%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='nic' (similarity: 0%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='nic' (similarity: 0%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='environmental economics' (similarity: 6.25%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='environmental economics' (similarity: 6.25%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='environmental economics' (similarity: 6.25%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rapid response telecoms' (similarity: 37.5%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rapid response telecoms' (similarity: 37.5%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rapid response telecoms' (similarity: 37.5%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='acs construction group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='acs construction group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='acs construction group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='247 fm' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='247 fm' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='247 fm' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='firmingers llp' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='firmingers llp' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='firmingers llp' (similarity: 17.391304347826%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='oscar mep' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='oscar mep' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='oscar mep' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bradley associates' (similarity: 44.444444444444%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='modular garden' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='modular garden' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='modular garden' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='create engineering llp' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='create engineering llp' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='create engineering llp' (similarity: 25.806451612903%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hughes bros construction' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hughes bros construction' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hughes bros construction' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='grk civils' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='paramount structures' (similarity: 27.586206896552%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='radii planet group' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ross planning' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ross planning' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ross planning' (similarity: 18.181818181818%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='studio moren' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='copeland borough council' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='copeland borough council' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='copeland borough council' (similarity: 12.121212121212%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='ej lazenby contactors' (similarity: 13.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='erith' (similarity: 14.285714285714%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jones hargreaves' (similarity: 16%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='quinn piling' (similarity: 9.5238095238095%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='quinn piling' (similarity: 9.5238095238095%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='quinn piling' (similarity: 9.5238095238095%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='a hammond sons' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='a hammond sons' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='a hammond sons' (similarity: 26.086956521739%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='premier group' (similarity: 9.0909090909091%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='premier group' (similarity: 9.0909090909091%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='premier group' (similarity: 9.0909090909091%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rwo group' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='etude' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hgce' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hgce' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hgce' (similarity: 15.384615384615%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='k home international' (similarity: 20.689655172414%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='k home international' (similarity: 20.689655172414%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='k home international' (similarity: 20.689655172414%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='gm developments' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='adf london' (similarity: 21.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hr wallingford' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hr wallingford' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hr wallingford' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='hr wallingford' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='hr wallingford' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='hr wallingford' (similarity: 8.695652173913%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='jj sweeney' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='root group' (similarity: 10.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='planet partitioning south division' (similarity: 13.953488372093%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='planet partitioning south division' (similarity: 13.953488372093%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='planet partitioning south division' (similarity: 13.953488372093%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='countrywide ceilings partitions' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='countrywide ceilings partitions' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='countrywide ceilings partitions' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='arcade tiling' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='arcade tiling' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='arcade tiling' (similarity: 27.272727272727%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='pls civil engineering' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='pls civil engineering' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='pls civil engineering' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bailiss co' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bailiss co' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='bailiss co' (similarity: 31.578947368421%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='keldin engineering' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='keldin engineering' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='keldin engineering' (similarity: 22.222222222222%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aden contracting' (similarity: 24%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='aura homes' (similarity: 42.105263157895%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='countrywide ceilings partitions' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='countrywide ceilings partitions' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='countrywide ceilings partitions' (similarity: 20%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='metway electrical industries' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='metway electrical industries' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='metway electrical industries' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='metway electrical industries' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='metway electrical industries' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='metway electrical industries' (similarity: 21.621621621622%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='fabric building physics' (similarity: 18.75%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='fabric building physics' (similarity: 18.75%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='fabric building physics' (similarity: 18.75%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='tprime' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='tprime' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='tprime' (similarity: 26.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='vke contractors' (similarity: 8.************3%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='sawcon construction services' (similarity: 37.837837837838%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='sawcon construction services' (similarity: 37.837837837838%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sawcon construction services' (similarity: 37.837837837838%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='cooper homewood' (similarity: 16.666666666667%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='lionweld kennedy group' (similarity: 19.354838709677%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='edmlondon' (similarity: 11.111111111111%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rock internet' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rock internet' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rock internet' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='rock internet' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='rock internet' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='rock internet' (similarity: 36.363636363636%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[endcust_name]='big c engineering' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='big c engineering' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='big c engineering' (similarity: 30.769230769231%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:190] Comparing emails: customer='<EMAIL>' vs entry[email_address]='<EMAIL>'
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='test company' (similarity: 28.571428571429%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:190] Comparing emails: customer='<EMAIL>' vs entry[email_address]='<EMAIL>'
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='sample' (similarity: 40%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:190] Comparing emails: customer='<EMAIL>' vs entry[email_address]='<EMAIL>'
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:220] Comparing companies: customer='bsba tees' vs entry[company_name]='demo industries' (similarity: 33.************%)
[customer_matching] [2025-08-08 08:58:39] [customers.api.php:245] Final result: Found 3 matching CSV subscriptions
