[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for id
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: subscription_id, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_id
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_id
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for subscription_id: {"original_name":"subscription_id","suggested_name":"subscription_id","confidence":73.5,"reasoning":"High confidence match for 'subscription_id' (score: 73.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"subscription_id","confidence":95},"data_analysis":{"type":"subscription_id","confidence":90,"pattern_matches":{"subscription_id":{"score":90,"matches":10,"total":10,"percentage":1}},"sample_size":10},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"subscription_id":73.5}}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for subscription_id (confidence: 73.5%, suggested: subscription_id)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for subscription_id
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: customer_name
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: customer_name
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for customer_name: {"original_name":"customer_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":81,"matches":9,"total":10,"percentage":0.9},"business_keywords":{"score":85,"matches":10,"total":10,"percentage":1,"type":"company_name"}},"sample_size":10},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:240] Intelligent naming applied: customer_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: software_product_name, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: software_product_name
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: software_product_name
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for software_product_name: {"original_name":"software_product_name","suggested_name":"software_product_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for software_product_name (confidence: 0%, suggested: software_product_name)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for software_product_name
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: license_type, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: license_type
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: license_type
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for license_type: {"original_name":"license_type","suggested_name":"product_name","confidence":32,"reasoning":"Medium confidence match for 'product_name' (score: 32)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"product_name","confidence":64,"pattern_matches":{"subscription_id":{"score":62.99999999999999,"matches":7,"total":10,"percentage":0.7},"software_keywords":{"score":64,"matches":8,"total":10,"percentage":0.8,"type":"product_name"}},"sample_size":10},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":32}}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:240] Intelligent naming applied: license_type -> product_name (confidence: 32)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: start_date, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: start_date
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: start_date
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for start_date: {"original_name":"start_date","suggested_name":"start_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for start_date (confidence: 0%, suggested: start_date)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for start_date
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for end_date
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: number_of_licenses_seats, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: number_of_licenses_seats
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: number_of_licenses_seats
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for number_of_licenses_seats: {"original_name":"number_of_licenses_seats","suggested_name":"number_of_licenses_seats","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for number_of_licenses_seats (confidence: 0%, suggested: number_of_licenses_seats)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for number_of_licenses_seats
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: price_annual_fee, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: price_annual_fee
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: price_annual_fee
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for price_annual_fee: {"original_name":"price_annual_fee","suggested_name":"price_annual_fee","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:244] No intelligent naming applied for price_annual_fee (confidence: 0%, suggested: price_annual_fee)
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:255] Using standard formatting for price_annual_fee
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:158] Generator debug - column: status, predefined_label: null, use_intelligent: 1, table: autobooks_test0955_data
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: status
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:223] get_intelligent_column_label called for: status
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:230] Analysis result for status: {"original_name":"status","suggested_name":"status_values","confidence":36,"reasoning":"Medium confidence match for 'status_values' (score: 36)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"status","confidence":95},"data_analysis":{"type":"status_values","confidence":72,"pattern_matches":{"status_values":{"score":72,"matches":9,"total":10,"percentage":0.9}},"sample_size":10},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"status":28.5,"status_values":36}}}
[column_analyzer] [2025-08-07 08:55:59] [data_table_generator.class.php:240] Intelligent naming applied: status -> status_values (confidence: 36)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:244] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:255] Using standard formatting for id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:244] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:255] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: sold_to_number, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: sold_to_number
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: sold_to_number
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for sold_to_number: {"original_name":"sold_to_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:240] Intelligent naming applied: sold_to_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:244] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:255] Using standard formatting for vendor_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:240] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:244] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:255] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:230] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:240] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:158] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 08:57:34] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_address_3, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_address_3
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_address_3
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_address_3: {"original_name":"end_customer_address_3","suggested_name":"end_customer_address_3","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_address_3 (confidence: 0%, suggested: end_customer_address_3)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_address_3
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_account_type, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_account_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_account_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_account_type: {"original_name":"end_customer_account_type","suggested_name":"end_customer_account_type","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_account_type (confidence: 0%, suggested: end_customer_account_type)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_account_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_contact_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":27.625,"reasoning":"Low confidence - added context prefix (score: 27.625)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":55.25,"pattern_matches":{"email":{"score":95,"matches":20,"total":20,"percentage":1},"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":55.25,"matches":13,"total":20,"percentage":0.65,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"uk_locations":{"score":37.5,"matches":15,"total":20,"percentage":0.75,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":27.625}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_contact_email (confidence: 27.625%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_contact_phone, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_contact_phone
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_contact_phone
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_contact_phone: {"original_name":"end_customer_contact_phone","suggested_name":"end_customer_contact_phone","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_contact_phone (confidence: 0%, suggested: end_customer_contact_phone)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_contact_phone
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: end_customer_industry_segment, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_industry_segment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_industry_segment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for end_customer_industry_segment: {"original_name":"end_customer_industry_segment","suggested_name":"end_customer_industry_segment","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_industry_segment (confidence: 0%, suggested: end_customer_industry_segment)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for end_customer_industry_segment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_program_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_program_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_program_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_program_name: {"original_name":"agreement_program_name","suggested_name":"product_name","confidence":40,"reasoning":"Medium confidence match for 'product_name' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"product_name","confidence":80,"pattern_matches":{"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":40}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: agreement_program_name -> product_name (confidence: 40)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_number, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_number: {"original_name":"agreement_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: agreement_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_start_date, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_start_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_start_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_start_date: {"original_name":"agreement_start_date","suggested_name":"agreement_start_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_start_date (confidence: 0%, suggested: agreement_start_date)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_start_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_end_date, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_end_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_end_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_end_date: {"original_name":"agreement_end_date","suggested_name":"agreement_end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_end_date (confidence: 0%, suggested: agreement_end_date)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_end_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_terms, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_terms
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_terms
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_terms: {"original_name":"agreement_terms","suggested_name":"agreement_terms","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_terms (confidence: 0%, suggested: agreement_terms)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_terms
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_type, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_type: {"original_name":"agreement_type","suggested_name":"agreement_type","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_type (confidence: 0%, suggested: agreement_type)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_status, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_status: {"original_name":"agreement_status","suggested_name":"status_values","confidence":40,"reasoning":"Medium confidence match for 'status_values' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"status_values","confidence":80,"pattern_matches":{"status_values":{"score":80,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"status_values":40}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: agreement_status -> status_values (confidence: 40)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_support_level, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_support_level
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_support_level
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_support_level: {"original_name":"agreement_support_level","suggested_name":"agreement_support_level","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_support_level (confidence: 0%, suggested: agreement_support_level)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_support_level
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_days_due, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_days_due
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_days_due
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_days_due: {"original_name":"agreement_days_due","suggested_name":"agreement_days_due","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_days_due (confidence: 0%, suggested: agreement_days_due)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_days_due
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: agreement_autorenew, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_autorenew
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_autorenew
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for agreement_autorenew: {"original_name":"agreement_autorenew","suggested_name":"agreement_autorenew","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for agreement_autorenew (confidence: 0%, suggested: agreement_autorenew)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for agreement_autorenew
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_family, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_family
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_family
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_family: {"original_name":"product_family","suggested_name":"product_family","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_family (confidence: 0%, suggested: product_family)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_family
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_market_segment, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_market_segment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_market_segment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_market_segment: {"original_name":"product_market_segment","suggested_name":"product_market_segment","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_market_segment (confidence: 0%, suggested: product_market_segment)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_market_segment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_release, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_release
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_release
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_release: {"original_name":"product_release","suggested_name":"product_release","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_release (confidence: 0%, suggested: product_release)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_release
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_type, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_type: {"original_name":"product_type","suggested_name":"product_name","confidence":40,"reasoning":"Medium confidence match for 'product_name' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"product_name","confidence":80,"pattern_matches":{"subscription_id":{"score":90,"matches":20,"total":20,"percentage":1},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":40}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: product_type -> product_name (confidence: 40)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_deployment, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_deployment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_deployment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_deployment: {"original_name":"product_deployment","suggested_name":"product_deployment","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_deployment (confidence: 0%, suggested: product_deployment)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_deployment
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_sku, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_sku
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_sku
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_sku: {"original_name":"product_sku","suggested_name":"product_sku","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_sku (confidence: 0%, suggested: product_sku)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_sku
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_sku_description, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_sku_description
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_sku_description
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_sku_description: {"original_name":"product_sku_description","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":4.5,"matches":1,"total":20,"percentage":0.05},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: product_sku_description -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_part, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_part
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_part
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_part: {"original_name":"product_part","suggested_name":"product_part","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_part (confidence: 0%, suggested: product_part)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_part
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_list_price, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_list_price
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_list_price
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_list_price: {"original_name":"product_list_price","suggested_name":"product_list_price","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_list_price (confidence: 0%, suggested: product_list_price)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_list_price
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: product_list_price_currency, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_list_price_currency
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_list_price_currency
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for product_list_price_currency: {"original_name":"product_list_price_currency","suggested_name":"autobooks_product_list_price_currency","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for product_list_price_currency (confidence: 25%, suggested: autobooks_product_list_price_currency)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for product_list_price_currency
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_id: {"original_name":"subscription_id","suggested_name":"autobooks_subscription_id","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"subscription_id","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"subscription_id":28.5}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_id (confidence: 28.5%, suggested: autobooks_subscription_id)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_serial_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_serial_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_serial_number: {"original_name":"subscription_serial_number","suggested_name":"subscription_serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_serial_number (confidence: 0%, suggested: subscription_serial_number)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_serial_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_status, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_status: {"original_name":"subscription_status","suggested_name":"status_values","confidence":40,"reasoning":"Medium confidence match for 'status_values' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"status_values","confidence":80,"pattern_matches":{"status_values":{"score":80,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"status_values":40}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: subscription_status -> status_values (confidence: 40)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_quantity, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_quantity
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_quantity
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_quantity: {"original_name":"subscription_quantity","suggested_name":"subscription_quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_quantity (confidence: 0%, suggested: subscription_quantity)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_quantity
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_start_date, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_start_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_start_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_start_date: {"original_name":"subscription_start_date","suggested_name":"subscription_start_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_start_date (confidence: 0%, suggested: subscription_start_date)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_start_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_end_date, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_end_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_end_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_end_date: {"original_name":"subscription_end_date","suggested_name":"subscription_end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_end_date (confidence: 0%, suggested: subscription_end_date)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_end_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_contact_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_contact_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_contact_name: {"original_name":"subscription_contact_name","suggested_name":"subscription_contact_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_contact_name (confidence: 0%, suggested: subscription_contact_name)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_contact_name
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_contact_email
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_contact_email
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_contact_email: {"original_name":"subscription_contact_email","suggested_name":"subscription_contact_email","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_contact_email (confidence: 0%, suggested: subscription_contact_email)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_contact_email
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_level, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_level
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_level
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_level: {"original_name":"subscription_level","suggested_name":"subscription_level","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_level (confidence: 0%, suggested: subscription_level)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_level
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: subscription_days_due, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_days_due
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_days_due
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for subscription_days_due: {"original_name":"subscription_days_due","suggested_name":"subscription_days_due","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for subscription_days_due (confidence: 0%, suggested: subscription_days_due)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for subscription_days_due
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_id: {"original_name":"quotation_id","suggested_name":"quotation_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for quotation_id (confidence: 0%, suggested: quotation_id)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for quotation_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_type, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_type: {"original_name":"quotation_type","suggested_name":"quotation_type","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for quotation_type (confidence: 0%, suggested: quotation_type)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for quotation_type
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_vendor_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_vendor_id
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_vendor_id: {"original_name":"quotation_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:240] Intelligent naming applied: quotation_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_deal_registration_number, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_deal_registration_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_deal_registration_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_deal_registration_number: {"original_name":"quotation_deal_registration_number","suggested_name":"quotation_deal_registration_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for quotation_deal_registration_number (confidence: 0%, suggested: quotation_deal_registration_number)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for quotation_deal_registration_number
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_status, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_status: {"original_name":"quotation_status","suggested_name":"quotation_status","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"Too many null values"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for quotation_status (confidence: 0%, suggested: quotation_status)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for quotation_status
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_resellerpo_previous, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_resellerpo_previous
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_resellerpo_previous
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_resellerpo_previous: {"original_name":"quotation_resellerpo_previous","suggested_name":"quotation_resellerpo_previous","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for quotation_resellerpo_previous (confidence: 0%, suggested: quotation_resellerpo_previous)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for quotation_resellerpo_previous
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: quotation_due_date, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_due_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_due_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for quotation_due_date: {"original_name":"quotation_due_date","suggested_name":"quotation_due_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for quotation_due_date (confidence: 0%, suggested: quotation_due_date)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for quotation_due_date
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: flaer_phase, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: flaer_phase
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: flaer_phase
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for flaer_phase: {"original_name":"flaer_phase","suggested_name":"flaer_phase","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for flaer_phase (confidence: 0%, suggested: flaer_phase)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for flaer_phase
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:158] Generator debug - column: updated, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup957_data
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: updated
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:223] get_intelligent_column_label called for: updated
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:230] Analysis result for updated: {"original_name":"updated","suggested_name":"updated","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:244] No intelligent naming applied for updated (confidence: 0%, suggested: updated)
[column_analyzer] [2025-08-07 08:57:35] [data_table_generator.class.php:255] Using standard formatting for updated
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: sold_to_number, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: sold_to_number
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: sold_to_number
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for sold_to_number: {"original_name":"sold_to_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:240] Intelligent naming applied: sold_to_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: vendor_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: vendor_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: vendor_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for vendor_name: {"original_name":"vendor_name","suggested_name":"vendor_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for vendor_name (confidence: 0%, suggested: vendor_name)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for vendor_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: reseller_number, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: reseller_number
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: reseller_number
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for reseller_number: {"original_name":"reseller_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:240] Intelligent naming applied: reseller_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: reseller_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: reseller_vendor_id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: reseller_vendor_id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for reseller_vendor_id: {"original_name":"reseller_vendor_id","suggested_name":"reseller_vendor_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for reseller_vendor_id (confidence: 0%, suggested: reseller_vendor_id)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for reseller_vendor_id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:240] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:240] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: end_customer_address_1, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_address_1
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_address_1
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for end_customer_address_1: {"original_name":"end_customer_address_1","suggested_name":"end_customer_address_1","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_address_1 (confidence: 0%, suggested: end_customer_address_1)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for end_customer_address_1
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: end_customer_address_2, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_address_2
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_address_2
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for end_customer_address_2: {"original_name":"end_customer_address_2","suggested_name":"end_customer_address_2","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_address_2 (confidence: 0%, suggested: end_customer_address_2)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for end_customer_address_2
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: end_customer_address_3, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_address_3
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_address_3
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:230] Analysis result for end_customer_address_3: {"original_name":"end_customer_address_3","suggested_name":"end_customer_address_3","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_address_3 (confidence: 0%, suggested: end_customer_address_3)
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:255] Using standard formatting for end_customer_address_3
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:158] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-07 09:37:54] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_state, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_state
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_state
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_state: {"original_name":"end_customer_state","suggested_name":"end_customer_state","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_state (confidence: 0%, suggested: end_customer_state)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_state
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_account_type, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_account_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_account_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_account_type: {"original_name":"end_customer_account_type","suggested_name":"end_customer_account_type","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_account_type (confidence: 0%, suggested: end_customer_account_type)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_account_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_contact_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":27.625,"reasoning":"Low confidence - added context prefix (score: 27.625)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":55.25,"pattern_matches":{"email":{"score":95,"matches":20,"total":20,"percentage":1},"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":55.25,"matches":13,"total":20,"percentage":0.65,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"uk_locations":{"score":37.5,"matches":15,"total":20,"percentage":0.75,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":27.625}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_contact_email (confidence: 27.625%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_contact_phone, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_contact_phone
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_contact_phone
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_contact_phone: {"original_name":"end_customer_contact_phone","suggested_name":"end_customer_contact_phone","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_contact_phone (confidence: 0%, suggested: end_customer_contact_phone)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_contact_phone
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: end_customer_industry_segment, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: end_customer_industry_segment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: end_customer_industry_segment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for end_customer_industry_segment: {"original_name":"end_customer_industry_segment","suggested_name":"end_customer_industry_segment","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for end_customer_industry_segment (confidence: 0%, suggested: end_customer_industry_segment)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for end_customer_industry_segment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_program_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_program_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_program_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_program_name: {"original_name":"agreement_program_name","suggested_name":"product_name","confidence":40,"reasoning":"Medium confidence match for 'product_name' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"product_name","confidence":80,"pattern_matches":{"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":40}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: agreement_program_name -> product_name (confidence: 40)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_number, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_number: {"original_name":"agreement_number","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: agreement_number -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_start_date, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_start_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_start_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_start_date: {"original_name":"agreement_start_date","suggested_name":"agreement_start_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_start_date (confidence: 0%, suggested: agreement_start_date)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_start_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_end_date, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_end_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_end_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_end_date: {"original_name":"agreement_end_date","suggested_name":"agreement_end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_end_date (confidence: 0%, suggested: agreement_end_date)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_end_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_terms, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_terms
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_terms
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_terms: {"original_name":"agreement_terms","suggested_name":"agreement_terms","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_terms (confidence: 0%, suggested: agreement_terms)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_terms
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_type, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_type: {"original_name":"agreement_type","suggested_name":"agreement_type","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_type (confidence: 0%, suggested: agreement_type)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_status, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_status: {"original_name":"agreement_status","suggested_name":"status_values","confidence":40,"reasoning":"Medium confidence match for 'status_values' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"status_values","confidence":80,"pattern_matches":{"status_values":{"score":80,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"status_values":40}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: agreement_status -> status_values (confidence: 40)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_support_level, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_support_level
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_support_level
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_support_level: {"original_name":"agreement_support_level","suggested_name":"agreement_support_level","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_support_level (confidence: 0%, suggested: agreement_support_level)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_support_level
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_days_due, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_days_due
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_days_due
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_days_due: {"original_name":"agreement_days_due","suggested_name":"agreement_days_due","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_days_due (confidence: 0%, suggested: agreement_days_due)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_days_due
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: agreement_autorenew, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: agreement_autorenew
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: agreement_autorenew
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for agreement_autorenew: {"original_name":"agreement_autorenew","suggested_name":"agreement_autorenew","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for agreement_autorenew (confidence: 0%, suggested: agreement_autorenew)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for agreement_autorenew
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_family, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_family
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_family
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_family: {"original_name":"product_family","suggested_name":"product_family","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_family (confidence: 0%, suggested: product_family)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_family
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_market_segment, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_market_segment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_market_segment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_market_segment: {"original_name":"product_market_segment","suggested_name":"product_market_segment","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_market_segment (confidence: 0%, suggested: product_market_segment)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_market_segment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_release, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_release
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_release
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_release: {"original_name":"product_release","suggested_name":"product_release","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_release (confidence: 0%, suggested: product_release)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_release
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_type, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_type: {"original_name":"product_type","suggested_name":"product_name","confidence":40,"reasoning":"Medium confidence match for 'product_name' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"product_name","confidence":80,"pattern_matches":{"subscription_id":{"score":90,"matches":20,"total":20,"percentage":1},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":40}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: product_type -> product_name (confidence: 40)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_deployment, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_deployment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_deployment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_deployment: {"original_name":"product_deployment","suggested_name":"product_deployment","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_deployment (confidence: 0%, suggested: product_deployment)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_deployment
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_sku, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_sku
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_sku
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_sku: {"original_name":"product_sku","suggested_name":"product_sku","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_sku (confidence: 0%, suggested: product_sku)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_sku
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_sku_description, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_sku_description
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_sku_description
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_sku_description: {"original_name":"product_sku_description","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":4.5,"matches":1,"total":20,"percentage":0.05},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: product_sku_description -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_part, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_part
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_part
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_part: {"original_name":"product_part","suggested_name":"product_part","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_part (confidence: 0%, suggested: product_part)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_part
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_list_price, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_list_price
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_list_price
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_list_price: {"original_name":"product_list_price","suggested_name":"product_list_price","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_list_price (confidence: 0%, suggested: product_list_price)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_list_price
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: product_list_price_currency, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: product_list_price_currency
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: product_list_price_currency
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for product_list_price_currency: {"original_name":"product_list_price_currency","suggested_name":"autobooks_product_list_price_currency","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for product_list_price_currency (confidence: 25%, suggested: autobooks_product_list_price_currency)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for product_list_price_currency
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_id: {"original_name":"subscription_id","suggested_name":"autobooks_subscription_id","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"subscription_id","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"subscription_id":28.5}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_id (confidence: 28.5%, suggested: autobooks_subscription_id)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_serial_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_serial_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_serial_number: {"original_name":"subscription_serial_number","suggested_name":"subscription_serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_serial_number (confidence: 0%, suggested: subscription_serial_number)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_serial_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_status, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_status: {"original_name":"subscription_status","suggested_name":"status_values","confidence":40,"reasoning":"Medium confidence match for 'status_values' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"status_values","confidence":80,"pattern_matches":{"status_values":{"score":80,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"status_values":40}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: subscription_status -> status_values (confidence: 40)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_quantity, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_quantity
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_quantity
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_quantity: {"original_name":"subscription_quantity","suggested_name":"subscription_quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_quantity (confidence: 0%, suggested: subscription_quantity)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_quantity
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_start_date, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_start_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_start_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_start_date: {"original_name":"subscription_start_date","suggested_name":"subscription_start_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_start_date (confidence: 0%, suggested: subscription_start_date)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_start_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_end_date, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_end_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_end_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_end_date: {"original_name":"subscription_end_date","suggested_name":"subscription_end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_end_date (confidence: 0%, suggested: subscription_end_date)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_end_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_contact_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_contact_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_contact_name: {"original_name":"subscription_contact_name","suggested_name":"subscription_contact_name","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_contact_name (confidence: 0%, suggested: subscription_contact_name)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_contact_name
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_contact_email
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_contact_email
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_contact_email: {"original_name":"subscription_contact_email","suggested_name":"subscription_contact_email","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_contact_email (confidence: 0%, suggested: subscription_contact_email)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_contact_email
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_level, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_level
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_level
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_level: {"original_name":"subscription_level","suggested_name":"subscription_level","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_level (confidence: 0%, suggested: subscription_level)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_level
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: subscription_days_due, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: subscription_days_due
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: subscription_days_due
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for subscription_days_due: {"original_name":"subscription_days_due","suggested_name":"subscription_days_due","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for subscription_days_due (confidence: 0%, suggested: subscription_days_due)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for subscription_days_due
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_id: {"original_name":"quotation_id","suggested_name":"quotation_id","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for quotation_id (confidence: 0%, suggested: quotation_id)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for quotation_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_type, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_type: {"original_name":"quotation_type","suggested_name":"quotation_type","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for quotation_type (confidence: 0%, suggested: quotation_type)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for quotation_type
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_vendor_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_vendor_id
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_vendor_id: {"original_name":"quotation_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:240] Intelligent naming applied: quotation_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_deal_registration_number, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_deal_registration_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_deal_registration_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_deal_registration_number: {"original_name":"quotation_deal_registration_number","suggested_name":"quotation_deal_registration_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for quotation_deal_registration_number (confidence: 0%, suggested: quotation_deal_registration_number)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for quotation_deal_registration_number
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_status, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_status: {"original_name":"quotation_status","suggested_name":"quotation_status","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"Too many null values"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for quotation_status (confidence: 0%, suggested: quotation_status)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for quotation_status
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_resellerpo_previous, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_resellerpo_previous
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_resellerpo_previous
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_resellerpo_previous: {"original_name":"quotation_resellerpo_previous","suggested_name":"quotation_resellerpo_previous","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"All values are null\/empty"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for quotation_resellerpo_previous (confidence: 0%, suggested: quotation_resellerpo_previous)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for quotation_resellerpo_previous
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: quotation_due_date, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: quotation_due_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: quotation_due_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for quotation_due_date: {"original_name":"quotation_due_date","suggested_name":"quotation_due_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for quotation_due_date (confidence: 0%, suggested: quotation_due_date)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for quotation_due_date
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: flaer_phase, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: flaer_phase
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: flaer_phase
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for flaer_phase: {"original_name":"flaer_phase","suggested_name":"flaer_phase","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for flaer_phase (confidence: 0%, suggested: flaer_phase)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for flaer_phase
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:158] Generator debug - column: updated, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:162] Generator debug - calling intelligent naming for column: updated
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:223] get_intelligent_column_label called for: updated
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:227] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:230] Analysis result for updated: {"original_name":"updated","suggested_name":"updated","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:244] No intelligent naming applied for updated (confidence: 0%, suggested: updated)
[column_analyzer] [2025-08-07 09:37:55] [data_table_generator.class.php:255] Using standard formatting for updated
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:428] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:439] Using standard formatting for id
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:428] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:439] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:424] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:424] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:424] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:428] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:439] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: end_customer_contact_email, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: end_customer_contact_email
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: end_customer_contact_email
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for end_customer_contact_email: {"original_name":"end_customer_contact_email","suggested_name":"autobooks_end_customer_contact_email","confidence":27.625,"reasoning":"Low confidence - added context prefix (score: 27.625)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":55.25,"pattern_matches":{"email":{"score":95,"matches":20,"total":20,"percentage":1},"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":55.25,"matches":13,"total":20,"percentage":0.65,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"uk_locations":{"score":37.5,"matches":15,"total":20,"percentage":0.75,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":27.625}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:428] No intelligent naming applied for end_customer_contact_email (confidence: 27.625%, suggested: autobooks_end_customer_contact_email)
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:439] Using standard formatting for end_customer_contact_email
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:342] Generator debug - column: agreement_program_name, predefined_label: null, use_intelligent: 1, table: autobooks_Sketchup_data
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:346] Generator debug - calling intelligent naming for column: agreement_program_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:407] get_intelligent_column_label called for: agreement_program_name
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:411] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:414] Analysis result for agreement_program_name: {"original_name":"agreement_program_name","suggested_name":"product_name","confidence":40,"reasoning":"Medium confidence match for 'product_name' (score: 40)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"product_name","confidence":80,"pattern_matches":{"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":40}}}
[column_analyzer] [2025-08-07 09:52:20] [data_table_generator.class.php:424] Intelligent naming applied: agreement_program_name -> product_name (confidence: 40)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:472] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:483] Using standard formatting for id
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:472] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:483] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:483] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:483] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:386] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch3_data
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:458] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-07 11:27:11] [data_table_generator.class.php:483] Using standard formatting for end_customer_contact_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:472] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:483] Using standard formatting for id
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:472] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:483] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:483] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:483] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:386] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch4_data
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:458] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-07 11:33:41] [data_table_generator.class.php:483] Using standard formatting for end_customer_contact_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:472] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:483] Using standard formatting for id
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:472] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:483] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:483] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:483] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:386] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketch0708_data
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:458] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-07 11:59:57] [data_table_generator.class.php:483] Using standard formatting for end_customer_contact_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:472] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:483] Using standard formatting for id
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: sold_to_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: sold_to_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: sold_to_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for sold_to_name: {"original_name":"sold_to_name","suggested_name":"autobooks_sold_to_name","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:472] No intelligent naming applied for sold_to_name (confidence: 25%, suggested: autobooks_sold_to_name)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:483] Using standard formatting for sold_to_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: end_customer_vendor_id, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_vendor_id
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_vendor_id
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for end_customer_vendor_id: {"original_name":"end_customer_vendor_id","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_vendor_id -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: end_customer_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for end_customer_name: {"original_name":"end_customer_name","suggested_name":"company_name","confidence":34,"reasoning":"Medium confidence match for 'company_name' (score: 34)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":68,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":68,"matches":16,"total":20,"percentage":0.8,"type":"company_name"},"software_keywords":{"score":12,"matches":3,"total":20,"percentage":0.15,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":34}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_name -> company_name (confidence: 34)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: end_customer_city, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_city
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_city
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for end_customer_city: {"original_name":"end_customer_city","suggested_name":"end_customer_city","confidence":5,"reasoning":"Confidence too low to suggest changes (score: 5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":10,"pattern_matches":{"business_keywords":{"score":4.25,"matches":1,"total":20,"percentage":0.05,"type":"company_name"},"uk_locations":{"score":10,"matches":4,"total":20,"percentage":0.2,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":5}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_city (confidence: 5%, suggested: end_customer_city)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:483] Using standard formatting for end_customer_city
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: end_customer_zip_code, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_zip_code
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_zip_code
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for end_customer_zip_code: {"original_name":"end_customer_zip_code","suggested_name":"uk_postcode","confidence":47.5,"reasoning":"Medium confidence match for 'uk_postcode' (score: 47.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"uk_postcode","confidence":95,"pattern_matches":{"uk_postcode":{"score":95,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"uk_postcode":47.5}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:468] Intelligent naming applied: end_customer_zip_code -> uk_postcode (confidence: 47.5)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: end_customer_country, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_country
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_country
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for end_customer_country: {"original_name":"end_customer_country","suggested_name":"autobooks_end_customer_country","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_country (confidence: 25%, suggested: autobooks_end_customer_country)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:483] Using standard formatting for end_customer_country
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:386] Generator debug - column: end_customer_contact_name, predefined_label: null, use_intelligent: 1, table: autobooks_sketchup1320_data
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: end_customer_contact_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:451] get_intelligent_column_label called for: end_customer_contact_name
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:458] Analysis result for end_customer_contact_name: {"original_name":"end_customer_contact_name","suggested_name":"end_customer_contact_name","confidence":5.25,"reasoning":"Confidence too low to suggest changes (score: 5.25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"first_name","confidence":10.5,"pattern_matches":{"first_names":{"score":10.5,"matches":3,"total":20,"percentage":0.15,"type":"first_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"first_name":5.25}}}
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:472] No intelligent naming applied for end_customer_contact_name (confidence: 5.25%, suggested: end_customer_contact_name)
[column_analyzer] [2025-08-07 12:20:13] [data_table_generator.class.php:483] Using standard formatting for end_customer_contact_name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: id, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: id
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: id
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for id: {"original_name":"id","suggested_name":"id","confidence":100,"reasoning":"System column - no analysis needed","analysis_performed":false}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:472] No intelligent naming applied for id (confidence: 100%, suggested: id)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:483] Using standard formatting for id
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:472] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:483] Using standard formatting for serial_number
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":34.625,"reasoning":"Medium confidence match for 'company_name' (score: 34.625)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":55.25,"pattern_matches":{"business_entity":{"score":58.5,"matches":13,"total":20,"percentage":0.65},"business_keywords":{"score":55.25,"matches":13,"total":20,"percentage":0.65,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":34.625}}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:468] Intelligent naming applied: name -> company_name (confidence: 34.625)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:468] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:472] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:483] Using standard formatting for product_name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:468] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: order_shipping_address, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: order_shipping_address
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: order_shipping_address
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for order_shipping_address: {"original_name":"order_shipping_address","suggested_name":"autobooks_order_shipping_address","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:472] No intelligent naming applied for order_shipping_address (confidence: 25%, suggested: autobooks_order_shipping_address)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:483] Using standard formatting for order_shipping_address
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:386] Generator debug - column: order_shipping_state_province, predefined_label: null, use_intelligent: 1, table: autobooks_Bluebeam_data
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:390] Generator debug - calling intelligent naming for column: order_shipping_state_province
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:451] get_intelligent_column_label called for: order_shipping_state_province
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:455] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:458] Analysis result for order_shipping_state_province: {"original_name":"order_shipping_state_province","suggested_name":"autobooks_order_shipping_state_province","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":4,"total":4,"percentage":1,"type":"location"}},"sample_size":4},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:472] No intelligent naming applied for order_shipping_state_province (confidence: 25%, suggested: autobooks_order_shipping_state_province)
[column_analyzer] [2025-08-07 13:28:21] [data_table_generator.class.php:483] Using standard formatting for order_shipping_state_province
