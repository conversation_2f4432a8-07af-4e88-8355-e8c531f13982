[builder_debug] [2025-08-01 13:47:39] [data-source-builder.edge.php:244] Array\n(\n    [function] => data_source_builder_preview\n    [data_source_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [data_source_keys] => Array\n        (\n            [0] => id\n            [1] => name\n            [2] => description\n            [3] => category\n            [4] => status\n            [5] => table_name\n            [6] => tables\n            [7] => table_aliases\n            [8] => joins\n            [9] => selected_columns\n            [10] => column_aliases\n            [11] => custom_columns\n            [12] => filters\n            [13] => sorting\n            [14] => limits\n            [15] => column_mapping\n            [16] => created_by\n            [17] => created_at\n            [18] => updated_at\n            [19] => grouping\n        )\n\n)\n
[builder_debug] [2025-08-01 13:49:46] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 13:50:32] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 13:51:47] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 13:54:57] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 14:58:10] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 15:03:27] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 15:04:18] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
[builder_debug] [2025-08-01 15:06:05] [data-source-builder.edge.php:262] Array\n(\n    [function] => data_source_builder_preview\n    [preview_joins] => Array\n        (\n            [0] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.endCustomer_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => endcust\n                )\n\n            [1] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.soldTo_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => soldto\n                )\n\n            [2] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.solutionProvider_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => solpro\n                )\n\n            [3] => Array\n                (\n                    [type] => LEFT\n                    [left_table] => autodesk_subscriptions\n                    [left_column] => autodesk_subscriptions.nurtureReseller_csn\n                    [right_table] => autodesk_accounts\n                    [right_column] => autodesk_accounts.account_csn\n                    [left_alias] => \n                    [right_alias] => resell\n                )\n\n        )\n\n    [preview_tables] => Array\n        (\n            [0] => autodesk_subscriptions\n            [1] => autodesk_accounts\n        )\n\n    [preview_table_aliases] => Array\n        (\n            [autodesk_subscriptions] => subs\n        )\n\n)\n
