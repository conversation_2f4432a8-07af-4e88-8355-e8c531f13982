[legacy_database_errors] [2025-07-31 18:48:31] [database.php:64]  Array\n(\n    [sql_state] => HY000\n    [driver_error_code] => 1005\n    [driver_error_message] => Can't create table `wwwcadservicescouk`.`#sql-6162_6c6fd` (errno: 121 "Duplicate key on write or update")\n    [query] => ALTER TABLE `autobooks_data_table_storage` \n               ADD CONSTRAINT `fk_data_table_storage_user` \n               FOREIGN KEY (`user_id`) REFERENCES `autobooks_users` (`id`) ON DELETE CASCADE\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/run_migration.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-31 18:48:31\n)\n
[legacy_database_errors] [2025-08-02 20:32:33] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-02 20:32:33\n)\n
[legacy_database_errors] [2025-08-02 20:40:12] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-02 20:40:12\n)\n
[legacy_database_errors] [2025-08-02 20:49:04] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-02 20:49:04\n)\n
[legacy_database_errors] [2025-08-02 20:52:08] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-02 20:52:08\n)\n
[legacy_database_errors] [2025-08-02 20:55:32] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-02 20:55:32\n)\n
[legacy_database_errors] [2025-08-02 21:04:21] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-02 21:04:21\n)\n
[legacy_database_errors] [2025-08-03 00:04:49] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-03 00:04:49\n)\n
[legacy_database_errors] [2025-08-04 07:44:46] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-04 07:44:46\n)\n
[legacy_database_errors] [2025-08-04 07:45:05] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-04 07:45:05\n)\n
[legacy_database_errors] [2025-08-04 09:23:59] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ')' at line 1\n    [query] => SELECT orders.quote_id AS orders_quote_id, cadserv.customers_name AS cadserv_customers_name, cadserv.customers_email_address AS cadserv_customers_email_address, cadserv.orders_id AS cadserv_orders_id, orders.quoteStatus AS orders_quoteStatus, orders.quoteNumber AS orders_quoteNumber, orders.message AS orders_message, orders.modifiedAt AS orders_modifiedAt FROM autodesk_orders orders LEFT JOIN orders cadserv on orders.orders_id = cadserv.orders_id WHERE ()\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/system/data_table/data_table_filter\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-04 09:23:59\n)\n
[legacy_database_errors] [2025-08-04 23:57:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-04 23:57:44\n)\n
[legacy_database_errors] [2025-08-05 19:40:09] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-05 19:40:09\n)\n
[legacy_database_errors] [2025-08-05 21:21:45] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-05 21:21:45\n)\n
[legacy_database_errors] [2025-08-05 21:31:29] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-05 21:31:29\n)\n
[legacy_database_errors] [2025-08-05 21:35:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-05 21:35:44\n)\n
[legacy_database_errors] [2025-08-06 08:16:00] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-06 08:16:00\n)\n
[legacy_database_errors] [2025-08-06 12:49:40] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-06 12:49:40\n)\n
[legacy_database_errors] [2025-08-07 09:00:51] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL' at line 1\n    [query] => SELECT endcust.account_csn AS endcust_account_csn, endcust.name AS endcust_name, endcust.address1 AS endcust_address1, endcust.address2 AS endcust_address2, endcust.address3 AS endcust_address3, endcust.city AS endcust_city, endcust.state_province AS endcust_state_province, endcust.postal_code AS endcust_postal_code, endcust.country AS endcust_country, endcust.primary_admin_first_name AS endcust_primary_admin_first_name, endcust.primary_admin_last_name AS endcust_primary_admin_last_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.team_id AS endcust_team_id, endcust.team_name AS endcust_team_name, endcust.first_name AS endcust_first_name, endcust.last_name AS endcust_last_name, endcust.email AS endcust_email, endcust.last_modified AS endcust_last_modified FROM autodesk_accounts endcust WHERE (endcust.account_csn LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.name LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.address1 LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.address2 LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.address3 LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.city LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.state_province LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.postal_code LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.country LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.primary_admin_first_name LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.primary_admin_last_name LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.primary_admin_email LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.team_id LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.team_name LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.first_name LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.last_name LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%' OR endcust.email LIKE '% MySQL returned an empty result set (i.e. zero rows). (Query took 0.0052 seconds.) -- SQL script to fix the autobooks_table_configs table -- This script should be run on the production database -- Drop the broken table if it exists DROP TABLE IF EXISTS `autobooks_table_configs`; [ Edit inline ] [ Edit ] [ Create PHP code ] MySQL returned an empty result set (i.e. zero rows). (Query took 0.0150 seconds.) -- Create the table with correct schema (no duplicate columns) CREATE TABLE `autobooks_table_configs` ( `id` int(10) unsigned NOT NULL AUTO_INCREMENT, `table_name` varchar(255) NOT NULL, `route_key` varchar(255) NOT NULL, `table_schema` json NOT NULL, `table_config` json NOT NULL, `column_mappings` json NOT NULL, `description` text DEFAULT NULL, `data_source` varchar(100) NOT NULL DEFAULT 'csv', `is_active` tinyint(1) NOT NULL DEFAULT 1, `created_at` timestamp NOT NULL DEFAULT current_timestamp(), `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(), PRIMARY KEY (`id`), UNIQUE KEY `autobooks_table_configs_table_name_unique` (`table_name`), KEY `autobooks_table_configs_route_key_index` (`route_key`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; [ Edit inline ] [ Edit ] [ Create PHP code ]%') ORDER BY endcust_last_modified LIMIT 300\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/system/data_table/data_table_filter\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-07 09:00:51\n)\n
[legacy_database_errors] [2025-08-07 09:22:32] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-07 09:22:32\n)\n
[legacy_database_errors] [2025-08-07 11:22:14] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-07 11:22:14\n)\n
[legacy_database_errors] [2025-08-07 11:24:33] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-07 11:24:33\n)\n
[legacy_database_errors] [2025-08-07 12:20:22] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-07 12:20:22\n)\n
[legacy_database_errors] [2025-08-07 13:35:24] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-07 13:35:24\n)\n
