[webhook] [2025-07-30 20:10:54] [adwsapi_v2.php:20]  Webhook request received at 2025-07-30 20:10:54
[webhook] [2025-07-30 20:10:54] [adwsapi_v2.php:36]  Provided signature: sha256=6cda3c8355a0c125e707fcdc4004b10e6a0a48ea096246e27b3f6dae993b219a
[webhook] [2025-07-30 20:10:54] [adwsapi_v2.php:37]  Calculated signature: sha256=483a7d7031e7b5f1372088e935f1cff745d9c4184ec63686a971a7eee70ce7c2
[webhook] [2025-07-30 20:10:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 26e713607052ff12\n    [X-B3-<PERSON><PERSON>] => 688a7c4b7dbe8dea2f62410e0d566de4\n    [B3] => 688a7c4b7dbe8dea2f62410e0d566de4-26e713607052ff12-1\n    [Traceparent] => 00-688a7c4b7dbe8dea2f62410e0d566de4-26e713607052ff12-01\n    [X-Amzn-Trace-Id] => Root=1-688a7c4b-7dbe8dea2f62410e0d566de4;Parent=26e713607052ff12;Sampled=1\n    [X-Adsk-Signature] => sha256=6cda3c8355a0c125e707fcdc4004b10e6a0a48ea096246e27b3f6dae993b219a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753906251787-69148544128710\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-30 20:10:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753906251787-69148544128710","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69148544128710","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-07-30T20:10:51.787Z"},"publishedAt":"2025-07-30T20:10:51.000Z","csn":"5103159758"}
[webhook] [2025-07-30 20:14:34] [adwsapi_v2.php:20]  Webhook request received at 2025-07-30 20:14:34
[webhook] [2025-07-30 20:14:34] [adwsapi_v2.php:36]  Provided signature: sha256=99f1b4390f3703c4e9e5ac9eb089280240341ef19bf6433a40ae7db77dd0c01c
[webhook] [2025-07-30 20:14:34] [adwsapi_v2.php:37]  Calculated signature: sha256=df93de2bdad5427171ef75cfc4dccd075ed0efe89d38bf2762e1c5b271fa9227
[webhook] [2025-07-30 20:14:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 61c4933d7146eed1\n    [X-B3-Traceid] => 688a7d27323f426f385aa9425b96dfc8\n    [B3] => 688a7d27323f426f385aa9425b96dfc8-61c4933d7146eed1-1\n    [Traceparent] => 00-688a7d27323f426f385aa9425b96dfc8-61c4933d7146eed1-01\n    [X-Amzn-Trace-Id] => Root=1-688a7d27-323f426f385aa9425b96dfc8;Parent=61c4933d7146eed1;Sampled=1\n    [X-Adsk-Signature] => sha256=99f1b4390f3703c4e9e5ac9eb089280240341ef19bf6433a40ae7db77dd0c01c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753906471813-56516996574127\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-30 20:14:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753906471813-56516996574127","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56516996574127","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-07-30T20:14:31.813Z"},"publishedAt":"2025-07-30T20:14:31.000Z","csn":"5103159758"}
[webhook] [2025-07-30 23:03:24] [adwsapi_v2.php:20]  Webhook request received at 2025-07-30 23:03:24
[webhook] [2025-07-30 23:03:24] [adwsapi_v2.php:36]  Provided signature: sha256=be25a9d3094c8840c8b770a19a4955a3f55fa36a37aad41e2217aa77f241b232
[webhook] [2025-07-30 23:03:24] [adwsapi_v2.php:37]  Calculated signature: sha256=2ca3799b20d7df058113c901e7273e05a7bbff17faffd3839496eb3dac8d9cea
[webhook] [2025-07-30 23:03:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 717550496da43dfe\n    [X-B3-Traceid] => 688aa4ba1866a428387ce8b66aed2b1b\n    [B3] => 688aa4ba1866a428387ce8b66aed2b1b-717550496da43dfe-1\n    [Traceparent] => 00-688aa4ba1866a428387ce8b66aed2b1b-717550496da43dfe-01\n    [X-Amzn-Trace-Id] => Root=1-688aa4ba-1866a428387ce8b66aed2b1b;Parent=717550496da43dfe;Sampled=1\n    [X-Adsk-Signature] => sha256=be25a9d3094c8840c8b770a19a4955a3f55fa36a37aad41e2217aa77f241b232\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753916602051-574-92527234\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-30 23:03:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753916602051-574-92527234","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-92527234","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-07-30T23:03:22.051Z"},"publishedAt":"2025-07-30T23:03:22.000Z","csn":"5103159758"}
[webhook] [2025-07-30 23:03:30] [adwsapi_v2.php:20]  Webhook request received at 2025-07-30 23:03:30
[webhook] [2025-07-30 23:03:30] [adwsapi_v2.php:36]  Provided signature: sha256=6c0b56ae655e5b0d80b19af424ae6748036e13eb66b6a8b246f799949e23be0c
[webhook] [2025-07-30 23:03:30] [adwsapi_v2.php:37]  Calculated signature: sha256=f0017103e7a615b18d65a9d096e6130ffecb24082feb05051cde752155ab0450
[webhook] [2025-07-30 23:03:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2625a659f9b9259b\n    [X-B3-Traceid] => 688aa4c051b931ef2bdd23e260ae11c9\n    [B3] => 688aa4c051b931ef2bdd23e260ae11c9-2625a659f9b9259b-1\n    [Traceparent] => 00-688aa4c051b931ef2bdd23e260ae11c9-2625a659f9b9259b-01\n    [X-Amzn-Trace-Id] => Root=1-688aa4c0-51b931ef2bdd23e260ae11c9;Parent=2625a659f9b9259b;Sampled=1\n    [X-Adsk-Signature] => sha256=6c0b56ae655e5b0d80b19af424ae6748036e13eb66b6a8b246f799949e23be0c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753916608517-569-55575321\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-30 23:03:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753916608517-569-55575321","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"569-55575321","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-07-30T23:03:28.517Z"},"publishedAt":"2025-07-30T23:03:28.000Z","csn":"5103159758"}
[webhook] [2025-07-30 23:04:30] [adwsapi_v2.php:20]  Webhook request received at 2025-07-30 23:04:30
[webhook] [2025-07-30 23:04:30] [adwsapi_v2.php:36]  Provided signature: sha256=5f403d24954b54e864eca851cd703ef1d76048f312d3692562e11b57aaf894ca
[webhook] [2025-07-30 23:04:30] [adwsapi_v2.php:37]  Calculated signature: sha256=58ac4a212f0e97f9ba316094f0ed9f34ab6dfb40fc8dbd7368d335e31559c44d
[webhook] [2025-07-30 23:04:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 84d4de062a651f89\n    [X-B3-Traceid] => 688aa4fc37549edb63ae723b5a7ed547\n    [B3] => 688aa4fc37549edb63ae723b5a7ed547-84d4de062a651f89-1\n    [Traceparent] => 00-688aa4fc37549edb63ae723b5a7ed547-84d4de062a651f89-01\n    [X-Amzn-Trace-Id] => Root=1-688aa4fc-37549edb63ae723b5a7ed547;Parent=84d4de062a651f89;Sampled=1\n    [X-Adsk-Signature] => sha256=5f403d24954b54e864eca851cd703ef1d76048f312d3692562e11b57aaf894ca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753916668268-573-16408490\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-30 23:04:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753916668268-573-16408490","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"573-16408490","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-07-30T23:04:28.268Z"},"publishedAt":"2025-07-30T23:04:28.000Z","csn":"5103159758"}
[webhook] [2025-07-30 23:04:48] [adwsapi_v2.php:20]  Webhook request received at 2025-07-30 23:04:48
[webhook] [2025-07-30 23:04:48] [adwsapi_v2.php:36]  Provided signature: sha256=2a1b2cdc9e4a7729460a92f704519f66ac2d85094b10df3a9ffb95e1e6af5652
[webhook] [2025-07-30 23:04:48] [adwsapi_v2.php:37]  Calculated signature: sha256=43dd6f33cb64dd83e20e765f7e4df2c3256e8dee1de3e207983a632d9261e602
[webhook] [2025-07-30 23:04:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 52e32295cdfe348a\n    [X-B3-Traceid] => 688aa50e239751ef393689ef310ec3e3\n    [B3] => 688aa50e239751ef393689ef310ec3e3-52e32295cdfe348a-1\n    [Traceparent] => 00-688aa50e239751ef393689ef310ec3e3-52e32295cdfe348a-01\n    [X-Amzn-Trace-Id] => Root=1-688aa50e-239751ef393689ef310ec3e3;Parent=52e32295cdfe348a;Sampled=1\n    [X-Adsk-Signature] => sha256=2a1b2cdc9e4a7729460a92f704519f66ac2d85094b10df3a9ffb95e1e6af5652\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753916686648-574-92526838\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-30 23:04:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753916686648-574-92526838","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-92526838","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-07-30T23:04:46.648Z"},"publishedAt":"2025-07-30T23:04:46.000Z","csn":"5103159758"}
[webhook] [2025-07-31 00:11:38] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 00:11:38
[webhook] [2025-07-31 00:11:38] [adwsapi_v2.php:36]  Provided signature: sha256=4041120afb298e8fd618d905494c20651b793fe00049ccd1f7dcf41600bf24b6
[webhook] [2025-07-31 00:11:38] [adwsapi_v2.php:37]  Calculated signature: sha256=1ce9e40283164992d8e9b750c9b78d33c082441f7287c3423a8f187ba8c254b3
[webhook] [2025-07-31 00:11:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e3b49de815f3355d\n    [X-B3-Traceid] => 688ab4b75ba574f62cb156a87d3c753f\n    [B3] => 688ab4b75ba574f62cb156a87d3c753f-e3b49de815f3355d-1\n    [Traceparent] => 00-688ab4b75ba574f62cb156a87d3c753f-e3b49de815f3355d-01\n    [X-Amzn-Trace-Id] => Root=1-688ab4b7-5ba574f62cb156a87d3c753f;Parent=e3b49de815f3355d;Sampled=1\n    [X-Adsk-Signature] => sha256=4041120afb298e8fd618d905494c20651b793fe00049ccd1f7dcf41600bf24b6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bfdb7c49-174b-422a-958f-7ee3a4ac2d9e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 00:11:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"bfdb7c49-174b-422a-958f-7ee3a4ac2d9e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75368953253895","status":"Expired","quantity":1,"message":"subscription status,quantity changed.","modifiedAt":"2025-07-30T23:51:33.000+0000"},"publishedAt":"2025-07-31T00:11:35.000Z","csn":"5103159758"}
[webhook] [2025-07-31 06:11:35] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 06:11:35
[webhook] [2025-07-31 06:11:35] [adwsapi_v2.php:36]  Provided signature: sha256=c5434b59352f1913e485a241013403f4aef2af302f69aefc3995f1d70785e161
[webhook] [2025-07-31 06:11:35] [adwsapi_v2.php:37]  Calculated signature: sha256=e6245cd73f6db6020d452e13c63cc424189df66f9aedb0c90e1a70e4e761ca2f
[webhook] [2025-07-31 06:11:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8b8674085ec88c85\n    [X-B3-Traceid] => 688b09147e42692d28253950153ff6c4\n    [B3] => 688b09147e42692d28253950153ff6c4-8b8674085ec88c85-1\n    [Traceparent] => 00-688b09147e42692d28253950153ff6c4-8b8674085ec88c85-01\n    [X-Amzn-Trace-Id] => Root=1-688b0914-7e42692d28253950153ff6c4;Parent=8b8674085ec88c85;Sampled=1\n    [X-Adsk-Signature] => sha256=c5434b59352f1913e485a241013403f4aef2af302f69aefc3995f1d70785e161\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => db372f0d-9d84-46a7-8064-0c9c4fe9ea48\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 06:11:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"db372f0d-9d84-46a7-8064-0c9c4fe9ea48","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75127504067843","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-07-31T05:56:30.000+0000"},"publishedAt":"2025-07-31T06:11:32.000Z","csn":"5103159758"}
[webhook] [2025-07-31 07:39:26] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 07:39:26
[webhook] [2025-07-31 07:39:26] [adwsapi_v2.php:36]  Provided signature: sha256=d1e10bd07a3e6a511525255c9dd948b69279ba4cb8d556894c3d3e0207e2d7f9
[webhook] [2025-07-31 07:39:26] [adwsapi_v2.php:37]  Calculated signature: sha256=8137d2ce007f081579caed6c1b91e55ea4d2a291626df4e005b72e89cf76ee1d
[webhook] [2025-07-31 07:39:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1fd9ce316219b950\n    [X-B3-Traceid] => 688b1dac0a8262ef471ac0d8027c4fc1\n    [B3] => 688b1dac0a8262ef471ac0d8027c4fc1-1fd9ce316219b950-1\n    [Traceparent] => 00-688b1dac0a8262ef471ac0d8027c4fc1-1fd9ce316219b950-01\n    [X-Amzn-Trace-Id] => Root=1-688b1dac-0a8262ef471ac0d8027c4fc1;Parent=1fd9ce316219b950;Sampled=1\n    [X-Adsk-Signature] => sha256=d1e10bd07a3e6a511525255c9dd948b69279ba4cb8d556894c3d3e0207e2d7f9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d04eb8d8-8e9a-4576-9620-be64539ec333\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 07:39:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"d04eb8d8-8e9a-4576-9620-be64539ec333","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62826288139585","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-07-31T07:09:20.000+0000"},"publishedAt":"2025-07-31T07:39:24.000Z","csn":"5103159758"}
[webhook] [2025-07-31 09:07:37] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 09:07:37
[webhook] [2025-07-31 09:07:37] [adwsapi_v2.php:36]  Provided signature: sha256=829931a35eebca52276b579da9a0f71c666b0a7f9039f0e31f2505e4cd7a3ffe
[webhook] [2025-07-31 09:07:37] [adwsapi_v2.php:37]  Calculated signature: sha256=573a93bca442fcb88b6f697a04b44999c0a19aa5aba4b7d95ab1ce0b4b9f65a6
[webhook] [2025-07-31 09:07:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4789f37b8ad886b6\n    [X-B3-Traceid] => 688b32570d31859623c6154848480b9c\n    [B3] => 688b32570d31859623c6154848480b9c-4789f37b8ad886b6-1\n    [Traceparent] => 00-688b32570d31859623c6154848480b9c-4789f37b8ad886b6-01\n    [X-Amzn-Trace-Id] => Root=1-688b3257-0d31859623c6154848480b9c;Parent=4789f37b8ad886b6;Sampled=1\n    [X-Adsk-Signature] => sha256=829931a35eebca52276b579da9a0f71c666b0a7f9039f0e31f2505e4cd7a3ffe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 18731ac7-447a-43e7-92fa-43bc39806820\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 09:07:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"18731ac7-447a-43e7-92fa-43bc39806820","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56927469919167","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-07-31T08:52:32.000+0000"},"publishedAt":"2025-07-31T09:07:35.000Z","csn":"5103159758"}
[webhook] [2025-07-31 09:08:10] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 09:08:10
[webhook] [2025-07-31 09:08:10] [adwsapi_v2.php:36]  Provided signature: sha256=fbc6d755d9e5572890f6a8c1614d73606677a6add4538ae7c5e2850bed8e70b3
[webhook] [2025-07-31 09:08:10] [adwsapi_v2.php:37]  Calculated signature: sha256=076eefb100964a0a685183babf4bc933fb44d06767593b392616580cf066d526
[webhook] [2025-07-31 09:08:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5bdb9ad2951039d4\n    [X-B3-Traceid] => 688b32772ac69b641422a1e7371617c3\n    [B3] => 688b32772ac69b641422a1e7371617c3-5bdb9ad2951039d4-1\n    [Traceparent] => 00-688b32772ac69b641422a1e7371617c3-5bdb9ad2951039d4-01\n    [X-Amzn-Trace-Id] => Root=1-688b3277-2ac69b641422a1e7371617c3;Parent=5bdb9ad2951039d4;Sampled=1\n    [X-Adsk-Signature] => sha256=fbc6d755d9e5572890f6a8c1614d73606677a6add4538ae7c5e2850bed8e70b3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 08ebb49c-b7d4-4189-87e5-7c678c74c992\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 09:08:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"08ebb49c-b7d4-4189-87e5-7c678c74c992","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73633647978810","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-07-31T08:53:05.000+0000"},"publishedAt":"2025-07-31T09:08:08.000Z","csn":"5103159758"}
[webhook] [2025-07-31 11:32:09] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 11:32:09
[webhook] [2025-07-31 11:32:09] [adwsapi_v2.php:36]  Provided signature: sha256=abf43fa98e53173fd63c86d0f4b52bc3762bde132ffd65337971321909a591da
[webhook] [2025-07-31 11:32:09] [adwsapi_v2.php:37]  Calculated signature: sha256=a8443a84e3eebb3831f4122c34637afb54fa0d6760fbe9e728f768610b39ed26
[webhook] [2025-07-31 11:32:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7133e65468ee4f1f\n    [X-B3-Traceid] => 688b5436ac5190137c9842d9df574e56\n    [B3] => 688b5436ac5190137c9842d9df574e56-7133e65468ee4f1f-1\n    [Traceparent] => 00-688b5436ac5190137c9842d9df574e56-7133e65468ee4f1f-01\n    [X-Amzn-Trace-Id] => Root=1-688b5436-ac5190137c9842d9df574e56;Parent=7133e65468ee4f1f;Sampled=1\n    [X-Adsk-Signature] => sha256=abf43fa98e53173fd63c86d0f4b52bc3762bde132ffd65337971321909a591da\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 18a30042-2cb7-4aa4-9807-2d0b7fa64480\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 11:32:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"18a30042-2cb7-4aa4-9807-2d0b7fa64480","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-925267","transactionId":"4436e063-a4d0-5941-84f1-8088af3beb02","quoteStatus":"Order Submitted","message":"Quote# Q-925267 status changed to Order Submitted.","modifiedAt":"2025-07-31T11:32:05.974Z"},"publishedAt":"2025-07-31T11:32:06.000Z","csn":"5103159758"}
[webhook] [2025-07-31 11:32:12] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 11:32:12
[webhook] [2025-07-31 11:32:12] [adwsapi_v2.php:36]  Provided signature: sha256=a7edda51b5281cd0fd7ee8e3b6d7e2af03e2de44d53fc787f1a31a102985462e
[webhook] [2025-07-31 11:32:12] [adwsapi_v2.php:37]  Calculated signature: sha256=78be5f9cd75ef3d0d809540bd21286cc0aff831c944f1dae0845484e452b3388
[webhook] [2025-07-31 11:32:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1f7f99c197354715\n    [X-B3-Traceid] => 688b5439de1c1e3e896bcc8f0705478b\n    [B3] => 688b5439de1c1e3e896bcc8f0705478b-1f7f99c197354715-1\n    [Traceparent] => 00-688b5439de1c1e3e896bcc8f0705478b-1f7f99c197354715-01\n    [X-Amzn-Trace-Id] => Root=1-688b5439-de1c1e3e896bcc8f0705478b;Parent=1f7f99c197354715;Sampled=1\n    [X-Adsk-Signature] => sha256=a7edda51b5281cd0fd7ee8e3b6d7e2af03e2de44d53fc787f1a31a102985462e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c7891a36-8531-4876-a03e-76784a5f73df\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 11:32:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"c7891a36-8531-4876-a03e-76784a5f73df","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-925267","transactionId":"4436e063-a4d0-5941-84f1-8088af3beb02","quoteStatus":"Ordered","message":"Quote# Q-925267 status changed to Ordered.","modifiedAt":"2025-07-31T11:32:09.534Z"},"publishedAt":"2025-07-31T11:32:09.000Z","csn":"5103159758"}
[webhook] [2025-07-31 12:07:18] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 12:07:18
[webhook] [2025-07-31 12:07:18] [adwsapi_v2.php:36]  Provided signature: sha256=ea8835701e6f0a0bb18ed1b6ab0b99436a50351547fbaac6606109fcb7fbfbde
[webhook] [2025-07-31 12:07:18] [adwsapi_v2.php:37]  Calculated signature: sha256=eaa80ba428f4a943f3ce4d4aaebc1cea6ab8b75e6eb4f1373a41af09476b8420
[webhook] [2025-07-31 12:07:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fc17bd9f4839b532\n    [X-B3-Traceid] => 688b5c741326c8bc49c972a124618cbe\n    [B3] => 688b5c741326c8bc49c972a124618cbe-fc17bd9f4839b532-1\n    [Traceparent] => 00-688b5c741326c8bc49c972a124618cbe-fc17bd9f4839b532-01\n    [X-Amzn-Trace-Id] => Root=1-688b5c74-1326c8bc49c972a124618cbe;Parent=fc17bd9f4839b532;Sampled=1\n    [X-Adsk-Signature] => sha256=ea8835701e6f0a0bb18ed1b6ab0b99436a50351547fbaac6606109fcb7fbfbde\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2d531938-2193-45af-b90b-81ab32be7cd2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 12:07:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"2d531938-2193-45af-b90b-81ab32be7cd2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56805113260413","status":"Active","quantity":2,"endDate":"2026-08-08","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-07-31T11:32:14.000+0000"},"publishedAt":"2025-07-31T12:07:16.000Z","csn":"5103159758"}
[webhook] [2025-07-31 13:23:44] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 13:23:44
[webhook] [2025-07-31 13:23:44] [adwsapi_v2.php:36]  Provided signature: sha256=78a01e7d448f83d0a900fdf3e7aa91be4c064780a91c708acb7c98435172c58c
[webhook] [2025-07-31 13:23:44] [adwsapi_v2.php:37]  Calculated signature: sha256=fa868ffddb3c79a2029b045c0ac52484688d4119bd1a9b1d586b971c854bcf18
[webhook] [2025-07-31 13:23:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7a47ab3606bd6cc0\n    [X-B3-Traceid] => 688b6e5d1be2378ff1b7b63944bb48b1\n    [B3] => 688b6e5d1be2378ff1b7b63944bb48b1-7a47ab3606bd6cc0-1\n    [Traceparent] => 00-688b6e5d1be2378ff1b7b63944bb48b1-7a47ab3606bd6cc0-01\n    [X-Amzn-Trace-Id] => Root=1-688b6e5d-1be2378ff1b7b63944bb48b1;Parent=7a47ab3606bd6cc0;Sampled=1\n    [X-Adsk-Signature] => sha256=78a01e7d448f83d0a900fdf3e7aa91be4c064780a91c708acb7c98435172c58c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d3139b6d-96ef-4f87-b9f6-a66e0c1e2a2d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 13:23:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"d3139b6d-96ef-4f87-b9f6-a66e0c1e2a2d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-973308","transactionId":"30a35632-19b5-50b7-856f-37376923d936","quoteStatus":"Draft","message":"Quote# Q-973308 status changed to Draft.","modifiedAt":"2025-07-31T13:23:41.346Z"},"publishedAt":"2025-07-31T13:23:41.000Z","csn":"5103159758"}
[webhook] [2025-07-31 14:20:47] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 14:20:47
[webhook] [2025-07-31 14:20:47] [adwsapi_v2.php:36]  Provided signature: sha256=4b96bb8313031fb2d544d1610ab85860570d46132ba273ac533f278bda2d28e2
[webhook] [2025-07-31 14:20:47] [adwsapi_v2.php:37]  Calculated signature: sha256=7a3a700fc8ce8999ab897894c41eb97d9a55e97edd5b706ff8ff5cce92ae67da
[webhook] [2025-07-31 14:20:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2d4a40d56d64b4ec\n    [X-B3-Traceid] => 688b7bbca6254da2486fd8d4fb5a424c\n    [B3] => 688b7bbca6254da2486fd8d4fb5a424c-2d4a40d56d64b4ec-1\n    [Traceparent] => 00-688b7bbca6254da2486fd8d4fb5a424c-2d4a40d56d64b4ec-01\n    [X-Amzn-Trace-Id] => Root=1-688b7bbc-a6254da2486fd8d4fb5a424c;Parent=2d4a40d56d64b4ec;Sampled=1\n    [X-Adsk-Signature] => sha256=4b96bb8313031fb2d544d1610ab85860570d46132ba273ac533f278bda2d28e2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 682f13ee-619e-43d3-a068-04edf6d6666a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 14:20:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"682f13ee-619e-43d3-a068-04edf6d6666a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901365","transactionId":"e75cb30c-1181-5a73-bace-d3d324dff9c8","quoteStatus":"Order Submitted","message":"Quote# Q-901365 status changed to Order Submitted.","modifiedAt":"2025-07-31T14:20:44.461Z"},"publishedAt":"2025-07-31T14:20:45.000Z","csn":"5103159758"}
[webhook] [2025-07-31 14:20:50] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 14:20:50
[webhook] [2025-07-31 14:20:50] [adwsapi_v2.php:36]  Provided signature: sha256=240dac56ef3eaafb0f7b087a4878f25bd95f725af550e2ab8acb5f2de415adad
[webhook] [2025-07-31 14:20:50] [adwsapi_v2.php:37]  Calculated signature: sha256=f5881abefc6542ca2cc1467b6998939e30adc8f4143834af28af314c84c0dcab
[webhook] [2025-07-31 14:20:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f1bbca9986cc15e4\n    [X-B3-Traceid] => 688b7bbf98fd6f8f39a22b9c23a20e05\n    [B3] => 688b7bbf98fd6f8f39a22b9c23a20e05-f1bbca9986cc15e4-1\n    [Traceparent] => 00-688b7bbf98fd6f8f39a22b9c23a20e05-f1bbca9986cc15e4-01\n    [X-Amzn-Trace-Id] => Root=1-688b7bbf-98fd6f8f39a22b9c23a20e05;Parent=f1bbca9986cc15e4;Sampled=1\n    [X-Adsk-Signature] => sha256=240dac56ef3eaafb0f7b087a4878f25bd95f725af550e2ab8acb5f2de415adad\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e75a545e-ec06-43ff-8f6b-d4c3698b9cd9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 14:20:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"e75a545e-ec06-43ff-8f6b-d4c3698b9cd9","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901365","transactionId":"e75cb30c-1181-5a73-bace-d3d324dff9c8","quoteStatus":"Ordered","message":"Quote# Q-901365 status changed to Ordered.","modifiedAt":"2025-07-31T14:20:47.084Z"},"publishedAt":"2025-07-31T14:20:47.000Z","csn":"5103159758"}
[webhook] [2025-07-31 14:41:01] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 14:41:01
[webhook] [2025-07-31 14:41:01] [adwsapi_v2.php:36]  Provided signature: sha256=00a3cf01b6edc1f981c8d1d3b79812a1d185b51b21e10a259f41639a5d7c4d11
[webhook] [2025-07-31 14:41:01] [adwsapi_v2.php:37]  Calculated signature: sha256=d25dd3bc4cfbe6389da6255e43bca6514ad02f6ea9339955d448ddc9f114c31b
[webhook] [2025-07-31 14:41:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3b232e5e7db43d57\n    [X-B3-Traceid] => 688b807a427dfed14cbeab30246522f7\n    [B3] => 688b807a427dfed14cbeab30246522f7-3b232e5e7db43d57-1\n    [Traceparent] => 00-688b807a427dfed14cbeab30246522f7-3b232e5e7db43d57-01\n    [X-Amzn-Trace-Id] => Root=1-688b807a-427dfed14cbeab30246522f7;Parent=3b232e5e7db43d57;Sampled=1\n    [X-Adsk-Signature] => sha256=00a3cf01b6edc1f981c8d1d3b79812a1d185b51b21e10a259f41639a5d7c4d11\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e38939a5-fde4-421c-9174-c4e1138200d4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 14:41:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"e38939a5-fde4-421c-9174-c4e1138200d4","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69175115173686","status":"Active","quantity":1,"endDate":"2026-08-10","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-07-31T14:20:53.000+0000"},"publishedAt":"2025-07-31T14:40:58.000Z","csn":"5103159758"}
[webhook] [2025-07-31 15:11:22] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 15:11:22
[webhook] [2025-07-31 15:11:22] [adwsapi_v2.php:36]  Provided signature: sha256=99acfdca27cfb1964572d338c74d63463bfbf6a94f32c97958fe9ebe2c3419e5
[webhook] [2025-07-31 15:11:22] [adwsapi_v2.php:37]  Calculated signature: sha256=f7e671161e8a26fa2bb297887bd9d0a9027bc671c4b0e1dad55d02571b7f6ccd
[webhook] [2025-07-31 15:11:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f413c06c88dc6385\n    [X-B3-Traceid] => 688b87975191504810d6535437188e66\n    [B3] => 688b87975191504810d6535437188e66-f413c06c88dc6385-1\n    [Traceparent] => 00-688b87975191504810d6535437188e66-f413c06c88dc6385-01\n    [X-Amzn-Trace-Id] => Root=1-688b8797-5191504810d6535437188e66;Parent=f413c06c88dc6385;Sampled=1\n    [X-Adsk-Signature] => sha256=99acfdca27cfb1964572d338c74d63463bfbf6a94f32c97958fe9ebe2c3419e5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b868f6f7-7803-4801-a842-cc404ffc176f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 15:11:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"b868f6f7-7803-4801-a842-cc404ffc176f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69529918880411","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-07-31T14:56:17.000+0000"},"publishedAt":"2025-07-31T15:11:19.000Z","csn":"5103159758"}
[webhook] [2025-07-31 15:37:43] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 15:37:43
[webhook] [2025-07-31 15:37:43] [adwsapi_v2.php:36]  Provided signature: sha256=7e40864d37314d55e2e35e05b77a856db99df144967783b430c7c3b7787c23d0
[webhook] [2025-07-31 15:37:43] [adwsapi_v2.php:37]  Calculated signature: sha256=ddeaa9e55d8a7ed20c3f57d262ef0b8b1ec127ba0eca6783f017870d6c18c1e6
[webhook] [2025-07-31 15:37:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1d633de0cf01bafb\n    [X-B3-Traceid] => 688b8dc460e586093221a1940e98a78d\n    [B3] => 688b8dc460e586093221a1940e98a78d-1d633de0cf01bafb-1\n    [Traceparent] => 00-688b8dc460e586093221a1940e98a78d-1d633de0cf01bafb-01\n    [X-Amzn-Trace-Id] => Root=1-688b8dc4-60e586093221a1940e98a78d;Parent=1d633de0cf01bafb;Sampled=1\n    [X-Adsk-Signature] => sha256=7e40864d37314d55e2e35e05b77a856db99df144967783b430c7c3b7787c23d0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 99f07402-197c-4a84-bfa9-197edfeb77c2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 15:37:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"99f07402-197c-4a84-bfa9-197edfeb77c2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56805113260413","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-07-31T15:07:33.000+0000"},"publishedAt":"2025-07-31T15:37:40.000Z","csn":"5103159758"}
[webhook] [2025-07-31 15:39:47] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 15:39:47
[webhook] [2025-07-31 15:39:47] [adwsapi_v2.php:36]  Provided signature: sha256=c78d3de0bdb6472994ff439acedcf291cb8f3e15c361f77b8428f9680583462f
[webhook] [2025-07-31 15:39:47] [adwsapi_v2.php:37]  Calculated signature: sha256=e10ea5cfa71b0bc33898cd4c607921807d9736156af9970f51b107165277665a
[webhook] [2025-07-31 15:39:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 61ceab207e3d17ac\n    [X-B3-Traceid] => 688b8e4155e837724783a17a27ed2a0c\n    [B3] => 688b8e4155e837724783a17a27ed2a0c-61ceab207e3d17ac-1\n    [Traceparent] => 00-688b8e4155e837724783a17a27ed2a0c-61ceab207e3d17ac-01\n    [X-Amzn-Trace-Id] => Root=1-688b8e41-55e837724783a17a27ed2a0c;Parent=61ceab207e3d17ac;Sampled=1\n    [X-Adsk-Signature] => sha256=c78d3de0bdb6472994ff439acedcf291cb8f3e15c361f77b8428f9680583462f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ec7f9ec1-bdb8-4747-989d-b14532cb30a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 15:39:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"ec7f9ec1-bdb8-4747-989d-b14532cb30a6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69175115173686","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-07-31T15:09:36.000+0000"},"publishedAt":"2025-07-31T15:39:45.000Z","csn":"5103159758"}
[webhook] [2025-07-31 16:10:26] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 16:10:26
[webhook] [2025-07-31 16:10:26] [adwsapi_v2.php:36]  Provided signature: sha256=251c5397748c767f93c5c83e940e151a803cc6ab467a7650de0a239c7d4b9236
[webhook] [2025-07-31 16:10:26] [adwsapi_v2.php:37]  Calculated signature: sha256=86b84b681a233af814a1d46cb0357d56240edbe1d5074942db2bc8a4067a2fbc
[webhook] [2025-07-31 16:10:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c26be5a09c8ad427\n    [X-B3-Traceid] => 688b956f77274c76162129ab53ce382a\n    [B3] => 688b956f77274c76162129ab53ce382a-c26be5a09c8ad427-1\n    [Traceparent] => 00-688b956f77274c76162129ab53ce382a-c26be5a09c8ad427-01\n    [X-Amzn-Trace-Id] => Root=1-688b956f-77274c76162129ab53ce382a;Parent=c26be5a09c8ad427;Sampled=1\n    [X-Adsk-Signature] => sha256=251c5397748c767f93c5c83e940e151a803cc6ab467a7650de0a239c7d4b9236\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753978223863-56805113260413\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 16:10:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753978223863-56805113260413","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56805113260413","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-07-31T16:10:23.863Z"},"publishedAt":"2025-07-31T16:10:23.000Z","csn":"5103159758"}
[webhook] [2025-07-31 16:37:44] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 16:37:44
[webhook] [2025-07-31 16:37:44] [adwsapi_v2.php:36]  Provided signature: sha256=600936a792c9d7e37372ae73b83fa1f04da0e6b621d034972c2fbb439c36ceff
[webhook] [2025-07-31 16:37:44] [adwsapi_v2.php:37]  Calculated signature: sha256=6b13e1a33bf9984360678892f3d19a792f7badd448a28c15e340ecd0c85cb323
[webhook] [2025-07-31 16:37:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 210eaf2290633e33\n    [X-B3-Traceid] => 688b9bd5554d677b284d22543d4e9bae\n    [B3] => 688b9bd5554d677b284d22543d4e9bae-210eaf2290633e33-1\n    [Traceparent] => 00-688b9bd5554d677b284d22543d4e9bae-210eaf2290633e33-01\n    [X-Amzn-Trace-Id] => Root=1-688b9bd5-554d677b284d22543d4e9bae;Parent=210eaf2290633e33;Sampled=1\n    [X-Adsk-Signature] => sha256=600936a792c9d7e37372ae73b83fa1f04da0e6b621d034972c2fbb439c36ceff\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7e7e45da-7484-4f0b-abca-56ddc1773e1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 16:37:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"7e7e45da-7484-4f0b-abca-56ddc1773e1b","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"68190330941508","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-07-31T16:12:39.000+0000"},"publishedAt":"2025-07-31T16:37:42.000Z","csn":"5103159758"}
[webhook] [2025-07-31 19:39:09] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 19:39:09
[webhook] [2025-07-31 19:39:09] [adwsapi_v2.php:36]  Provided signature: sha256=b8b7a7969d02dc2fb4bb01d83a0d3456f1550db4ac66195f00c2236284665306
[webhook] [2025-07-31 19:39:09] [adwsapi_v2.php:37]  Calculated signature: sha256=a9d28b1ef9b06b758876600679b6cd9982ce25e95aecf94624f5b9bccc257299
[webhook] [2025-07-31 19:39:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b9361e91f0030b79\n    [X-B3-Traceid] => 688bc65a5d1f26c05cfe3d956e5c89e2\n    [B3] => 688bc65a5d1f26c05cfe3d956e5c89e2-b9361e91f0030b79-1\n    [Traceparent] => 00-688bc65a5d1f26c05cfe3d956e5c89e2-b9361e91f0030b79-01\n    [X-Amzn-Trace-Id] => Root=1-688bc65a-5d1f26c05cfe3d956e5c89e2;Parent=b9361e91f0030b79;Sampled=1\n    [X-Adsk-Signature] => sha256=b8b7a7969d02dc2fb4bb01d83a0d3456f1550db4ac66195f00c2236284665306\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 282e46ef-5b39-481a-aa7a-9da6e1f9d79d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 19:39:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"282e46ef-5b39-481a-aa7a-9da6e1f9d79d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74169544390614","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-07-31T19:03:21.000+0000"},"publishedAt":"2025-07-31T19:39:06.000Z","csn":"5103159758"}
[webhook] [2025-07-31 20:06:38] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 20:06:38
[webhook] [2025-07-31 20:06:38] [adwsapi_v2.php:36]  Provided signature: sha256=b041a474af2f1e65a8868e0b2dd7d8117b68a741228bbf21bc65d5a020dd1544
[webhook] [2025-07-31 20:06:38] [adwsapi_v2.php:37]  Calculated signature: sha256=93d56a11af95548781130ef09baa7430e1399ca2f4c36c65cc80dd563c07e8d9
[webhook] [2025-07-31 20:06:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 549d0ed826f68d01\n    [X-B3-Traceid] => 688bcccb3620c55b7cbe59e36ec2b689\n    [B3] => 688bcccb3620c55b7cbe59e36ec2b689-549d0ed826f68d01-1\n    [Traceparent] => 00-688bcccb3620c55b7cbe59e36ec2b689-549d0ed826f68d01-01\n    [X-Amzn-Trace-Id] => Root=1-688bcccb-3620c55b7cbe59e36ec2b689;Parent=549d0ed826f68d01;Sampled=1\n    [X-Adsk-Signature] => sha256=b041a474af2f1e65a8868e0b2dd7d8117b68a741228bbf21bc65d5a020dd1544\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753992395808-69175115173686\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 20:06:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753992395808-69175115173686","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69175115173686","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-07-31T20:06:35.808Z"},"publishedAt":"2025-07-31T20:06:35.000Z","csn":"5103159758"}
[webhook] [2025-07-31 20:11:37] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 20:11:37
[webhook] [2025-07-31 20:11:37] [adwsapi_v2.php:36]  Provided signature: sha256=4b565da9dbfcb3bcc84244a387a14f82f66e51133759f979e1a64b6cb6ca542c
[webhook] [2025-07-31 20:11:37] [adwsapi_v2.php:37]  Calculated signature: sha256=26abdd686800fb05aa3a7f2207f13f64ec63ac9eeb1abbad735db311d46593d5
[webhook] [2025-07-31 20:11:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 22d4dc6caf5e54e7\n    [X-B3-Traceid] => 688bcdf67d1eb4f9200485c723690c5b\n    [B3] => 688bcdf67d1eb4f9200485c723690c5b-22d4dc6caf5e54e7-1\n    [Traceparent] => 00-688bcdf67d1eb4f9200485c723690c5b-22d4dc6caf5e54e7-01\n    [X-Amzn-Trace-Id] => Root=1-688bcdf6-7d1eb4f9200485c723690c5b;Parent=22d4dc6caf5e54e7;Sampled=1\n    [X-Adsk-Signature] => sha256=4b565da9dbfcb3bcc84244a387a14f82f66e51133759f979e1a64b6cb6ca542c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753992694891-69529918880411\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 20:11:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753992694891-69529918880411","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69529918880411","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-07-31T20:11:34.891Z"},"publishedAt":"2025-07-31T20:11:34.000Z","csn":"5103159758"}
[webhook] [2025-07-31 20:16:18] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 20:16:18
[webhook] [2025-07-31 20:16:18] [adwsapi_v2.php:36]  Provided signature: sha256=4fb4f0f1e98df0ffb661c1162272350fb7e1c53e9314d0487449fe11edce45f0
[webhook] [2025-07-31 20:16:18] [adwsapi_v2.php:37]  Calculated signature: sha256=9c8498f6fef964e238d14853e45406cf4c7944923218e5a23f0897100eea76e4
[webhook] [2025-07-31 20:16:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => dd32e1698c2a292b\n    [X-B3-Traceid] => 688bcf0f400db56146319def18fbc9b4\n    [B3] => 688bcf0f400db56146319def18fbc9b4-dd32e1698c2a292b-1\n    [Traceparent] => 00-688bcf0f400db56146319def18fbc9b4-dd32e1698c2a292b-01\n    [X-Amzn-Trace-Id] => Root=1-688bcf0f-400db56146319def18fbc9b4;Parent=dd32e1698c2a292b;Sampled=1\n    [X-Adsk-Signature] => sha256=4fb4f0f1e98df0ffb661c1162272350fb7e1c53e9314d0487449fe11edce45f0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1753992975327-56805113260413\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 20:16:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"1753992975327-56805113260413","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56805113260413","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-07-31T20:16:15.327Z"},"publishedAt":"2025-07-31T20:16:15.000Z","csn":"5103159758"}
[webhook] [2025-07-31 23:01:56] [adwsapi_v2.php:20]  Webhook request received at 2025-07-31 23:01:56
[webhook] [2025-07-31 23:01:56] [adwsapi_v2.php:36]  Provided signature: sha256=9715e8708208cfb95ddc86cae498bb9ece0089d23fef19143447e9e561081dda
[webhook] [2025-07-31 23:01:56] [adwsapi_v2.php:37]  Calculated signature: sha256=cb5f720974985689681160e7aa7a927df7526cad5c220ce41367b2f13207f4c2
[webhook] [2025-07-31 23:01:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 82c18ebe20e0e9e0\n    [X-B3-Traceid] => 688bf5e147e8898a397c76ef3c4336c6\n    [B3] => 688bf5e147e8898a397c76ef3c4336c6-82c18ebe20e0e9e0-1\n    [Traceparent] => 00-688bf5e147e8898a397c76ef3c4336c6-82c18ebe20e0e9e0-01\n    [X-Amzn-Trace-Id] => Root=1-688bf5e1-47e8898a397c76ef3c4336c6;Parent=82c18ebe20e0e9e0;Sampled=1\n    [X-Adsk-Signature] => sha256=9715e8708208cfb95ddc86cae498bb9ece0089d23fef19143447e9e561081dda\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754002913617-574-92722818\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-07-31 23:01:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754002913617-574-92722818","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-92722818","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-07-31T23:01:53.617Z"},"publishedAt":"2025-07-31T23:01:53.000Z","csn":"5103159758"}
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 00:06:57
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:36]  Provided signature: sha256=9ed09980f9bb7f354adcc7657d31d83c493e469a69ec3fc97e11abf2539e1033
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:37]  Calculated signature: sha256=6665a9d8a18b1608375f2d278b7f9a44eebe3311093411374650ec98593bdcf6
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8bbc22db4c2a5cf3\n    [X-B3-Traceid] => 688c051f0419b34d078011df4d9d9c99\n    [B3] => 688c051f0419b34d078011df4d9d9c99-8bbc22db4c2a5cf3-1\n    [Traceparent] => 00-688c051f0419b34d078011df4d9d9c99-8bbc22db4c2a5cf3-01\n    [X-Amzn-Trace-Id] => Root=1-688c051f-0419b34d078011df4d9d9c99;Parent=8bbc22db4c2a5cf3;Sampled=1\n    [X-Adsk-Signature] => sha256=9ed09980f9bb7f354adcc7657d31d83c493e469a69ec3fc97e11abf2539e1033\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cc44b3a3-d8ad-4783-82c2-7a3cb7601534\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"cc44b3a3-d8ad-4783-82c2-7a3cb7601534","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75014667058610","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-07-31T23:41:52.000+0000"},"publishedAt":"2025-08-01T00:06:55.000Z","csn":"5103159758"}
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 00:06:57
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:36]  Provided signature: sha256=8ccfb3869d1b8a1b6faf3b7e3387f696e5a4c8606dcf74455317aff672ab4397
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:37]  Calculated signature: sha256=12da0c2e53a8f725ad285e67e96b33661d085f32f99f41003e0200df50565332
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ce41708be70cedeb\n    [X-B3-Traceid] => 688c051f797824296c44e506281c2b7a\n    [B3] => 688c051f797824296c44e506281c2b7a-ce41708be70cedeb-1\n    [Traceparent] => 00-688c051f797824296c44e506281c2b7a-ce41708be70cedeb-01\n    [X-Amzn-Trace-Id] => Root=1-688c051f-797824296c44e506281c2b7a;Parent=ce41708be70cedeb;Sampled=1\n    [X-Adsk-Signature] => sha256=8ccfb3869d1b8a1b6faf3b7e3387f696e5a4c8606dcf74455317aff672ab4397\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d812da0c-4abc-46e8-b97e-53e3dff0ffd8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 00:06:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"d812da0c-4abc-46e8-b97e-53e3dff0ffd8","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75006472719688","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-07-31T23:41:53.000+0000"},"publishedAt":"2025-08-01T00:06:55.000Z","csn":"5103159758"}
[webhook] [2025-08-01 00:10:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 00:10:43
[webhook] [2025-08-01 00:10:43] [adwsapi_v2.php:36]  Provided signature: sha256=796bea6fec1f35ad430985ed13e589da14a02eacb1fe86268f7bf7f00e3eddf6
[webhook] [2025-08-01 00:10:43] [adwsapi_v2.php:37]  Calculated signature: sha256=71a2518bc887ab3ab1615fbf2bf13801e177564cc2e66c1fc1c7ac81500d7b75
[webhook] [2025-08-01 00:10:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4715085014998dff\n    [X-B3-Traceid] => 688c0601505f258f76e7359f19b241bc\n    [B3] => 688c0601505f258f76e7359f19b241bc-4715085014998dff-1\n    [Traceparent] => 00-688c0601505f258f76e7359f19b241bc-4715085014998dff-01\n    [X-Amzn-Trace-Id] => Root=1-688c0601-505f258f76e7359f19b241bc;Parent=4715085014998dff;Sampled=1\n    [X-Adsk-Signature] => sha256=796bea6fec1f35ad430985ed13e589da14a02eacb1fe86268f7bf7f00e3eddf6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 060da2fe-4b35-45df-89de-2689f8789eb9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 00:10:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"060da2fe-4b35-45df-89de-2689f8789eb9","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"68915687453814","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-07-31T23:40:39.000+0000"},"publishedAt":"2025-08-01T00:10:41.000Z","csn":"5103159758"}
[webhook] [2025-08-01 00:11:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 00:11:22
[webhook] [2025-08-01 00:11:22] [adwsapi_v2.php:36]  Provided signature: sha256=545ad3a345bdb06b43f52222fc58340d8e3cbd7d73d6400d0f421c509775cbce
[webhook] [2025-08-01 00:11:22] [adwsapi_v2.php:37]  Calculated signature: sha256=76afb54d0c88962c0077ffea98269c3e3cb4daa2bfe460323ded1e10f907c17d
[webhook] [2025-08-01 00:11:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 23e9b4a39f28d00a\n    [X-B3-Traceid] => 688c06287082db944218607b4f7088b3\n    [B3] => 688c06287082db944218607b4f7088b3-23e9b4a39f28d00a-1\n    [Traceparent] => 00-688c06287082db944218607b4f7088b3-23e9b4a39f28d00a-01\n    [X-Amzn-Trace-Id] => Root=1-688c0628-7082db944218607b4f7088b3;Parent=23e9b4a39f28d00a;Sampled=1\n    [X-Adsk-Signature] => sha256=545ad3a345bdb06b43f52222fc58340d8e3cbd7d73d6400d0f421c509775cbce\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 693fd46b-e840-43a0-906a-07673c2ee718\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 00:11:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"693fd46b-e840-43a0-906a-07673c2ee718","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71887756008038","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-07-31T23:41:16.000+0000"},"publishedAt":"2025-08-01T00:11:20.000Z","csn":"5103159758"}
[webhook] [2025-08-01 00:11:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 00:11:23
[webhook] [2025-08-01 00:11:23] [adwsapi_v2.php:36]  Provided signature: sha256=7a7907db7ff481e43d6a693f1790c5e551e65a452dbaa10d3926a956c2fd8ae0
[webhook] [2025-08-01 00:11:23] [adwsapi_v2.php:37]  Calculated signature: sha256=df32593d03cec1e0cccf6ab0daa8dbccb7476adbffe67925d214e8cfcb07d1e9
[webhook] [2025-08-01 00:11:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9da160e69621391a\n    [X-B3-Traceid] => 688c0629600b36e0326714bb658a9461\n    [B3] => 688c0629600b36e0326714bb658a9461-9da160e69621391a-1\n    [Traceparent] => 00-688c0629600b36e0326714bb658a9461-9da160e69621391a-01\n    [X-Amzn-Trace-Id] => Root=1-688c0629-600b36e0326714bb658a9461;Parent=9da160e69621391a;Sampled=1\n    [X-Adsk-Signature] => sha256=7a7907db7ff481e43d6a693f1790c5e551e65a452dbaa10d3926a956c2fd8ae0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ab003ae8-9d5c-414b-b8be-bc8583b9c63f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 00:11:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"ab003ae8-9d5c-414b-b8be-bc8583b9c63f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55508693518307","status":"Suspended","message":"subscription status changed.","modifiedAt":"2025-07-31T23:41:18.000+0000"},"publishedAt":"2025-08-01T00:11:21.000Z","csn":"5103159758"}
[webhook] [2025-08-01 06:06:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 06:06:07
[webhook] [2025-08-01 06:06:07] [adwsapi_v2.php:36]  Provided signature: sha256=3980a731b4326d89fee344386d304a08a3ebbd27fe613b184df85f07cbd1a906
[webhook] [2025-08-01 06:06:07] [adwsapi_v2.php:37]  Calculated signature: sha256=7f9004467bc713abf53da279bbdee5c5ddbd302e4dc2da98528738a5275f15ab
[webhook] [2025-08-01 06:06:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c06beb384b418536\n    [X-B3-Traceid] => 688c594d125e1f2366e5b9b61db0bf93\n    [B3] => 688c594d125e1f2366e5b9b61db0bf93-c06beb384b418536-1\n    [Traceparent] => 00-688c594d125e1f2366e5b9b61db0bf93-c06beb384b418536-01\n    [X-Amzn-Trace-Id] => Root=1-688c594d-125e1f2366e5b9b61db0bf93;Parent=c06beb384b418536;Sampled=1\n    [X-Adsk-Signature] => sha256=3980a731b4326d89fee344386d304a08a3ebbd27fe613b184df85f07cbd1a906\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e8bded2b-47c7-40b3-b648-897f955044fa\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 06:06:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"e8bded2b-47c7-40b3-b648-897f955044fa","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75136903143720","status":"Expired","quantity":1,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-01T05:36:02.000+0000"},"publishedAt":"2025-08-01T06:06:05.000Z","csn":"5103159758"}
[webhook] [2025-08-01 07:09:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 07:09:21
[webhook] [2025-08-01 07:09:21] [adwsapi_v2.php:36]  Provided signature: sha256=ab618785365790f439eb1a377efa516e6ac416fd501a01270a29e5e464ecd73e
[webhook] [2025-08-01 07:09:21] [adwsapi_v2.php:37]  Calculated signature: sha256=e7a87c684e5047cbf7bf480eb1c8990279cceddbc879c387f041ad0a4ac5e979
[webhook] [2025-08-01 07:09:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 371bd9cd2f47e33a\n    [X-B3-Traceid] => 688c681f35e0eea016d6709b663ad205\n    [B3] => 688c681f35e0eea016d6709b663ad205-371bd9cd2f47e33a-1\n    [Traceparent] => 00-688c681f35e0eea016d6709b663ad205-371bd9cd2f47e33a-01\n    [X-Amzn-Trace-Id] => Root=1-688c681f-35e0eea016d6709b663ad205;Parent=371bd9cd2f47e33a;Sampled=1\n    [X-Adsk-Signature] => sha256=ab618785365790f439eb1a377efa516e6ac416fd501a01270a29e5e464ecd73e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 43c52483-8b52-4119-97d0-767fc09578ba\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 07:09:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"43c52483-8b52-4119-97d0-767fc09578ba","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69148544128710","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T06:54:16.000+0000"},"publishedAt":"2025-08-01T07:09:19.000Z","csn":"5103159758"}
[webhook] [2025-08-01 07:10:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 07:10:54
[webhook] [2025-08-01 07:10:54] [adwsapi_v2.php:36]  Provided signature: sha256=e38fa8d3406e65298a55fd9e3c57f9588cc999032e4ad84f6cb5cf8a9543eff2
[webhook] [2025-08-01 07:10:54] [adwsapi_v2.php:37]  Calculated signature: sha256=84b295bb4eee928d8c697a10c23de88102f7bf1694c187b919ea0ac530d2b72a
[webhook] [2025-08-01 07:10:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 28352a707eaf63ba\n    [X-B3-Traceid] => 688c687c7666915e5f8ee821720145c3\n    [B3] => 688c687c7666915e5f8ee821720145c3-28352a707eaf63ba-1\n    [Traceparent] => 00-688c687c7666915e5f8ee821720145c3-28352a707eaf63ba-01\n    [X-Amzn-Trace-Id] => Root=1-688c687c-7666915e5f8ee821720145c3;Parent=28352a707eaf63ba;Sampled=1\n    [X-Adsk-Signature] => sha256=e38fa8d3406e65298a55fd9e3c57f9588cc999032e4ad84f6cb5cf8a9543eff2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e973a56c-8d80-4f93-92d5-e9f2a9209f67\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 07:10:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"e973a56c-8d80-4f93-92d5-e9f2a9209f67","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71932610877074","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-01T06:45:48.000+0000"},"publishedAt":"2025-08-01T07:10:52.000Z","csn":"5103159758"}
[webhook] [2025-08-01 07:12:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 07:12:45
[webhook] [2025-08-01 07:12:45] [adwsapi_v2.php:36]  Provided signature: sha256=f51b159adeaf28e312040ac35fe91aa736e73311141e03dee872d65df4c7613e
[webhook] [2025-08-01 07:12:45] [adwsapi_v2.php:37]  Calculated signature: sha256=57bcbf1a9d61c8806ddbe6dd14dae765a66f156a14d1c96d46e875dc6a0959c0
[webhook] [2025-08-01 07:12:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => df6b8eccb904c6e2\n    [X-B3-Traceid] => 688c68ea67118a203c65df744b9617d7\n    [B3] => 688c68ea67118a203c65df744b9617d7-df6b8eccb904c6e2-1\n    [Traceparent] => 00-688c68ea67118a203c65df744b9617d7-df6b8eccb904c6e2-01\n    [X-Amzn-Trace-Id] => Root=1-688c68ea-67118a203c65df744b9617d7;Parent=df6b8eccb904c6e2;Sampled=1\n    [X-Adsk-Signature] => sha256=f51b159adeaf28e312040ac35fe91aa736e73311141e03dee872d65df4c7613e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7d7ef65c-8f56-42d9-b5b7-0784afad5bbb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 07:12:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"7d7ef65c-8f56-42d9-b5b7-0784afad5bbb","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56942318578860","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T06:57:39.000+0000"},"publishedAt":"2025-08-01T07:12:42.000Z","csn":"5103159758"}
[webhook] [2025-08-01 09:07:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 09:07:19
[webhook] [2025-08-01 09:07:19] [adwsapi_v2.php:36]  Provided signature: sha256=87c7040a24e195d62ddb5409797f361ec989f9258e6166752bd8d29e5128c69a
[webhook] [2025-08-01 09:07:19] [adwsapi_v2.php:37]  Calculated signature: sha256=65ef5688a52682cdcb198d5c834e1133078bdf0791bbbf07eeca38b5ace68f51
[webhook] [2025-08-01 09:07:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec7e1a0b543c785f\n    [X-B3-Traceid] => 688c83c33b0a61f45f4be6cc12879125\n    [B3] => 688c83c33b0a61f45f4be6cc12879125-ec7e1a0b543c785f-1\n    [Traceparent] => 00-688c83c33b0a61f45f4be6cc12879125-ec7e1a0b543c785f-01\n    [X-Amzn-Trace-Id] => Root=1-688c83c3-3b0a61f45f4be6cc12879125;Parent=ec7e1a0b543c785f;Sampled=1\n    [X-Adsk-Signature] => sha256=87c7040a24e195d62ddb5409797f361ec989f9258e6166752bd8d29e5128c69a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 891a94cd-e56b-4816-ad16-65cfeeb8836d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 09:07:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"891a94cd-e56b-4816-ad16-65cfeeb8836d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"57615947812131","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-01T08:52:12.000+0000"},"publishedAt":"2025-08-01T09:07:15.000Z","csn":"5103159758"}
[webhook] [2025-08-01 09:53:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 09:53:56
[webhook] [2025-08-01 09:53:56] [adwsapi_v2.php:36]  Provided signature: sha256=32ef3240c65efee00db79255e3cfc13f7602f15fda3f3c9412b4622e3386eae7
[webhook] [2025-08-01 09:53:56] [adwsapi_v2.php:37]  Calculated signature: sha256=e6cedba811be2c385e5ea395805685dc8507a6387c0cbd0ecb5a7ed96d541856
[webhook] [2025-08-01 09:53:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e12bfc76088fc901\n    [X-B3-Traceid] => 688c8eb1e86c31a5c506862f30c7abba\n    [B3] => 688c8eb1e86c31a5c506862f30c7abba-e12bfc76088fc901-1\n    [Traceparent] => 00-688c8eb1e86c31a5c506862f30c7abba-e12bfc76088fc901-01\n    [X-Amzn-Trace-Id] => Root=1-688c8eb1-e86c31a5c506862f30c7abba;Parent=e12bfc76088fc901;Sampled=1\n    [X-Adsk-Signature] => sha256=32ef3240c65efee00db79255e3cfc13f7602f15fda3f3c9412b4622e3386eae7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f181b3ed-ba17-4925-ba06-3b2c440fd155\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 09:53:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"f181b3ed-ba17-4925-ba06-3b2c440fd155","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-975780","transactionId":"04edbbc9-f070-5615-8a95-dacf609e3200","quoteStatus":"Draft","message":"Quote# Q-975780 status changed to Draft.","modifiedAt":"2025-08-01T09:53:53.701Z"},"publishedAt":"2025-08-01T09:53:54.000Z","csn":"5103159758"}
[webhook] [2025-08-01 09:59:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 09:59:44
[webhook] [2025-08-01 09:59:44] [adwsapi_v2.php:36]  Provided signature: sha256=af63694d81e44b856f84878c74a5f2c0a1545441909abcaa879ff15e072f742b
[webhook] [2025-08-01 09:59:44] [adwsapi_v2.php:37]  Calculated signature: sha256=e334bea7bf28bb54cc01bf9cc0315851ae1797873938a730065ca675a715efc3
[webhook] [2025-08-01 09:59:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1e6e05e9fe7dc723\n    [X-B3-Traceid] => 688c900ec51eb9e4201bc4661dda4c04\n    [B3] => 688c900ec51eb9e4201bc4661dda4c04-1e6e05e9fe7dc723-1\n    [Traceparent] => 00-688c900ec51eb9e4201bc4661dda4c04-1e6e05e9fe7dc723-01\n    [X-Amzn-Trace-Id] => Root=1-688c900e-c51eb9e4201bc4661dda4c04;Parent=1e6e05e9fe7dc723;Sampled=1\n    [X-Adsk-Signature] => sha256=af63694d81e44b856f84878c74a5f2c0a1545441909abcaa879ff15e072f742b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cb17a5d3-aefa-4178-bfbb-7912d0be6871\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 09:59:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"cb17a5d3-aefa-4178-bfbb-7912d0be6871","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-975780","transactionId":"04edbbc9-f070-5615-8a95-dacf609e3200","quoteStatus":"Quoted","message":"Quote# Q-975780 status changed to Quoted.","modifiedAt":"2025-08-01T09:59:41.967Z"},"publishedAt":"2025-08-01T09:59:42.000Z","csn":"5103159758"}
[webhook] [2025-08-01 10:55:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 10:55:23
[webhook] [2025-08-01 10:55:23] [adwsapi_v2.php:36]  Provided signature: sha256=11e7550204ad7033fceebb78e16e1b2ec21b45f426adde8a50abc205db113fba
[webhook] [2025-08-01 10:55:23] [adwsapi_v2.php:37]  Calculated signature: sha256=38078033284b6ca81628771bf76c34024cccbd7207802c72afb862bd6c3f9fae
[webhook] [2025-08-01 10:55:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e2b1e972ad18fefe\n    [X-B3-Traceid] => 688c9d17c3479371696650226e664a8d\n    [B3] => 688c9d17c3479371696650226e664a8d-e2b1e972ad18fefe-1\n    [Traceparent] => 00-688c9d17c3479371696650226e664a8d-e2b1e972ad18fefe-01\n    [X-Amzn-Trace-Id] => Root=1-688c9d17-c3479371696650226e664a8d;Parent=e2b1e972ad18fefe;Sampled=1\n    [X-Adsk-Signature] => sha256=11e7550204ad7033fceebb78e16e1b2ec21b45f426adde8a50abc205db113fba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 96081806-d11a-4dbd-9b55-b7db157a6c96\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 10:55:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"96081806-d11a-4dbd-9b55-b7db157a6c96","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-975921","transactionId":"c1e3f97a-ea1b-51b1-a560-987c754b158b","quoteStatus":"Draft","message":"Quote# Q-975921 status changed to Draft.","modifiedAt":"2025-08-01T10:55:19.776Z"},"publishedAt":"2025-08-01T10:55:19.000Z","csn":"5103159758"}
[webhook] [2025-08-01 10:57:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 10:57:47
[webhook] [2025-08-01 10:57:47] [adwsapi_v2.php:36]  Provided signature: sha256=a148b1472583200ef5417b085dda150bedc07315c2bb896a9e5ab38a382674e6
[webhook] [2025-08-01 10:57:47] [adwsapi_v2.php:37]  Calculated signature: sha256=24ce75f8b5c981bc6da3e8d85d27a51af57ee9221d332a7485427e01296b3bd8
[webhook] [2025-08-01 10:57:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1e3f7e7926e57c49\n    [X-B3-Traceid] => 688c9da854339fb6a508ed8aa66ed4fe\n    [B3] => 688c9da854339fb6a508ed8aa66ed4fe-1e3f7e7926e57c49-1\n    [Traceparent] => 00-688c9da854339fb6a508ed8aa66ed4fe-1e3f7e7926e57c49-01\n    [X-Amzn-Trace-Id] => Root=1-688c9da8-54339fb6a508ed8aa66ed4fe;Parent=1e3f7e7926e57c49;Sampled=1\n    [X-Adsk-Signature] => sha256=a148b1472583200ef5417b085dda150bedc07315c2bb896a9e5ab38a382674e6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 302e00a1-1dda-4396-a9b6-c7da06379517\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 10:57:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"302e00a1-1dda-4396-a9b6-c7da06379517","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-975921","transactionId":"c1e3f97a-ea1b-51b1-a560-987c754b158b","quoteStatus":"Quoted","message":"Quote# Q-975921 status changed to Quoted.","modifiedAt":"2025-08-01T10:57:44.376Z"},"publishedAt":"2025-08-01T10:57:44.000Z","csn":"5103159758"}
[webhook] [2025-08-01 11:27:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 11:27:03
[webhook] [2025-08-01 11:27:03] [adwsapi_v2.php:36]  Provided signature: sha256=e8e06f195024bc368f55c8d9214a88fefd488f49df539399ab91a38b167f81e4
[webhook] [2025-08-01 11:27:03] [adwsapi_v2.php:37]  Calculated signature: sha256=a731b102d5ebe060311976a70033373ff761804b2de0989e618e96ca8056379d
[webhook] [2025-08-01 11:27:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5a1eefd054aa1159\n    [X-B3-Traceid] => 688ca485d2f345b6e646bccb273ab295\n    [B3] => 688ca485d2f345b6e646bccb273ab295-5a1eefd054aa1159-1\n    [Traceparent] => 00-688ca485d2f345b6e646bccb273ab295-5a1eefd054aa1159-01\n    [X-Amzn-Trace-Id] => Root=1-688ca485-d2f345b6e646bccb273ab295;Parent=5a1eefd054aa1159;Sampled=1\n    [X-Adsk-Signature] => sha256=e8e06f195024bc368f55c8d9214a88fefd488f49df539399ab91a38b167f81e4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1c629d27-56c6-42f6-9874-c2ac94aa8e15\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 11:27:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"1c629d27-56c6-42f6-9874-c2ac94aa8e15","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-975921","transactionId":"c1e3f97a-ea1b-51b1-a560-987c754b158b","quoteStatus":"Order Submitted","message":"Quote# Q-975921 status changed to Order Submitted.","modifiedAt":"2025-08-01T11:27:00.909Z"},"publishedAt":"2025-08-01T11:27:01.000Z","csn":"5103159758"}
[webhook] [2025-08-01 11:27:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 11:27:06
[webhook] [2025-08-01 11:27:06] [adwsapi_v2.php:36]  Provided signature: sha256=247a575846d16472ae9b5bcfb53429fa93a3dd7ee9e0655a99ac82c319b88b34
[webhook] [2025-08-01 11:27:06] [adwsapi_v2.php:37]  Calculated signature: sha256=cc892c3b6c90274be38951da67e78b876980c009fa2251a1955254381a286191
[webhook] [2025-08-01 11:27:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c0ebe9522b3096ab\n    [X-B3-Traceid] => 688ca4873a98b3a636b943ef810b33f4\n    [B3] => 688ca4873a98b3a636b943ef810b33f4-c0ebe9522b3096ab-1\n    [Traceparent] => 00-688ca4873a98b3a636b943ef810b33f4-c0ebe9522b3096ab-01\n    [X-Amzn-Trace-Id] => Root=1-688ca487-3a98b3a636b943ef810b33f4;Parent=c0ebe9522b3096ab;Sampled=1\n    [X-Adsk-Signature] => sha256=247a575846d16472ae9b5bcfb53429fa93a3dd7ee9e0655a99ac82c319b88b34\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6e13f417-2b64-4a85-925c-a03a8f1649ec\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 11:27:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"6e13f417-2b64-4a85-925c-a03a8f1649ec","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-975921","transactionId":"c1e3f97a-ea1b-51b1-a560-987c754b158b","quoteStatus":"Ordered","message":"Quote# Q-975921 status changed to Ordered.","modifiedAt":"2025-08-01T11:27:03.413Z"},"publishedAt":"2025-08-01T11:27:03.000Z","csn":"5103159758"}
[webhook] [2025-08-01 11:42:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 11:42:15
[webhook] [2025-08-01 11:42:15] [adwsapi_v2.php:36]  Provided signature: sha256=3eb59ff1e17add1e2a600aecb8fbde78f567809d367ec34fbf62118a1c87232d
[webhook] [2025-08-01 11:42:15] [adwsapi_v2.php:37]  Calculated signature: sha256=2f307ebff844933ee9e13db650b9139800f5945b239bf00da146d31f3c44f416
[webhook] [2025-08-01 11:42:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4515215412a6d5e9\n    [X-B3-Traceid] => 688ca81441ef99fd188e93c6630cab06\n    [B3] => 688ca81441ef99fd188e93c6630cab06-4515215412a6d5e9-1\n    [Traceparent] => 00-688ca81441ef99fd188e93c6630cab06-4515215412a6d5e9-01\n    [X-Amzn-Trace-Id] => Root=1-688ca814-41ef99fd188e93c6630cab06;Parent=4515215412a6d5e9;Sampled=1\n    [X-Adsk-Signature] => sha256=3eb59ff1e17add1e2a600aecb8fbde78f567809d367ec34fbf62118a1c87232d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 93891827-9790-4ac2-86c2-6a2e50bff3a1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 11:42:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"93891827-9790-4ac2-86c2-6a2e50bff3a1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56700593929570","status":"Active","quantity":1,"endDate":"2028-08-27","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-01T11:27:09.000+0000"},"publishedAt":"2025-08-01T11:42:12.000Z","csn":"5103159758"}
[webhook] [2025-08-01 11:42:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 11:42:28
[webhook] [2025-08-01 11:42:28] [adwsapi_v2.php:36]  Provided signature: sha256=a056b92323b87b7960935f11f75a3f5c287e7f73e9ce2a657933b6313c50bbca
[webhook] [2025-08-01 11:42:28] [adwsapi_v2.php:37]  Calculated signature: sha256=ce0d57e64ddff651bf6086b4d5fb65541ab4484760be104b2f004596b770a7b0
[webhook] [2025-08-01 11:42:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6833cb5c236eeaed\n    [X-B3-Traceid] => 688ca8217195baac2d0f01a00ca59909\n    [B3] => 688ca8217195baac2d0f01a00ca59909-6833cb5c236eeaed-1\n    [Traceparent] => 00-688ca8217195baac2d0f01a00ca59909-6833cb5c236eeaed-01\n    [X-Amzn-Trace-Id] => Root=1-688ca821-7195baac2d0f01a00ca59909;Parent=6833cb5c236eeaed;Sampled=1\n    [X-Adsk-Signature] => sha256=a056b92323b87b7960935f11f75a3f5c287e7f73e9ce2a657933b6313c50bbca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5ea6d16a-a5b8-4269-bba5-6a90c6875d66\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 11:42:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"5ea6d16a-a5b8-4269-bba5-6a90c6875d66","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56716427656767","status":"Active","quantity":1,"endDate":"2028-08-29","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-01T11:27:23.000+0000"},"publishedAt":"2025-08-01T11:42:26.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:10:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:10:59
[webhook] [2025-08-01 12:10:59] [adwsapi_v2.php:36]  Provided signature: sha256=ae7376ff70ef77b69847ab30faa2981b7f72f2eca79d3b4e6dca20b6aeb57aaf
[webhook] [2025-08-01 12:10:59] [adwsapi_v2.php:37]  Calculated signature: sha256=c0c5372c47428b538cd55043c2df6994c87cbf07598003433a8847760a10499f
[webhook] [2025-08-01 12:10:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cbb11f51f35925e1\n    [X-B3-Traceid] => 688caece2c5285762ed99bab6ac58ae8\n    [B3] => 688caece2c5285762ed99bab6ac58ae8-cbb11f51f35925e1-1\n    [Traceparent] => 00-688caece2c5285762ed99bab6ac58ae8-cbb11f51f35925e1-01\n    [X-Amzn-Trace-Id] => Root=1-688caece-2c5285762ed99bab6ac58ae8;Parent=cbb11f51f35925e1;Sampled=1\n    [X-Adsk-Signature] => sha256=ae7376ff70ef77b69847ab30faa2981b7f72f2eca79d3b4e6dca20b6aeb57aaf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754050254779-56942318578860\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:10:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754050254779-56942318578860","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56942318578860","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T12:10:54.779Z"},"publishedAt":"2025-08-01T12:10:54.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:12:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:12:51
[webhook] [2025-08-01 12:12:51] [adwsapi_v2.php:36]  Provided signature: sha256=dbecc8a60733f04cfd2c2e60d593e6645dfa3d2a81cf6f6faba2fb7a0a1178d6
[webhook] [2025-08-01 12:12:51] [adwsapi_v2.php:37]  Calculated signature: sha256=10895c5643c7773429b925137591335a948931d1e666748fc3f5afd4a188dded
[webhook] [2025-08-01 12:12:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 23171b05b7bc538c\n    [X-B3-Traceid] => 688caf4018420d80882a66aa6718d306\n    [B3] => 688caf4018420d80882a66aa6718d306-23171b05b7bc538c-1\n    [Traceparent] => 00-688caf4018420d80882a66aa6718d306-23171b05b7bc538c-01\n    [X-Amzn-Trace-Id] => Root=1-688caf40-18420d80882a66aa6718d306;Parent=23171b05b7bc538c;Sampled=1\n    [X-Adsk-Signature] => sha256=dbecc8a60733f04cfd2c2e60d593e6645dfa3d2a81cf6f6faba2fb7a0a1178d6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f95e2696-257d-48c2-9aa6-f6b6e77d339a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:12:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"f95e2696-257d-48c2-9aa6-f6b6e77d339a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976055","transactionId":"d7c59616-aa7d-5dbf-90c4-f7101b0ba4f6","quoteStatus":"Draft","message":"Quote# Q-976055 status changed to Draft.","modifiedAt":"2025-08-01T12:12:48.511Z"},"publishedAt":"2025-08-01T12:12:48.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:18:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:18:35
[webhook] [2025-08-01 12:18:35] [adwsapi_v2.php:36]  Provided signature: sha256=6790bc6070d7d2899058af5f246eb39bd773bf02ff982b20c84b89037580de64
[webhook] [2025-08-01 12:18:35] [adwsapi_v2.php:37]  Calculated signature: sha256=c229ce0f025c3ea85fe4404c616efdd71f9ee0a6cc012f5c9424bc1900d6b40e
[webhook] [2025-08-01 12:18:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 31f90093374658ec\n    [X-B3-Traceid] => 688cb098c1dd5a2ea9b0a06f0b0b034d\n    [B3] => 688cb098c1dd5a2ea9b0a06f0b0b034d-31f90093374658ec-1\n    [Traceparent] => 00-688cb098c1dd5a2ea9b0a06f0b0b034d-31f90093374658ec-01\n    [X-Amzn-Trace-Id] => Root=1-688cb098-c1dd5a2ea9b0a06f0b0b034d;Parent=31f90093374658ec;Sampled=1\n    [X-Adsk-Signature] => sha256=6790bc6070d7d2899058af5f246eb39bd773bf02ff982b20c84b89037580de64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 99519bc4-f44c-4887-9ece-2449df5d5c33\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:18:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"99519bc4-f44c-4887-9ece-2449df5d5c33","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976055","transactionId":"d7c59616-aa7d-5dbf-90c4-f7101b0ba4f6","quoteStatus":"Quoted","message":"Quote# Q-976055 status changed to Quoted.","modifiedAt":"2025-08-01T12:18:32.221Z"},"publishedAt":"2025-08-01T12:18:32.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:19:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:19:26
[webhook] [2025-08-01 12:19:26] [adwsapi_v2.php:36]  Provided signature: sha256=3e14c97c5c67101a4b61106104307d448b1ee84f750c6a5262a9f87515ffd255
[webhook] [2025-08-01 12:19:26] [adwsapi_v2.php:37]  Calculated signature: sha256=924455d2178069ed0d67da390b0f94dc3aeb80c8ec1383d2849673a79e1729b3
[webhook] [2025-08-01 12:19:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f2cc016ede96b9d0\n    [X-B3-Traceid] => 688cb0cb2c13233a16d6453323ba2ec0\n    [B3] => 688cb0cb2c13233a16d6453323ba2ec0-f2cc016ede96b9d0-1\n    [Traceparent] => 00-688cb0cb2c13233a16d6453323ba2ec0-f2cc016ede96b9d0-01\n    [X-Amzn-Trace-Id] => Root=1-688cb0cb-2c13233a16d6453323ba2ec0;Parent=f2cc016ede96b9d0;Sampled=1\n    [X-Adsk-Signature] => sha256=3e14c97c5c67101a4b61106104307d448b1ee84f750c6a5262a9f87515ffd255\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754050763983-71932610877074\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:19:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754050763983-71932610877074","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71932610877074","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T12:19:23.983Z"},"publishedAt":"2025-08-01T12:19:24.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:29:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:29:06
[webhook] [2025-08-01 12:29:06] [adwsapi_v2.php:36]  Provided signature: sha256=5af6f137e6057c09d01b6660723ed70c4ea6a2e5072ede04ee9c1b6b80ccaeee
[webhook] [2025-08-01 12:29:06] [adwsapi_v2.php:37]  Calculated signature: sha256=c00694db67e2a26658df6960bb62e721114261b36a2adf282911f9ed78339d7e
[webhook] [2025-08-01 12:29:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f2cdc2615d93861f\n    [X-B3-Traceid] => 688cb30f90a11b876ba8fd9d093e6b11\n    [B3] => 688cb30f90a11b876ba8fd9d093e6b11-f2cdc2615d93861f-1\n    [Traceparent] => 00-688cb30f90a11b876ba8fd9d093e6b11-f2cdc2615d93861f-01\n    [X-Amzn-Trace-Id] => Root=1-688cb30f-90a11b876ba8fd9d093e6b11;Parent=f2cdc2615d93861f;Sampled=1\n    [X-Adsk-Signature] => sha256=5af6f137e6057c09d01b6660723ed70c4ea6a2e5072ede04ee9c1b6b80ccaeee\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6ddbc06a-118b-4f48-85d9-e7efe4d11a62\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:29:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"6ddbc06a-118b-4f48-85d9-e7efe4d11a62","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-936160","transactionId":"44dd3535-f3d4-5709-8860-55c456d0c2ae","quoteStatus":"Order Submitted","message":"Quote# Q-936160 status changed to Order Submitted.","modifiedAt":"2025-08-01T12:29:03.019Z"},"publishedAt":"2025-08-01T12:29:03.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:29:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:29:09
[webhook] [2025-08-01 12:29:09] [adwsapi_v2.php:36]  Provided signature: sha256=c4867b3124eae8bb050dedf747ebab7469ad62471a72b9edcc647a53dadee4b6
[webhook] [2025-08-01 12:29:09] [adwsapi_v2.php:37]  Calculated signature: sha256=f9c9af15724beb051472f4c38588d92f706bb56391e4125c3f38fcec2c8d4251
[webhook] [2025-08-01 12:29:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => eaa35b8bb3d6df12\n    [X-B3-Traceid] => 688cb3120fa346883234d03b3122baa7\n    [B3] => 688cb3120fa346883234d03b3122baa7-eaa35b8bb3d6df12-1\n    [Traceparent] => 00-688cb3120fa346883234d03b3122baa7-eaa35b8bb3d6df12-01\n    [X-Amzn-Trace-Id] => Root=1-688cb312-0fa346883234d03b3122baa7;Parent=eaa35b8bb3d6df12;Sampled=1\n    [X-Adsk-Signature] => sha256=c4867b3124eae8bb050dedf747ebab7469ad62471a72b9edcc647a53dadee4b6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 99f82686-cbf3-4686-bda3-c1e2a0260451\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:29:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"99f82686-cbf3-4686-bda3-c1e2a0260451","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-936160","transactionId":"44dd3535-f3d4-5709-8860-55c456d0c2ae","quoteStatus":"Ordered","message":"Quote# Q-936160 status changed to Ordered.","modifiedAt":"2025-08-01T12:29:06.117Z"},"publishedAt":"2025-08-01T12:29:06.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:39:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:39:40
[webhook] [2025-08-01 12:39:40] [adwsapi_v2.php:36]  Provided signature: sha256=8d1dbf5d580fae2a934feabfe7965d1f9943252f3a160b4db772f22f367f3132
[webhook] [2025-08-01 12:39:40] [adwsapi_v2.php:37]  Calculated signature: sha256=85bca5f23f02b0fb6fa589b13321c5893e67413073b6cc88faea6be8e1ff9cef
[webhook] [2025-08-01 12:39:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6fe16d55706a321e\n    [X-B3-Traceid] => 688cb589101aa187604e85cb27ebf8b9\n    [B3] => 688cb589101aa187604e85cb27ebf8b9-6fe16d55706a321e-1\n    [Traceparent] => 00-688cb589101aa187604e85cb27ebf8b9-6fe16d55706a321e-01\n    [X-Amzn-Trace-Id] => Root=1-688cb589-101aa187604e85cb27ebf8b9;Parent=6fe16d55706a321e;Sampled=1\n    [X-Adsk-Signature] => sha256=8d1dbf5d580fae2a934feabfe7965d1f9943252f3a160b4db772f22f367f3132\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754051977928-69148544128710\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:39:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754051977928-69148544128710","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69148544128710","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T12:39:37.928Z"},"publishedAt":"2025-08-01T12:39:37.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:44:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:44:18
[webhook] [2025-08-01 12:44:18] [adwsapi_v2.php:36]  Provided signature: sha256=83c800d4431cbc9091f6e79aff396312ac94b352b2811ecd107540b557c78081
[webhook] [2025-08-01 12:44:18] [adwsapi_v2.php:37]  Calculated signature: sha256=e42924214a1b54af81a7147d56b83c2cc430fd08c5f32122cbe199b223bb9c0b
[webhook] [2025-08-01 12:44:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ab8f068c045e9702\n    [X-B3-Traceid] => 688cb69f36c928b21ed636bc0d0590ac\n    [B3] => 688cb69f36c928b21ed636bc0d0590ac-ab8f068c045e9702-1\n    [Traceparent] => 00-688cb69f36c928b21ed636bc0d0590ac-ab8f068c045e9702-01\n    [X-Amzn-Trace-Id] => Root=1-688cb69f-36c928b21ed636bc0d0590ac;Parent=ab8f068c045e9702;Sampled=1\n    [X-Adsk-Signature] => sha256=83c800d4431cbc9091f6e79aff396312ac94b352b2811ecd107540b557c78081\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 413e7f3f-28ad-46b9-a110-221ee27d690c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:44:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"413e7f3f-28ad-46b9-a110-221ee27d690c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59861408333647","status":"Active","quantity":1,"endDate":"2026-08-27","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-01T12:29:13.000+0000"},"publishedAt":"2025-08-01T12:44:15.000Z","csn":"5103159758"}
[webhook] [2025-08-01 12:52:57] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 12:52:57
[webhook] [2025-08-01 12:52:57] [adwsapi_v2.php:36]  Provided signature: sha256=6a502533b6e29b975d39ef3c2c0eb1b780217472fd301434a05754a94f315309
[webhook] [2025-08-01 12:52:57] [adwsapi_v2.php:37]  Calculated signature: sha256=637fc1db2156489b762b2e4e882ae33c737f9bf9683a1bd11395050058c1aa37
[webhook] [2025-08-01 12:52:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7df9b0a3ec62b361\n    [X-B3-Traceid] => 688cb8a7ffc3e9e0b27af6bf52e2c498\n    [B3] => 688cb8a7ffc3e9e0b27af6bf52e2c498-7df9b0a3ec62b361-1\n    [Traceparent] => 00-688cb8a7ffc3e9e0b27af6bf52e2c498-7df9b0a3ec62b361-01\n    [X-Amzn-Trace-Id] => Root=1-688cb8a7-ffc3e9e0b27af6bf52e2c498;Parent=7df9b0a3ec62b361;Sampled=1\n    [X-Adsk-Signature] => sha256=6a502533b6e29b975d39ef3c2c0eb1b780217472fd301434a05754a94f315309\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e25bee6e-076b-45bf-a5f7-fdb18d8f2258\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 12:52:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"e25bee6e-076b-45bf-a5f7-fdb18d8f2258","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976151","transactionId":"75ffed50-2ebb-5be3-8700-8c56946e23e8","quoteStatus":"Draft","message":"Quote# Q-976151 status changed to Draft.","modifiedAt":"2025-08-01T12:52:54.838Z"},"publishedAt":"2025-08-01T12:52:55.000Z","csn":"5103159758"}
[webhook] [2025-08-01 13:17:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 13:17:58
[webhook] [2025-08-01 13:17:58] [adwsapi_v2.php:36]  Provided signature: sha256=154d478e54642aae03d3c807234cda7acdde22b6317612764a3010233f98820f
[webhook] [2025-08-01 13:17:58] [adwsapi_v2.php:37]  Calculated signature: sha256=4321574ee2e1e85931008326715774402abb993f28da6e93047011509ef32ef4
[webhook] [2025-08-01 13:17:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f667ecdeae32a599\n    [X-B3-Traceid] => 688cbe83615594d79f2999ab5a80410d\n    [B3] => 688cbe83615594d79f2999ab5a80410d-f667ecdeae32a599-1\n    [Traceparent] => 00-688cbe83615594d79f2999ab5a80410d-f667ecdeae32a599-01\n    [X-Amzn-Trace-Id] => Root=1-688cbe83-615594d79f2999ab5a80410d;Parent=f667ecdeae32a599;Sampled=1\n    [X-Adsk-Signature] => sha256=154d478e54642aae03d3c807234cda7acdde22b6317612764a3010233f98820f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 76a24d2e-8be3-49ea-a559-271a6a0e2160\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 13:17:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"76a24d2e-8be3-49ea-a559-271a6a0e2160","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-929414","transactionId":"81b36726-663d-540b-97d9-07c97ddb7acc","quoteStatus":"Order Submitted","message":"Quote# Q-929414 status changed to Order Submitted.","modifiedAt":"2025-08-01T13:17:55.375Z"},"publishedAt":"2025-08-01T13:17:55.000Z","csn":"5103159758"}
[webhook] [2025-08-01 13:18:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 13:18:00
[webhook] [2025-08-01 13:18:00] [adwsapi_v2.php:36]  Provided signature: sha256=c91a15ff032130708e40b45f1c9a1fd9b555786319428247b4d72017adafff90
[webhook] [2025-08-01 13:18:00] [adwsapi_v2.php:37]  Calculated signature: sha256=e0275cf01bd5f2cc1bd56d41f2d998e45cca470979496842879b355c5629e23e
[webhook] [2025-08-01 13:18:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 077ddb54b5820548\n    [X-B3-Traceid] => 688cbe8652005f3882f4b2d57d4d08f0\n    [B3] => 688cbe8652005f3882f4b2d57d4d08f0-077ddb54b5820548-1\n    [Traceparent] => 00-688cbe8652005f3882f4b2d57d4d08f0-077ddb54b5820548-01\n    [X-Amzn-Trace-Id] => Root=1-688cbe86-52005f3882f4b2d57d4d08f0;Parent=077ddb54b5820548;Sampled=1\n    [X-Adsk-Signature] => sha256=c91a15ff032130708e40b45f1c9a1fd9b555786319428247b4d72017adafff90\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6d2fd80b-84f2-4910-bb3e-9f8bab93f292\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 13:18:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"6d2fd80b-84f2-4910-bb3e-9f8bab93f292","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-929414","transactionId":"81b36726-663d-540b-97d9-07c97ddb7acc","quoteStatus":"Ordered","message":"Quote# Q-929414 status changed to Ordered.","modifiedAt":"2025-08-01T13:17:57.757Z"},"publishedAt":"2025-08-01T13:17:58.000Z","csn":"5103159758"}
[webhook] [2025-08-01 13:38:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 13:38:07
[webhook] [2025-08-01 13:38:07] [adwsapi_v2.php:36]  Provided signature: sha256=e64a7cab3d4e7d25887f77bbba19b2f661a099dbfcda9e7880611d38c6d0113d
[webhook] [2025-08-01 13:38:07] [adwsapi_v2.php:37]  Calculated signature: sha256=a3c5b868195fffb9fc031acbb067562448e2a797902dc373d974175e60858014
[webhook] [2025-08-01 13:38:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b97b14dae7c114e8\n    [X-B3-Traceid] => 688cc33d0d093cc77194522e6d07a572\n    [B3] => 688cc33d0d093cc77194522e6d07a572-b97b14dae7c114e8-1\n    [Traceparent] => 00-688cc33d0d093cc77194522e6d07a572-b97b14dae7c114e8-01\n    [X-Amzn-Trace-Id] => Root=1-688cc33d-0d093cc77194522e6d07a572;Parent=b97b14dae7c114e8;Sampled=1\n    [X-Adsk-Signature] => sha256=e64a7cab3d4e7d25887f77bbba19b2f661a099dbfcda9e7880611d38c6d0113d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1a900fa6-d4f9-4dd8-9811-9040b9352a3d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 13:38:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"1a900fa6-d4f9-4dd8-9811-9040b9352a3d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56577398248377","status":"Active","quantity":4,"endDate":"2026-08-13","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-01T13:18:02.000+0000"},"publishedAt":"2025-08-01T13:38:05.000Z","csn":"5103159758"}
[webhook] [2025-08-01 13:56:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 13:56:25
[webhook] [2025-08-01 13:56:25] [adwsapi_v2.php:36]  Provided signature: sha256=e7751f089bf4f2a75c82146e8666ec3675dd1319321b1d26593a9444f18edabf
[webhook] [2025-08-01 13:56:25] [adwsapi_v2.php:37]  Calculated signature: sha256=a1b42cdc01e487f8b70a16051c0175b0ce81a662d65432e7b2dfdad7550732f7
[webhook] [2025-08-01 13:56:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e80400412d824e33\n    [X-B3-Traceid] => 688cc7869391d2bd7d63f055c3c5a0ee\n    [B3] => 688cc7869391d2bd7d63f055c3c5a0ee-e80400412d824e33-1\n    [Traceparent] => 00-688cc7869391d2bd7d63f055c3c5a0ee-e80400412d824e33-01\n    [X-Amzn-Trace-Id] => Root=1-688cc786-9391d2bd7d63f055c3c5a0ee;Parent=e80400412d824e33;Sampled=1\n    [X-Adsk-Signature] => sha256=e7751f089bf4f2a75c82146e8666ec3675dd1319321b1d26593a9444f18edabf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 05541fb9-4359-4dbd-a691-cb5e8e106a76\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 13:56:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"05541fb9-4359-4dbd-a691-cb5e8e106a76","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976696","transactionId":"f3e71575-8ad8-5ff4-9ef1-7473436e6dfa","quoteStatus":"Draft","message":"Quote# Q-976696 status changed to Draft.","modifiedAt":"2025-08-01T13:56:22.789Z"},"publishedAt":"2025-08-01T13:56:23.000Z","csn":"5103159758"}
[webhook] [2025-08-01 13:57:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 13:57:25
[webhook] [2025-08-01 13:57:25] [adwsapi_v2.php:36]  Provided signature: sha256=ced35ea1f79dc93e9f6f7a8daca69644f4e00786aa053c42ded04a573e4ec4df
[webhook] [2025-08-01 13:57:25] [adwsapi_v2.php:37]  Calculated signature: sha256=aa84aa58ddbd8305f402404632dfe50dc7cfac61744d7a243d569470131050a6
[webhook] [2025-08-01 13:57:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 153df79ff74572b0\n    [X-B3-Traceid] => 688cc7c28d68dea1d4bb302c739e2a64\n    [B3] => 688cc7c28d68dea1d4bb302c739e2a64-153df79ff74572b0-1\n    [Traceparent] => 00-688cc7c28d68dea1d4bb302c739e2a64-153df79ff74572b0-01\n    [X-Amzn-Trace-Id] => Root=1-688cc7c2-8d68dea1d4bb302c739e2a64;Parent=153df79ff74572b0;Sampled=1\n    [X-Adsk-Signature] => sha256=ced35ea1f79dc93e9f6f7a8daca69644f4e00786aa053c42ded04a573e4ec4df\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 39cfb55d-2655-4f35-bb8c-5fdc41234ce4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 13:57:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"39cfb55d-2655-4f35-bb8c-5fdc41234ce4","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976696","transactionId":"f3e71575-8ad8-5ff4-9ef1-7473436e6dfa","quoteStatus":"Quoted","message":"Quote# Q-976696 status changed to Quoted.","modifiedAt":"2025-08-01T13:57:22.475Z"},"publishedAt":"2025-08-01T13:57:22.000Z","csn":"5103159758"}
[webhook] [2025-08-01 15:36:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 15:36:46
[webhook] [2025-08-01 15:36:46] [adwsapi_v2.php:36]  Provided signature: sha256=242861b5f6bdf0153ff5d712df2c45a6046f0aea634730d148600dfdd875ef08
[webhook] [2025-08-01 15:36:46] [adwsapi_v2.php:37]  Calculated signature: sha256=8d313fc997dd28c65a7ddc5ca14e4fe5fd5c9f73efdc1ea8e313bc5742b154fc
[webhook] [2025-08-01 15:36:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 60bf6a77595625eb\n    [X-B3-Traceid] => 688cdf0c597b3fb3225a2f921eb1236f\n    [B3] => 688cdf0c597b3fb3225a2f921eb1236f-60bf6a77595625eb-1\n    [Traceparent] => 00-688cdf0c597b3fb3225a2f921eb1236f-60bf6a77595625eb-01\n    [X-Amzn-Trace-Id] => Root=1-688cdf0c-597b3fb3225a2f921eb1236f;Parent=60bf6a77595625eb;Sampled=1\n    [X-Adsk-Signature] => sha256=242861b5f6bdf0153ff5d712df2c45a6046f0aea634730d148600dfdd875ef08\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0dc8b960-aed2-473f-a9b9-3a19b728e1bb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 15:36:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"0dc8b960-aed2-473f-a9b9-3a19b728e1bb","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59861408333647","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T15:06:20.000+0000"},"publishedAt":"2025-08-01T15:36:44.000Z","csn":"5103159758"}
[webhook] [2025-08-01 15:40:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 15:40:50
[webhook] [2025-08-01 15:40:50] [adwsapi_v2.php:36]  Provided signature: sha256=d64f587f545d8da52193e121f96bcc4fe80a793a25cd20d87edc74828a69b6e2
[webhook] [2025-08-01 15:40:50] [adwsapi_v2.php:37]  Calculated signature: sha256=03f85deb495c174258bf46ea2c4cd6576613adadb71cdf504eaf3cb6f7a27d61
[webhook] [2025-08-01 15:40:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d6af25e7fe57b92b\n    [X-B3-Traceid] => 688cdfff0fddf5a520a1bc237052c050\n    [B3] => 688cdfff0fddf5a520a1bc237052c050-d6af25e7fe57b92b-1\n    [Traceparent] => 00-688cdfff0fddf5a520a1bc237052c050-d6af25e7fe57b92b-01\n    [X-Amzn-Trace-Id] => Root=1-688cdfff-0fddf5a520a1bc237052c050;Parent=d6af25e7fe57b92b;Sampled=1\n    [X-Adsk-Signature] => sha256=d64f587f545d8da52193e121f96bcc4fe80a793a25cd20d87edc74828a69b6e2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1a38d6a0-8ce6-4d9b-839f-cb8171444e49\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 15:40:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"1a38d6a0-8ce6-4d9b-839f-cb8171444e49","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56577398248377","quantity":4,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T15:05:41.000+0000"},"publishedAt":"2025-08-01T15:40:47.000Z","csn":"5103159758"}
[webhook] [2025-08-01 15:40:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 15:40:59
[webhook] [2025-08-01 15:40:59] [adwsapi_v2.php:36]  Provided signature: sha256=e9003a36c44d5ae6c9f392ad67a380ea52c26ef616f4bc00c81d674075a289e3
[webhook] [2025-08-01 15:40:59] [adwsapi_v2.php:37]  Calculated signature: sha256=e642dad13738b73071fb88083ca6110cf21f2cf7a5ff6ad07157d2015b290eb6
[webhook] [2025-08-01 15:40:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 21527a6b11cb1623\n    [X-B3-Traceid] => 688ce00928dc7736540542cf24da0b14\n    [B3] => 688ce00928dc7736540542cf24da0b14-21527a6b11cb1623-1\n    [Traceparent] => 00-688ce00928dc7736540542cf24da0b14-21527a6b11cb1623-01\n    [X-Amzn-Trace-Id] => Root=1-688ce009-28dc7736540542cf24da0b14;Parent=21527a6b11cb1623;Sampled=1\n    [X-Adsk-Signature] => sha256=e9003a36c44d5ae6c9f392ad67a380ea52c26ef616f4bc00c81d674075a289e3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 44f5ccba-42ae-40ca-ad04-01bafaac1fcf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 15:40:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"44f5ccba-42ae-40ca-ad04-01bafaac1fcf","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56700593929570","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T15:05:51.000+0000"},"publishedAt":"2025-08-01T15:40:57.000Z","csn":"5103159758"}
[webhook] [2025-08-01 15:41:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 15:41:06
[webhook] [2025-08-01 15:41:06] [adwsapi_v2.php:36]  Provided signature: sha256=09ba2ab02e15e2d1795b51ff004b984d03667cf61054f355015e443ddcfbdb51
[webhook] [2025-08-01 15:41:06] [adwsapi_v2.php:37]  Calculated signature: sha256=4e898ae3a477828960c9636cd390f7077cd17dcbef8de0c3800f3967be593033
[webhook] [2025-08-01 15:41:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2232f4c87b05f54d\n    [X-B3-Traceid] => 688ce0102dadcf102f7c9cc02dda1aa8\n    [B3] => 688ce0102dadcf102f7c9cc02dda1aa8-2232f4c87b05f54d-1\n    [Traceparent] => 00-688ce0102dadcf102f7c9cc02dda1aa8-2232f4c87b05f54d-01\n    [X-Amzn-Trace-Id] => Root=1-688ce010-2dadcf102f7c9cc02dda1aa8;Parent=2232f4c87b05f54d;Sampled=1\n    [X-Adsk-Signature] => sha256=09ba2ab02e15e2d1795b51ff004b984d03667cf61054f355015e443ddcfbdb51\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7c9c1c9d-4bf5-4e24-83d5-314272282cef\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 15:41:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"7c9c1c9d-4bf5-4e24-83d5-314272282cef","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56716427656767","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T15:05:51.000+0000"},"publishedAt":"2025-08-01T15:41:04.000Z","csn":"5103159758"}
[webhook] [2025-08-01 18:10:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 18:10:10
[webhook] [2025-08-01 18:10:10] [adwsapi_v2.php:36]  Provided signature: sha256=0a07559a09caee83ebc39d4c58c706f0a82043c23bde5463af2b04353cd1a04b
[webhook] [2025-08-01 18:10:10] [adwsapi_v2.php:37]  Calculated signature: sha256=bd4fb5186d952b74dd244a9baa0f0fe766c04bd56631678ad207c85a720e7cbe
[webhook] [2025-08-01 18:10:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fe227220fc7891ad\n    [X-B3-Traceid] => 688d02ff152b31a46c17fd21409c4779\n    [B3] => 688d02ff152b31a46c17fd21409c4779-fe227220fc7891ad-1\n    [Traceparent] => 00-688d02ff152b31a46c17fd21409c4779-fe227220fc7891ad-01\n    [X-Amzn-Trace-Id] => Root=1-688d02ff-152b31a46c17fd21409c4779;Parent=fe227220fc7891ad;Sampled=1\n    [X-Adsk-Signature] => sha256=0a07559a09caee83ebc39d4c58c706f0a82043c23bde5463af2b04353cd1a04b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 039a06f6-f2eb-46ed-aa5b-790b836f6c47\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 18:10:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"039a06f6-f2eb-46ed-aa5b-790b836f6c47","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56533871230169","status":"Active","quantity":1,"endDate":"2028-08-08","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-01T17:40:05.000+0000"},"publishedAt":"2025-08-01T18:10:07.000Z","csn":"5103159758"}
[webhook] [2025-08-01 19:36:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 19:36:35
[webhook] [2025-08-01 19:36:35] [adwsapi_v2.php:36]  Provided signature: sha256=c7850e56987c816d9fef4a143422e246a5db587570fe9a2eec277b50a7b504ac
[webhook] [2025-08-01 19:36:35] [adwsapi_v2.php:37]  Calculated signature: sha256=41b9df1b3b7dfdd45c77a2a09d2bfa2efdcf00d43990e7e24b473fcc54c281e8
[webhook] [2025-08-01 19:36:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b1ab3975e0189ba5\n    [X-B3-Traceid] => 688d17415de5be8256b798b66dccf5c7\n    [B3] => 688d17415de5be8256b798b66dccf5c7-b1ab3975e0189ba5-1\n    [Traceparent] => 00-688d17415de5be8256b798b66dccf5c7-b1ab3975e0189ba5-01\n    [X-Amzn-Trace-Id] => Root=1-688d1741-5de5be8256b798b66dccf5c7;Parent=b1ab3975e0189ba5;Sampled=1\n    [X-Adsk-Signature] => sha256=c7850e56987c816d9fef4a143422e246a5db587570fe9a2eec277b50a7b504ac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e1f17dda-42e9-4d4b-965a-f2431cdbdcbc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 19:36:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"e1f17dda-42e9-4d4b-965a-f2431cdbdcbc","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56533871230169","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-01T19:11:23.000+0000"},"publishedAt":"2025-08-01T19:36:33.000Z","csn":"5103159758"}
[webhook] [2025-08-01 20:03:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 20:03:03
[webhook] [2025-08-01 20:03:03] [adwsapi_v2.php:36]  Provided signature: sha256=0237ed519c6a2f2d55b82059faa1c711243002366999f9555cf3c6157adfaf8d
[webhook] [2025-08-01 20:03:03] [adwsapi_v2.php:37]  Calculated signature: sha256=e5159d0f3c26569cb72ca2f850ac420a912af7bd5b4905d8242e04b7a3468cf9
[webhook] [2025-08-01 20:03:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 41745017d54be040\n    [X-B3-Traceid] => 688d1d742562f1e33003a9502bfe6355\n    [B3] => 688d1d742562f1e33003a9502bfe6355-41745017d54be040-1\n    [Traceparent] => 00-688d1d742562f1e33003a9502bfe6355-41745017d54be040-01\n    [X-Amzn-Trace-Id] => Root=1-688d1d74-2562f1e33003a9502bfe6355;Parent=41745017d54be040;Sampled=1\n    [X-Adsk-Signature] => sha256=0237ed519c6a2f2d55b82059faa1c711243002366999f9555cf3c6157adfaf8d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754078580475-56716427656767\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 20:03:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754078580475-56716427656767","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56716427656767","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T20:03:00.475Z"},"publishedAt":"2025-08-01T20:03:00.000Z","csn":"5103159758"}
[webhook] [2025-08-01 20:06:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 20:06:37
[webhook] [2025-08-01 20:06:37] [adwsapi_v2.php:36]  Provided signature: sha256=40b25659de8ca7daee89720312a20e351bab1ba9df9483679aadb810d6a39833
[webhook] [2025-08-01 20:06:37] [adwsapi_v2.php:37]  Calculated signature: sha256=ed26e5dcf1a19f6418f9c98c031f96aa04ee19f8b50a6ecdd963e11a84f1715c
[webhook] [2025-08-01 20:06:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6e7362a6c81f4876\n    [X-B3-Traceid] => 688d1e4a090064c7233b225d7a3facc6\n    [B3] => 688d1e4a090064c7233b225d7a3facc6-6e7362a6c81f4876-1\n    [Traceparent] => 00-688d1e4a090064c7233b225d7a3facc6-6e7362a6c81f4876-01\n    [X-Amzn-Trace-Id] => Root=1-688d1e4a-090064c7233b225d7a3facc6;Parent=6e7362a6c81f4876;Sampled=1\n    [X-Adsk-Signature] => sha256=40b25659de8ca7daee89720312a20e351bab1ba9df9483679aadb810d6a39833\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754078794843-56700593929570\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 20:06:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754078794843-56700593929570","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56700593929570","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T20:06:34.844Z"},"publishedAt":"2025-08-01T20:06:34.000Z","csn":"5103159758"}
[webhook] [2025-08-01 20:09:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 20:09:17
[webhook] [2025-08-01 20:09:17] [adwsapi_v2.php:36]  Provided signature: sha256=382593b33b5de3aca053de4da3036732a9f63868705780fd8b1199791f924d77
[webhook] [2025-08-01 20:09:17] [adwsapi_v2.php:37]  Calculated signature: sha256=7d8e5d83d6f397d2642aa196d6e7762556379fba70d808ad8c9022bd6ad47887
[webhook] [2025-08-01 20:09:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d6dff6145095e345\n    [X-B3-Traceid] => 688d1eea03040d7574d44dd14b1c2d93\n    [B3] => 688d1eea03040d7574d44dd14b1c2d93-d6dff6145095e345-1\n    [Traceparent] => 00-688d1eea03040d7574d44dd14b1c2d93-d6dff6145095e345-01\n    [X-Amzn-Trace-Id] => Root=1-688d1eea-03040d7574d44dd14b1c2d93;Parent=d6dff6145095e345;Sampled=1\n    [X-Adsk-Signature] => sha256=382593b33b5de3aca053de4da3036732a9f63868705780fd8b1199791f924d77\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754078954621-59861408333647\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 20:09:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754078954621-59861408333647","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"59861408333647","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T20:09:14.621Z"},"publishedAt":"2025-08-01T20:09:14.000Z","csn":"5103159758"}
[webhook] [2025-08-01 20:14:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 20:14:45
[webhook] [2025-08-01 20:14:45] [adwsapi_v2.php:36]  Provided signature: sha256=c47d2ccd87a34a57ebe015d8c63e81b438761e4ef237ec9375147c158ef408c1
[webhook] [2025-08-01 20:14:45] [adwsapi_v2.php:37]  Calculated signature: sha256=70ae318e6ed56de4e973ae46d9f374e9a3334ecb79783862f1b0ddb34cea3c25
[webhook] [2025-08-01 20:14:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f0f6e64da363d319\n    [X-B3-Traceid] => 688d20336fe88c2f0db6eb33182a4a10\n    [B3] => 688d20336fe88c2f0db6eb33182a4a10-f0f6e64da363d319-1\n    [Traceparent] => 00-688d20336fe88c2f0db6eb33182a4a10-f0f6e64da363d319-01\n    [X-Amzn-Trace-Id] => Root=1-688d2033-6fe88c2f0db6eb33182a4a10;Parent=f0f6e64da363d319;Sampled=1\n    [X-Adsk-Signature] => sha256=c47d2ccd87a34a57ebe015d8c63e81b438761e4ef237ec9375147c158ef408c1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754079283184-56577398248377\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 20:14:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754079283184-56577398248377","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56577398248377","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T20:14:43.184Z"},"publishedAt":"2025-08-01T20:14:43.000Z","csn":"5103159758"}
[webhook] [2025-08-01 22:03:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 22:03:24
[webhook] [2025-08-01 22:03:24] [adwsapi_v2.php:36]  Provided signature: sha256=3943044cfc4b71aa4996b193da5e8604c558d3a29bee1e31eba23c3f3d0dc25f
[webhook] [2025-08-01 22:03:24] [adwsapi_v2.php:37]  Calculated signature: sha256=b355c58080c97397816111ad1c3f79b85408d613068a09d79b30264f77fb9a1a
[webhook] [2025-08-01 22:03:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f0cd88f0b1d4e1dd\n    [X-B3-Traceid] => 688d39aa0f5fef3d1a4475bb3e65866f\n    [B3] => 688d39aa0f5fef3d1a4475bb3e65866f-f0cd88f0b1d4e1dd-1\n    [Traceparent] => 00-688d39aa0f5fef3d1a4475bb3e65866f-f0cd88f0b1d4e1dd-01\n    [X-Amzn-Trace-Id] => Root=1-688d39aa-0f5fef3d1a4475bb3e65866f;Parent=f0cd88f0b1d4e1dd;Sampled=1\n    [X-Adsk-Signature] => sha256=3943044cfc4b71aa4996b193da5e8604c558d3a29bee1e31eba23c3f3d0dc25f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754085802209-56533871230169\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 22:03:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754085802209-56533871230169","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56533871230169","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-01T22:03:22.209Z"},"publishedAt":"2025-08-01T22:03:22.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:00:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:00:47
[webhook] [2025-08-01 23:00:47] [adwsapi_v2.php:36]  Provided signature: sha256=920551988d0be6a6a38478cba3a88b599103544e7e6805607950a751a743785e
[webhook] [2025-08-01 23:00:47] [adwsapi_v2.php:37]  Calculated signature: sha256=17c817bbd2c35a4585c9624cd08509bba22e6c624ac5c7da553275928e0e95d9
[webhook] [2025-08-01 23:00:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 59cb4cf8096ac394\n    [X-B3-Traceid] => 688d471d140636f378f74c4e284ffc73\n    [B3] => 688d471d140636f378f74c4e284ffc73-59cb4cf8096ac394-1\n    [Traceparent] => 00-688d471d140636f378f74c4e284ffc73-59cb4cf8096ac394-01\n    [X-Amzn-Trace-Id] => Root=1-688d471d-140636f378f74c4e284ffc73;Parent=59cb4cf8096ac394;Sampled=1\n    [X-Adsk-Signature] => sha256=920551988d0be6a6a38478cba3a88b599103544e7e6805607950a751a743785e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d33db6b6-11d0-4c84-a8af-da855219ae48\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:00:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"d33db6b6-11d0-4c84-a8af-da855219ae48","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901307","transactionId":"39ecfad0-b4c2-54c2-89a9-9a0f867dac66","quoteStatus":"Expired","message":"Quote# Q-901307 status changed to Expired.","modifiedAt":"2025-08-01T23:00:44.556Z"},"publishedAt":"2025-08-01T23:00:45.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:00:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:00:56
[webhook] [2025-08-01 23:00:56] [adwsapi_v2.php:36]  Provided signature: sha256=e2ebc8ff834c0bf40f5f6029ed4e575ff6489bb86a526ec89296f06238efc772
[webhook] [2025-08-01 23:00:56] [adwsapi_v2.php:37]  Calculated signature: sha256=8f8118565059f146de877415d85509652e38d5ee46ef0ae0bb72b41e3a6b66a3
[webhook] [2025-08-01 23:00:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 157d27b2b8b06e95\n    [X-B3-Traceid] => 688d4726366ba5d1f858c549658b9792\n    [B3] => 688d4726366ba5d1f858c549658b9792-157d27b2b8b06e95-1\n    [Traceparent] => 00-688d4726366ba5d1f858c549658b9792-157d27b2b8b06e95-01\n    [X-Amzn-Trace-Id] => Root=1-688d4726-366ba5d1f858c549658b9792;Parent=157d27b2b8b06e95;Sampled=1\n    [X-Adsk-Signature] => sha256=e2ebc8ff834c0bf40f5f6029ed4e575ff6489bb86a526ec89296f06238efc772\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8ee010f2-b5a4-457b-898f-0bc0df47810b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:00:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"8ee010f2-b5a4-457b-898f-0bc0df47810b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901803","transactionId":"3ed1a33f-5eee-50ab-b3f6-c3e012cf89d8","quoteStatus":"Expired","message":"Quote# Q-901803 status changed to Expired.","modifiedAt":"2025-08-01T23:00:49.334Z"},"publishedAt":"2025-08-01T23:00:54.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:01:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:01:08
[webhook] [2025-08-01 23:01:08] [adwsapi_v2.php:36]  Provided signature: sha256=9d3e41c60949c2926f92f35dcc581d2090fe20cac9b87433a34a70011570cf4d
[webhook] [2025-08-01 23:01:08] [adwsapi_v2.php:37]  Calculated signature: sha256=2e624cc827aa4b6dcc19cb52b4eff2f5032fd8a282e4753183b15d4778d74c2c
[webhook] [2025-08-01 23:01:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 88f484ec43aea78e\n    [X-B3-Traceid] => 688d4732f5ee0aa1637e921705e07fab\n    [B3] => 688d4732f5ee0aa1637e921705e07fab-88f484ec43aea78e-1\n    [Traceparent] => 00-688d4732f5ee0aa1637e921705e07fab-88f484ec43aea78e-01\n    [X-Amzn-Trace-Id] => Root=1-688d4732-f5ee0aa1637e921705e07fab;Parent=88f484ec43aea78e;Sampled=1\n    [X-Adsk-Signature] => sha256=9d3e41c60949c2926f92f35dcc581d2090fe20cac9b87433a34a70011570cf4d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8530ec68-edd0-42d5-aa2c-bcfa872d52da\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:01:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"8530ec68-edd0-42d5-aa2c-bcfa872d52da","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901815","transactionId":"e946fefe-2964-5a8e-bbf4-9e2165fe6017","quoteStatus":"Expired","message":"Quote# Q-901815 status changed to Expired.","modifiedAt":"2025-08-01T23:00:57.561Z"},"publishedAt":"2025-08-01T23:01:06.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:01:09
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:36]  Provided signature: sha256=226263ff49c1375ef02d3e0729ca474d690e5b8fe2386133fbc88840fdba0507
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:37]  Calculated signature: sha256=53fe543b51a4e99aa17443ed9c5a5ea186232b0b3627d7c5f5924a8bb404e2de
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 99a5f3293d0096fd\n    [X-B3-Traceid] => 688d4733d65a772adf6714f3da795f1c\n    [B3] => 688d4733d65a772adf6714f3da795f1c-99a5f3293d0096fd-1\n    [Traceparent] => 00-688d4733d65a772adf6714f3da795f1c-99a5f3293d0096fd-01\n    [X-Amzn-Trace-Id] => Root=1-688d4733-d65a772adf6714f3da795f1c;Parent=99a5f3293d0096fd;Sampled=1\n    [X-Adsk-Signature] => sha256=226263ff49c1375ef02d3e0729ca474d690e5b8fe2386133fbc88840fdba0507\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ecc2d3f2-b8d3-4572-ae50-6262e117fbdb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"ecc2d3f2-b8d3-4572-ae50-6262e117fbdb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901279","transactionId":"35aa88d7-30a8-5af7-8fce-e80801b0afd5","quoteStatus":"Expired","message":"Quote# Q-901279 status changed to Expired.","modifiedAt":"2025-08-01T23:00:54.268Z"},"publishedAt":"2025-08-01T23:01:07.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:01:09
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:36]  Provided signature: sha256=ba69c7d5822da92d92af833798373c2dedc6ca7984f5bac0ae432562107ebf4b
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:37]  Calculated signature: sha256=3a851126adc90c1a798503f320994ecbda208755a8369765d23d184157105a15
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 68778430712e4e44\n    [X-B3-Traceid] => 688d4733a756f1832520fa85d622ef07\n    [B3] => 688d4733a756f1832520fa85d622ef07-68778430712e4e44-1\n    [Traceparent] => 00-688d4733a756f1832520fa85d622ef07-68778430712e4e44-01\n    [X-Amzn-Trace-Id] => Root=1-688d4733-a756f1832520fa85d622ef07;Parent=68778430712e4e44;Sampled=1\n    [X-Adsk-Signature] => sha256=ba69c7d5822da92d92af833798373c2dedc6ca7984f5bac0ae432562107ebf4b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8c0d0fc1-d9fc-4818-9f96-6d7dbc82e5d3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:01:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"8c0d0fc1-d9fc-4818-9f96-6d7dbc82e5d3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901541","transactionId":"da0a2fc7-ddd2-5528-bfd5-4f18e06e1d0c","quoteStatus":"Expired","message":"Quote# Q-901541 status changed to Expired.","modifiedAt":"2025-08-01T23:00:57.747Z"},"publishedAt":"2025-08-01T23:01:07.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:01:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:01:10
[webhook] [2025-08-01 23:01:10] [adwsapi_v2.php:36]  Provided signature: sha256=cefd012c397a5a445186e512d0f17ef9b4a4291122248a19ada9e0a4c7d77460
[webhook] [2025-08-01 23:01:10] [adwsapi_v2.php:37]  Calculated signature: sha256=b1784900bf022a5c4330886e848c8bdd19f71492e0264eda7c2c0f218d5ee8ed
[webhook] [2025-08-01 23:01:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4b1d25fb49113575\n    [X-B3-Traceid] => 688d47339ed858af11e4a3a980679beb\n    [B3] => 688d47339ed858af11e4a3a980679beb-4b1d25fb49113575-1\n    [Traceparent] => 00-688d47339ed858af11e4a3a980679beb-4b1d25fb49113575-01\n    [X-Amzn-Trace-Id] => Root=1-688d4733-9ed858af11e4a3a980679beb;Parent=4b1d25fb49113575;Sampled=1\n    [X-Adsk-Signature] => sha256=cefd012c397a5a445186e512d0f17ef9b4a4291122248a19ada9e0a4c7d77460\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3b14dfa5-62bf-4e3d-9526-4358a4d98d15\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:01:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"3b14dfa5-62bf-4e3d-9526-4358a4d98d15","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901971","transactionId":"b0a8343b-6b4a-54fb-92db-54e43cda7f22","quoteStatus":"Expired","message":"Quote# Q-901971 status changed to Expired.","modifiedAt":"2025-08-01T23:00:54.579Z"},"publishedAt":"2025-08-01T23:01:08.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:01:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:01:13
[webhook] [2025-08-01 23:01:13] [adwsapi_v2.php:36]  Provided signature: sha256=5066679796186c19f5c528f8d48d88c3de3b62da9a570f74cac8eb87184068b5
[webhook] [2025-08-01 23:01:13] [adwsapi_v2.php:37]  Calculated signature: sha256=2eac0a33d8829ef22d05ad25f03ef19469bc637d483f1a3a394e0b8ce730046c
[webhook] [2025-08-01 23:01:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0952597b074e7246\n    [X-B3-Traceid] => 688d4736e5f5d72069db3b64b064d9a5\n    [B3] => 688d4736e5f5d72069db3b64b064d9a5-0952597b074e7246-1\n    [Traceparent] => 00-688d4736e5f5d72069db3b64b064d9a5-0952597b074e7246-01\n    [X-Amzn-Trace-Id] => Root=1-688d4736-e5f5d72069db3b64b064d9a5;Parent=0952597b074e7246;Sampled=1\n    [X-Adsk-Signature] => sha256=5066679796186c19f5c528f8d48d88c3de3b62da9a570f74cac8eb87184068b5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 730d9a3e-b66f-4056-98fc-a84b6f023232\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:01:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"730d9a3e-b66f-4056-98fc-a84b6f023232","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-901736","transactionId":"0251d32b-f3b8-58c5-93fa-b3f4bbcdc6fc","quoteStatus":"Expired","message":"Quote# Q-901736 status changed to Expired.","modifiedAt":"2025-08-01T23:00:57.456Z"},"publishedAt":"2025-08-01T23:01:11.000Z","csn":"5103159758"}
[webhook] [2025-08-01 23:01:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-01 23:01:43
[webhook] [2025-08-01 23:01:43] [adwsapi_v2.php:36]  Provided signature: sha256=a6566de485de3b83a1ba2f2eaf3b4dd8a0520a9580cfb2262aac420f1a772358
[webhook] [2025-08-01 23:01:43] [adwsapi_v2.php:37]  Calculated signature: sha256=002e4235a605166a5c8de16ce9eb96ed1fd0c80837ae91733f06402b33a6b9fd
[webhook] [2025-08-01 23:01:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 509eae519a17ed6b\n    [X-B3-Traceid] => 688d4755676985f262edfafd390227e1\n    [B3] => 688d4755676985f262edfafd390227e1-509eae519a17ed6b-1\n    [Traceparent] => 00-688d4755676985f262edfafd390227e1-509eae519a17ed6b-01\n    [X-Amzn-Trace-Id] => Root=1-688d4755-676985f262edfafd390227e1;Parent=509eae519a17ed6b;Sampled=1\n    [X-Adsk-Signature] => sha256=a6566de485de3b83a1ba2f2eaf3b4dd8a0520a9580cfb2262aac420f1a772358\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754089301238-574-09838098\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-01 23:01:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754089301238-574-09838098","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-09838098","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-01T23:01:41.238Z"},"publishedAt":"2025-08-01T23:01:41.000Z","csn":"5103159758"}
[webhook] [2025-08-02 00:00:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 00:00:59
[webhook] [2025-08-02 00:00:59] [adwsapi_v2.php:36]  Provided signature: sha256=c9976a64d1b254d0656a0a791f285cae7390f4042b4b366d259a0d15c497b272
[webhook] [2025-08-02 00:00:59] [adwsapi_v2.php:37]  Calculated signature: sha256=0c566e5d65841865ae1821343c269948792ed50d5113070acd8d9a649e7cc411
[webhook] [2025-08-02 00:00:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6e94abb3213c4e8b\n    [X-B3-Traceid] => 688d55386aa4862e3f44eb917b145096\n    [B3] => 688d55386aa4862e3f44eb917b145096-6e94abb3213c4e8b-1\n    [Traceparent] => 00-688d55386aa4862e3f44eb917b145096-6e94abb3213c4e8b-01\n    [X-Amzn-Trace-Id] => Root=1-688d5538-6aa4862e3f44eb917b145096;Parent=6e94abb3213c4e8b;Sampled=1\n    [X-Adsk-Signature] => sha256=c9976a64d1b254d0656a0a791f285cae7390f4042b4b366d259a0d15c497b272\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3bb313a7-32d1-4d81-96c4-88c6ffd1f73f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 00:00:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"3bb313a7-32d1-4d81-96c4-88c6ffd1f73f","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-02T00:00:57Z"},"publishedAt":"2025-08-02T00:00:57.000Z","country":"GB"}
[webhook] [2025-08-02 00:01:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 00:01:47
[webhook] [2025-08-02 00:01:47] [adwsapi_v2.php:36]  Provided signature: sha256=0351994b678aa156b70c8de546c8b3e5d54ea1d4a7db910d2436c614128d56b9
[webhook] [2025-08-02 00:01:47] [adwsapi_v2.php:37]  Calculated signature: sha256=51675d4958b99177e342b8818aeef245945c017f131ce684ecbcbcbf89ffe902
[webhook] [2025-08-02 00:01:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0b347414a8891247\n    [X-B3-Traceid] => 688d55687112ce895e75ce1c2539a554\n    [B3] => 688d55687112ce895e75ce1c2539a554-0b347414a8891247-1\n    [Traceparent] => 00-688d55687112ce895e75ce1c2539a554-0b347414a8891247-01\n    [X-Amzn-Trace-Id] => Root=1-688d5568-7112ce895e75ce1c2539a554;Parent=0b347414a8891247;Sampled=1\n    [X-Adsk-Signature] => sha256=0351994b678aa156b70c8de546c8b3e5d54ea1d4a7db910d2436c614128d56b9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ec35dc69-b6d6-4a46-8b5c-a6ca51806a05\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 00:01:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"ec35dc69-b6d6-4a46-8b5c-a6ca51806a05","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Price_Change","modifiedAt":"2025-08-02T00:01:44Z"},"publishedAt":"2025-08-02T00:01:44.000Z","country":"GB"}
[webhook] [2025-08-02 00:06:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 00:06:31
[webhook] [2025-08-02 00:06:31] [adwsapi_v2.php:36]  Provided signature: sha256=038fb185fc0687b0884854918aad997ac6fbdcaec9ad9db07e4e876b1243e302
[webhook] [2025-08-02 00:06:31] [adwsapi_v2.php:37]  Calculated signature: sha256=00ba7afc677558e36178fbfb4034e822361ca6704775c0ea997141c933bdc1f1
[webhook] [2025-08-02 00:06:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0ae8641e3753a0ee\n    [X-B3-Traceid] => 688d56850f656ed05146419e2e1ebdc8\n    [B3] => 688d56850f656ed05146419e2e1ebdc8-0ae8641e3753a0ee-1\n    [Traceparent] => 00-688d56850f656ed05146419e2e1ebdc8-0ae8641e3753a0ee-01\n    [X-Amzn-Trace-Id] => Root=1-688d5685-0f656ed05146419e2e1ebdc8;Parent=0ae8641e3753a0ee;Sampled=1\n    [X-Adsk-Signature] => sha256=038fb185fc0687b0884854918aad997ac6fbdcaec9ad9db07e4e876b1243e302\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 161fd91d-83aa-4913-a8dc-38b43adc4e06\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 00:06:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"161fd91d-83aa-4913-a8dc-38b43adc4e06","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72258911056117","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-01T23:51:26.000+0000"},"publishedAt":"2025-08-02T00:06:29.000Z","csn":"5103159758"}
[webhook] [2025-08-02 06:08:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 06:08:32
[webhook] [2025-08-02 06:08:32] [adwsapi_v2.php:36]  Provided signature: sha256=18da81c48e3aef2883fd04d075a3681899e748f397da39dcb29665eb972ddd6c
[webhook] [2025-08-02 06:08:32] [adwsapi_v2.php:37]  Calculated signature: sha256=01ccd3c952793fb48a387907670ee4e2fba020ce63bb037ef1781cd37f1da51a
[webhook] [2025-08-02 06:08:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 760325abad7d9001\n    [X-B3-Traceid] => 688dab5d044d977a323b5bb2327ec9f3\n    [B3] => 688dab5d044d977a323b5bb2327ec9f3-760325abad7d9001-1\n    [Traceparent] => 00-688dab5d044d977a323b5bb2327ec9f3-760325abad7d9001-01\n    [X-Amzn-Trace-Id] => Root=1-688dab5d-044d977a323b5bb2327ec9f3;Parent=760325abad7d9001;Sampled=1\n    [X-Adsk-Signature] => sha256=18da81c48e3aef2883fd04d075a3681899e748f397da39dcb29665eb972ddd6c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ac355e98-d9fe-45b9-8d03-05640123652d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 06:08:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"ac355e98-d9fe-45b9-8d03-05640123652d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65701794230087","status":"Expired","quantity":4,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-02T05:43:26.000+0000"},"publishedAt":"2025-08-02T06:08:29.000Z","csn":"5103159758"}
[webhook] [2025-08-02 06:10:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 06:10:23
[webhook] [2025-08-02 06:10:23] [adwsapi_v2.php:36]  Provided signature: sha256=38bf6fc8532f9da26b73ae04c9d872361ff440973d363dad8c08b764bfb61082
[webhook] [2025-08-02 06:10:23] [adwsapi_v2.php:37]  Calculated signature: sha256=8649709ae08b63d1eecc054ed1a8d4dffd1cc8dbcce99b7037c961aba4841c21
[webhook] [2025-08-02 06:10:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5059f27761308ed1\n    [X-B3-Traceid] => 688dabcd29c5df21477c0cde09095a17\n    [B3] => 688dabcd29c5df21477c0cde09095a17-5059f27761308ed1-1\n    [Traceparent] => 00-688dabcd29c5df21477c0cde09095a17-5059f27761308ed1-01\n    [X-Amzn-Trace-Id] => Root=1-688dabcd-29c5df21477c0cde09095a17;Parent=5059f27761308ed1;Sampled=1\n    [X-Adsk-Signature] => sha256=38bf6fc8532f9da26b73ae04c9d872361ff440973d363dad8c08b764bfb61082\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f7f74bb1-fe85-4da2-a107-0e202a2a3890\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 06:10:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"f7f74bb1-fe85-4da2-a107-0e202a2a3890","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75146432676914","status":"Expired","quantity":1,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-02T05:40:19.000+0000"},"publishedAt":"2025-08-02T06:10:21.000Z","csn":"5103159758"}
[webhook] [2025-08-02 07:08:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 07:08:06
[webhook] [2025-08-02 07:08:06] [adwsapi_v2.php:36]  Provided signature: sha256=840bb4ffafc2172a7ac788bab3d8e4f7021133274eed005a355c655af7fb2ff4
[webhook] [2025-08-02 07:08:06] [adwsapi_v2.php:37]  Calculated signature: sha256=c121fc4284e24b4044e9f16f65581e231837e052fcb3ab9f6eaddc3e2bd37cca
[webhook] [2025-08-02 07:08:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 867192e690691dc0\n    [X-B3-Traceid] => 688db9543913037a71871d113ef3bd5e\n    [B3] => 688db9543913037a71871d113ef3bd5e-867192e690691dc0-1\n    [Traceparent] => 00-688db9543913037a71871d113ef3bd5e-867192e690691dc0-01\n    [X-Amzn-Trace-Id] => Root=1-688db954-3913037a71871d113ef3bd5e;Parent=867192e690691dc0;Sampled=1\n    [X-Adsk-Signature] => sha256=840bb4ffafc2172a7ac788bab3d8e4f7021133274eed005a355c655af7fb2ff4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 161d92f1-9789-43d4-8dad-389dd7f798ee\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 07:08:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"161d92f1-9789-43d4-8dad-389dd7f798ee","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65701794230087","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-02T06:48:02.000+0000"},"publishedAt":"2025-08-02T07:08:04.000Z","csn":"5103159758"}
[webhook] [2025-08-02 07:08:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 07:08:27
[webhook] [2025-08-02 07:08:27] [adwsapi_v2.php:36]  Provided signature: sha256=b9fdf1ccfec528b2c5ae5e250eb67399e6eccf361af8c95d1ab8e8c523dbb530
[webhook] [2025-08-02 07:08:27] [adwsapi_v2.php:37]  Calculated signature: sha256=dd3445668c1bb645c86854cdb94721de8da39f0fb0624cb81f471e04d997c127
[webhook] [2025-08-02 07:08:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 72db7c14501747e7\n    [X-B3-Traceid] => 688db96955375390658364fc4ea3682e\n    [B3] => 688db96955375390658364fc4ea3682e-72db7c14501747e7-1\n    [Traceparent] => 00-688db96955375390658364fc4ea3682e-72db7c14501747e7-01\n    [X-Amzn-Trace-Id] => Root=1-688db969-55375390658364fc4ea3682e;Parent=72db7c14501747e7;Sampled=1\n    [X-Adsk-Signature] => sha256=b9fdf1ccfec528b2c5ae5e250eb67399e6eccf361af8c95d1ab8e8c523dbb530\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9653edff-236d-4334-a931-cc4f89b4afdd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 07:08:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"9653edff-236d-4334-a931-cc4f89b4afdd","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72018003980494","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-02T06:43:23.000+0000"},"publishedAt":"2025-08-02T07:08:25.000Z","csn":"5103159758"}
[webhook] [2025-08-02 07:11:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 07:11:01
[webhook] [2025-08-02 07:11:01] [adwsapi_v2.php:36]  Provided signature: sha256=0a9071d91a0b16fbcb1cc92eac499e0770f2833fb6c2921cb6e19be0022bf5b5
[webhook] [2025-08-02 07:11:01] [adwsapi_v2.php:37]  Calculated signature: sha256=76a7635e8956489debf2f27151c91c495b46e0aadebae4f9e338a9eec5ce2441
[webhook] [2025-08-02 07:11:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5630acb30244f36d\n    [X-B3-Traceid] => 688dba0238f5ce91324e758a50e4aaed\n    [B3] => 688dba0238f5ce91324e758a50e4aaed-5630acb30244f36d-1\n    [Traceparent] => 00-688dba0238f5ce91324e758a50e4aaed-5630acb30244f36d-01\n    [X-Amzn-Trace-Id] => Root=1-688dba02-38f5ce91324e758a50e4aaed;Parent=5630acb30244f36d;Sampled=1\n    [X-Adsk-Signature] => sha256=0a9071d91a0b16fbcb1cc92eac499e0770f2833fb6c2921cb6e19be0022bf5b5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e9f64d5f-a065-4164-83cc-ba5e9e21e6b5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 07:11:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"e9f64d5f-a065-4164-83cc-ba5e9e21e6b5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72258911056117","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-02T06:50:56.000+0000"},"publishedAt":"2025-08-02T07:10:58.000Z","csn":"5103159758"}
[webhook] [2025-08-02 12:11:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 12:11:35
[webhook] [2025-08-02 12:11:35] [adwsapi_v2.php:36]  Provided signature: sha256=51850f18747ebb590a1b798400100516bfa8b22294654324e11b30e249768f1b
[webhook] [2025-08-02 12:11:35] [adwsapi_v2.php:37]  Calculated signature: sha256=e564c4076db3dc1c7d3adc111c6800ecd768240283e8b1f6059f7735210663e7
[webhook] [2025-08-02 12:11:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 93cd1410f9057c7f\n    [X-B3-Traceid] => 688e007476bb06cc13bcc51829ed55bf\n    [B3] => 688e007476bb06cc13bcc51829ed55bf-93cd1410f9057c7f-1\n    [Traceparent] => 00-688e007476bb06cc13bcc51829ed55bf-93cd1410f9057c7f-01\n    [X-Amzn-Trace-Id] => Root=1-688e0074-76bb06cc13bcc51829ed55bf;Parent=93cd1410f9057c7f;Sampled=1\n    [X-Adsk-Signature] => sha256=51850f18747ebb590a1b798400100516bfa8b22294654324e11b30e249768f1b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754136692878-65701794230087\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 12:11:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754136692878-65701794230087","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65701794230087","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-02T12:11:32.878Z"},"publishedAt":"2025-08-02T12:11:32.000Z","csn":"5103159758"}
[webhook] [2025-08-02 12:12:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 12:12:32
[webhook] [2025-08-02 12:12:32] [adwsapi_v2.php:36]  Provided signature: sha256=d4cc6bff31e5a3d9318fe2bd2ba0a3caf1c3828365022b548686c990bbfee3a9
[webhook] [2025-08-02 12:12:32] [adwsapi_v2.php:37]  Calculated signature: sha256=0ff63dd07e36d91ac51eff3e118f63d0f448849b36f0c6772242f9547eab2e17
[webhook] [2025-08-02 12:12:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b729292eb89097a9\n    [X-B3-Traceid] => 688e00ae22a44fb260023ec279232557\n    [B3] => 688e00ae22a44fb260023ec279232557-b729292eb89097a9-1\n    [Traceparent] => 00-688e00ae22a44fb260023ec279232557-b729292eb89097a9-01\n    [X-Amzn-Trace-Id] => Root=1-688e00ae-22a44fb260023ec279232557;Parent=b729292eb89097a9;Sampled=1\n    [X-Adsk-Signature] => sha256=d4cc6bff31e5a3d9318fe2bd2ba0a3caf1c3828365022b548686c990bbfee3a9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754136750519-72258911056117\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 12:12:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754136750519-72258911056117","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72258911056117","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-02T12:12:30.519Z"},"publishedAt":"2025-08-02T12:12:30.000Z","csn":"5103159758"}
[webhook] [2025-08-02 12:15:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 12:15:01
[webhook] [2025-08-02 12:15:01] [adwsapi_v2.php:36]  Provided signature: sha256=b42877f7d06ef4b6bd6f0d182e27d4b83f5d9106bee7aada162f5865fe429a16
[webhook] [2025-08-02 12:15:01] [adwsapi_v2.php:37]  Calculated signature: sha256=d3abd4626731236ed2c0cf9653438061bf5ac0cca6b86e7d3ea679e17555c464
[webhook] [2025-08-02 12:15:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 231b64b94f3243ef\n    [X-B3-Traceid] => 688e0143449a16854584a982327397c4\n    [B3] => 688e0143449a16854584a982327397c4-231b64b94f3243ef-1\n    [Traceparent] => 00-688e0143449a16854584a982327397c4-231b64b94f3243ef-01\n    [X-Amzn-Trace-Id] => Root=1-688e0143-449a16854584a982327397c4;Parent=231b64b94f3243ef;Sampled=1\n    [X-Adsk-Signature] => sha256=b42877f7d06ef4b6bd6f0d182e27d4b83f5d9106bee7aada162f5865fe429a16\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754136899159-72018003980494\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 12:15:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754136899159-72018003980494","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72018003980494","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-02T12:14:59.159Z"},"publishedAt":"2025-08-02T12:14:59.000Z","csn":"5103159758"}
[webhook] [2025-08-02 15:13:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 15:13:43
[webhook] [2025-08-02 15:13:43] [adwsapi_v2.php:36]  Provided signature: sha256=dcfceb0f5bb1509f6fca4e7629eb9dee381cf8176f52abeba9b431b77e5702ba
[webhook] [2025-08-02 15:13:43] [adwsapi_v2.php:37]  Calculated signature: sha256=c54fd945c19ea7088c411e6a5ea28bbadf1761212d9a925b5b3bad6aec6cb1de
[webhook] [2025-08-02 15:13:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b44be9768889b3aa\n    [X-B3-Traceid] => 688e2b243c40d28b6dc28ec306830223\n    [B3] => 688e2b243c40d28b6dc28ec306830223-b44be9768889b3aa-1\n    [Traceparent] => 00-688e2b243c40d28b6dc28ec306830223-b44be9768889b3aa-01\n    [X-Amzn-Trace-Id] => Root=1-688e2b24-3c40d28b6dc28ec306830223;Parent=b44be9768889b3aa;Sampled=1\n    [X-Adsk-Signature] => sha256=dcfceb0f5bb1509f6fca4e7629eb9dee381cf8176f52abeba9b431b77e5702ba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d8e0a480-f63d-4c6a-861a-2e3252176196\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 15:13:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"d8e0a480-f63d-4c6a-861a-2e3252176196","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72864095179347","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-02T14:58:37.000+0000"},"publishedAt":"2025-08-02T15:13:41.000Z","csn":"5103159758"}
[webhook] [2025-08-02 23:00:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-02 23:00:43
[webhook] [2025-08-02 23:00:43] [adwsapi_v2.php:36]  Provided signature: sha256=aa9d631bdbfe9697b3c21abd6b5cd91530ef3b362195accafabaa8db309b4618
[webhook] [2025-08-02 23:00:43] [adwsapi_v2.php:37]  Calculated signature: sha256=91ac01222e23a97eaf1326a3d8ed4d7137b81ed2a9c8e2ac6cf6b381c2d0a1b4
[webhook] [2025-08-02 23:00:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7b3f7eadb6647e8f\n    [X-B3-Traceid] => 688e9899df8a3b8bb5a562ee9b3ff52b\n    [B3] => 688e9899df8a3b8bb5a562ee9b3ff52b-7b3f7eadb6647e8f-1\n    [Traceparent] => 00-688e9899df8a3b8bb5a562ee9b3ff52b-7b3f7eadb6647e8f-01\n    [X-Amzn-Trace-Id] => Root=1-688e9899-df8a3b8bb5a562ee9b3ff52b;Parent=7b3f7eadb6647e8f;Sampled=1\n    [X-Adsk-Signature] => sha256=aa9d631bdbfe9697b3c21abd6b5cd91530ef3b362195accafabaa8db309b4618\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1def7a34-b416-41a1-801c-7b5620678a8c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-02 23:00:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"1def7a34-b416-41a1-801c-7b5620678a8c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-904892","transactionId":"0d7dfce1-f559-5432-91cb-9c018a70b297","quoteStatus":"Expired","message":"Quote# Q-904892 status changed to Expired.","modifiedAt":"2025-08-02T23:00:24.428Z"},"publishedAt":"2025-08-02T23:00:41.000Z","csn":"5103159758"}
[webhook] [2025-08-03 06:08:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 06:08:50
[webhook] [2025-08-03 06:08:51] [adwsapi_v2.php:36]  Provided signature: sha256=a96352a8cab2e74ea0ab9db8247749d8cd85c0ea3582b29f98724c55c732df6e
[webhook] [2025-08-03 06:08:51] [adwsapi_v2.php:37]  Calculated signature: sha256=af541ce090c211d54a66e7c9df816150fb37515c64ec65f7b6d662cb88f35739
[webhook] [2025-08-03 06:08:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 68e2d659daceadd8\n    [X-B3-Traceid] => 688efcf03d6dcae81cde2f942c24e4eb\n    [B3] => 688efcf03d6dcae81cde2f942c24e4eb-68e2d659daceadd8-1\n    [Traceparent] => 00-688efcf03d6dcae81cde2f942c24e4eb-68e2d659daceadd8-01\n    [X-Amzn-Trace-Id] => Root=1-688efcf0-3d6dcae81cde2f942c24e4eb;Parent=68e2d659daceadd8;Sampled=1\n    [X-Adsk-Signature] => sha256=a96352a8cab2e74ea0ab9db8247749d8cd85c0ea3582b29f98724c55c732df6e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 66129f82-23e8-4535-b506-c8717133f47b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 06:08:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"66129f82-23e8-4535-b506-c8717133f47b","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56018211103443","status":"Expired","quantity":1,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-03T05:38:44.000+0000"},"publishedAt":"2025-08-03T06:08:48.000Z","csn":"5103159758"}
[webhook] [2025-08-03 07:31:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 07:31:01
[webhook] [2025-08-03 07:31:01] [adwsapi_v2.php:36]  Provided signature: sha256=f930af7f2c5e1bab637e28abd8af2dfac5d0871f324af019486b5379275e4fa8
[webhook] [2025-08-03 07:31:01] [adwsapi_v2.php:37]  Calculated signature: sha256=b33d6e7fbbbf030cd1e1c2c2bbb0712ad441a3d78f5b7999d87ff616cfb52a9b
[webhook] [2025-08-03 07:31:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 908355000ca82612\n    [X-B3-Traceid] => 688f103285e97af420be42761ec5e6f9\n    [B3] => 688f103285e97af420be42761ec5e6f9-908355000ca82612-1\n    [Traceparent] => 00-688f103285e97af420be42761ec5e6f9-908355000ca82612-01\n    [X-Amzn-Trace-Id] => Root=1-688f1032-85e97af420be42761ec5e6f9;Parent=908355000ca82612;Sampled=1\n    [X-Adsk-Signature] => sha256=f930af7f2c5e1bab637e28abd8af2dfac5d0871f324af019486b5379275e4fa8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b900975b-5fa1-4cc8-a749-31c68d5886ee\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 07:31:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"b900975b-5fa1-4cc8-a749-31c68d5886ee","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-942782","transactionId":"568846e0-699c-5721-9801-261433a9465b","quoteStatus":"Order Submitted","message":"Quote# Q-942782 status changed to Order Submitted.","modifiedAt":"2025-08-03T07:30:58.287Z"},"publishedAt":"2025-08-03T07:30:58.000Z","csn":"5103159758"}
[webhook] [2025-08-03 07:31:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 07:31:07
[webhook] [2025-08-03 07:31:07] [adwsapi_v2.php:36]  Provided signature: sha256=571d401b4296763bd047ed759426bd7e037da13667264dce6b9a0c9d61f525c7
[webhook] [2025-08-03 07:31:07] [adwsapi_v2.php:37]  Calculated signature: sha256=1f9e933fb8850e37b1e771b678560f58d2c34f9555473a1cad55163c2bfd0404
[webhook] [2025-08-03 07:31:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 33285934413bca26\n    [X-B3-Traceid] => 688f103805a88e324c626bb0f311aaac\n    [B3] => 688f103805a88e324c626bb0f311aaac-33285934413bca26-1\n    [Traceparent] => 00-688f103805a88e324c626bb0f311aaac-33285934413bca26-01\n    [X-Amzn-Trace-Id] => Root=1-688f1038-05a88e324c626bb0f311aaac;Parent=33285934413bca26;Sampled=1\n    [X-Adsk-Signature] => sha256=571d401b4296763bd047ed759426bd7e037da13667264dce6b9a0c9d61f525c7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6b8758a7-3682-4091-8b61-6f18a91de89b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 07:31:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"6b8758a7-3682-4091-8b61-6f18a91de89b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-942782","transactionId":"568846e0-699c-5721-9801-261433a9465b","quoteStatus":"Ordered","message":"Quote# Q-942782 status changed to Ordered.","modifiedAt":"2025-08-03T07:31:04.200Z"},"publishedAt":"2025-08-03T07:31:04.000Z","csn":"5103159758"}
[webhook] [2025-08-03 07:46:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 07:46:13
[webhook] [2025-08-03 07:46:13] [adwsapi_v2.php:36]  Provided signature: sha256=129c61cd4126ad88cbc5751207e6fe7cad9874820008d2fc933d78833d786005
[webhook] [2025-08-03 07:46:13] [adwsapi_v2.php:37]  Calculated signature: sha256=16aa33ffc63a3fb89eef64fac81a2c65493b9d54652ec43d38eab65ca22341ee
[webhook] [2025-08-03 07:46:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 56456590ac80a309\n    [X-B3-Traceid] => 688f13c25cbc1c814227e8c812d08e67\n    [B3] => 688f13c25cbc1c814227e8c812d08e67-56456590ac80a309-1\n    [Traceparent] => 00-688f13c25cbc1c814227e8c812d08e67-56456590ac80a309-01\n    [X-Amzn-Trace-Id] => Root=1-688f13c2-5cbc1c814227e8c812d08e67;Parent=56456590ac80a309;Sampled=1\n    [X-Adsk-Signature] => sha256=129c61cd4126ad88cbc5751207e6fe7cad9874820008d2fc933d78833d786005\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => aeee4c20-9d4e-415a-aa25-d3e22fe03fe3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 07:46:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"aeee4c20-9d4e-415a-aa25-d3e22fe03fe3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63170833754989","quantity":1,"endDate":"2026-09-14","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-03T07:31:08.000+0000"},"publishedAt":"2025-08-03T07:46:10.000Z","csn":"5103159758"}
[webhook] [2025-08-03 11:39:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 11:39:30
[webhook] [2025-08-03 11:39:30] [adwsapi_v2.php:36]  Provided signature: sha256=9fab112b32d43754826aa5ab990a336d7c0e9a4fff02e69571e926227fd5085d
[webhook] [2025-08-03 11:39:30] [adwsapi_v2.php:37]  Calculated signature: sha256=1525585d6a022f281ac8582e044b61746dde12e3bf5d1fe2968545e808014a78
[webhook] [2025-08-03 11:39:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 62b8f4dd1df8c77e\n    [X-B3-Traceid] => 688f4a6f062da20a71753ed17ce24770\n    [B3] => 688f4a6f062da20a71753ed17ce24770-62b8f4dd1df8c77e-1\n    [Traceparent] => 00-688f4a6f062da20a71753ed17ce24770-62b8f4dd1df8c77e-01\n    [X-Amzn-Trace-Id] => Root=1-688f4a6f-062da20a71753ed17ce24770;Parent=62b8f4dd1df8c77e;Sampled=1\n    [X-Adsk-Signature] => sha256=9fab112b32d43754826aa5ab990a336d7c0e9a4fff02e69571e926227fd5085d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 26bf65db-ac9b-4696-952a-cf86193f66b5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 11:39:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"26bf65db-ac9b-4696-952a-cf86193f66b5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63170833754989","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-03T11:19:20.000+0000"},"publishedAt":"2025-08-03T11:39:27.000Z","csn":"5103159758"}
[webhook] [2025-08-03 12:08:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 12:08:21
[webhook] [2025-08-03 12:08:21] [adwsapi_v2.php:36]  Provided signature: sha256=3bd6f9473e02f21263bb67e8cb86716b3ac13a6a13ed70b544f98d9a6eeaff79
[webhook] [2025-08-03 12:08:21] [adwsapi_v2.php:37]  Calculated signature: sha256=4e4407cb9ca3b8b7c2e982a9a88f7b7d83bb5dbceb4ee998af76b324f7afd492
[webhook] [2025-08-03 12:08:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c45cc5a2c1f7be0f\n    [X-B3-Traceid] => 688f513139a74e43580914c61c1d4917\n    [B3] => 688f513139a74e43580914c61c1d4917-c45cc5a2c1f7be0f-1\n    [Traceparent] => 00-688f513139a74e43580914c61c1d4917-c45cc5a2c1f7be0f-01\n    [X-Amzn-Trace-Id] => Root=1-688f5131-39a74e43580914c61c1d4917;Parent=c45cc5a2c1f7be0f;Sampled=1\n    [X-Adsk-Signature] => sha256=3bd6f9473e02f21263bb67e8cb86716b3ac13a6a13ed70b544f98d9a6eeaff79\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 403bc316-0ffd-4e90-99f9-b93604026cc3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 12:08:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"403bc316-0ffd-4e90-99f9-b93604026cc3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72648778789850","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-03T11:48:15.000+0000"},"publishedAt":"2025-08-03T12:08:17.000Z","csn":"5103159758"}
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 12:10:51
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:36]  Provided signature: sha256=5f1d4287f4c7754b9b093fc7bfae8e5159104e420330b60de8e6191513748130
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:37]  Calculated signature: sha256=f2d7aebe0f5cde018284c442b0a59c4f6f575007942134a8055c1af2f3d45b80
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f4cbe3b6b91740bc\n    [X-B3-Traceid] => 688f51c82bb49d98563b4934619e40be\n    [B3] => 688f51c82bb49d98563b4934619e40be-f4cbe3b6b91740bc-1\n    [Traceparent] => 00-688f51c82bb49d98563b4934619e40be-f4cbe3b6b91740bc-01\n    [X-Amzn-Trace-Id] => Root=1-688f51c8-2bb49d98563b4934619e40be;Parent=f4cbe3b6b91740bc;Sampled=1\n    [X-Adsk-Signature] => sha256=5f1d4287f4c7754b9b093fc7bfae8e5159104e420330b60de8e6191513748130\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 535ed5d5-b57a-45fb-b998-60db33585206\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"535ed5d5-b57a-45fb-b998-60db33585206","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75006472719688","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-03T11:40:44.000+0000"},"publishedAt":"2025-08-03T12:10:48.000Z","csn":"5103159758"}
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 12:10:51
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:36]  Provided signature: sha256=6c46c4e099a29a403906aba7b435d4ded74fe05117d18acd7d66f84413d482d3
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:37]  Calculated signature: sha256=76fa6d905135d98faecf18b21fe21652aad7f5afbac037120f43cb23df6f1b12
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ae0e7edbddd0de16\n    [X-B3-Traceid] => 688f51c84c74b9db74c503bf1a98f545\n    [B3] => 688f51c84c74b9db74c503bf1a98f545-ae0e7edbddd0de16-1\n    [Traceparent] => 00-688f51c84c74b9db74c503bf1a98f545-ae0e7edbddd0de16-01\n    [X-Amzn-Trace-Id] => Root=1-688f51c8-4c74b9db74c503bf1a98f545;Parent=ae0e7edbddd0de16;Sampled=1\n    [X-Adsk-Signature] => sha256=6c46c4e099a29a403906aba7b435d4ded74fe05117d18acd7d66f84413d482d3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 976fd034-c4c1-40f0-be19-eb45e0deacb2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 12:10:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"976fd034-c4c1-40f0-be19-eb45e0deacb2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"68915687453814","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-03T11:40:46.000+0000"},"publishedAt":"2025-08-03T12:10:48.000Z","csn":"5103159758"}
[webhook] [2025-08-03 12:10:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 12:10:52
[webhook] [2025-08-03 12:10:52] [adwsapi_v2.php:36]  Provided signature: sha256=909721a82fb21c07a5bf4abbf6352d439879d9aa9b91ce0c3881f9c15fd2068c
[webhook] [2025-08-03 12:10:52] [adwsapi_v2.php:37]  Calculated signature: sha256=cf9e903114f962249cb9f72b78c4930f81f0ded5f5fce638958bd532454fd490
[webhook] [2025-08-03 12:10:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ad2a831d04010ec1\n    [X-B3-Traceid] => 688f51ca7d2f221e70a2d2115784b6c2\n    [B3] => 688f51ca7d2f221e70a2d2115784b6c2-ad2a831d04010ec1-1\n    [Traceparent] => 00-688f51ca7d2f221e70a2d2115784b6c2-ad2a831d04010ec1-01\n    [X-Amzn-Trace-Id] => Root=1-688f51ca-7d2f221e70a2d2115784b6c2;Parent=ad2a831d04010ec1;Sampled=1\n    [X-Adsk-Signature] => sha256=909721a82fb21c07a5bf4abbf6352d439879d9aa9b91ce0c3881f9c15fd2068c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 91603999-96bc-4b5f-ba96-75e1d3567c25\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 12:10:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"91603999-96bc-4b5f-ba96-75e1d3567c25","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71887756008038","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-03T11:40:47.000+0000"},"publishedAt":"2025-08-03T12:10:50.000Z","csn":"5103159758"}
[webhook] [2025-08-03 12:10:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 12:10:53
[webhook] [2025-08-03 12:10:53] [adwsapi_v2.php:36]  Provided signature: sha256=98c09887676dda1c8dec68c43999d2b25e351842e75fafb5882d7075870a7425
[webhook] [2025-08-03 12:10:53] [adwsapi_v2.php:37]  Calculated signature: sha256=aa64ed0b3f8e9eb92226bfcdabb141af53d726da0393a73b35e1cc3e1574df95
[webhook] [2025-08-03 12:10:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4220e8fddc0d8fbe\n    [X-B3-Traceid] => 688f51cb31a826033387988b54a3a542\n    [B3] => 688f51cb31a826033387988b54a3a542-4220e8fddc0d8fbe-1\n    [Traceparent] => 00-688f51cb31a826033387988b54a3a542-4220e8fddc0d8fbe-01\n    [X-Amzn-Trace-Id] => Root=1-688f51cb-31a826033387988b54a3a542;Parent=4220e8fddc0d8fbe;Sampled=1\n    [X-Adsk-Signature] => sha256=98c09887676dda1c8dec68c43999d2b25e351842e75fafb5882d7075870a7425\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 29929270-d4a0-4b4f-8eaf-75ec42aaacac\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 12:10:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"29929270-d4a0-4b4f-8eaf-75ec42aaacac","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55508693518307","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-03T11:40:48.000+0000"},"publishedAt":"2025-08-03T12:10:51.000Z","csn":"5103159758"}
[webhook] [2025-08-03 16:01:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 16:01:53
[webhook] [2025-08-03 16:01:53] [adwsapi_v2.php:36]  Provided signature: sha256=76af6a0a60489e99712cc2dbca3d51295e448cc8b46913b5bf60348b44038b7b
[webhook] [2025-08-03 16:01:53] [adwsapi_v2.php:37]  Calculated signature: sha256=9d513e60ddc2d29f5494e89d24d22fab46843afcc72885f0032cfd43f31c7c67
[webhook] [2025-08-03 16:01:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6b487ae28650f2e7\n    [X-B3-Traceid] => 688f87ef30030cca7572d029767dc990\n    [B3] => 688f87ef30030cca7572d029767dc990-6b487ae28650f2e7-1\n    [Traceparent] => 00-688f87ef30030cca7572d029767dc990-6b487ae28650f2e7-01\n    [X-Amzn-Trace-Id] => Root=1-688f87ef-30030cca7572d029767dc990;Parent=6b487ae28650f2e7;Sampled=1\n    [X-Adsk-Signature] => sha256=76af6a0a60489e99712cc2dbca3d51295e448cc8b46913b5bf60348b44038b7b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754236911524-63170833754989\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 16:01:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754236911524-63170833754989","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63170833754989","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-03T16:01:51.524Z"},"publishedAt":"2025-08-03T16:01:51.000Z","csn":"5103159758"}
[webhook] [2025-08-03 16:02:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 16:02:10
[webhook] [2025-08-03 16:02:10] [adwsapi_v2.php:36]  Provided signature: sha256=ab18a4354ee85c78b940f27eff9698daf00d02dd26da0ae694dd9e8a3523ac8d
[webhook] [2025-08-03 16:02:10] [adwsapi_v2.php:37]  Calculated signature: sha256=8bb57575c685113a1d303a453dc057d564ef6de76a24d61cfc8ebf5ecb678a33
[webhook] [2025-08-03 16:02:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c51c7bc08c11997e\n    [X-B3-Traceid] => 688f88007a3b51c120b953c50aef894a\n    [B3] => 688f88007a3b51c120b953c50aef894a-c51c7bc08c11997e-1\n    [Traceparent] => 00-688f88007a3b51c120b953c50aef894a-c51c7bc08c11997e-01\n    [X-Amzn-Trace-Id] => Root=1-688f8800-7a3b51c120b953c50aef894a;Parent=c51c7bc08c11997e;Sampled=1\n    [X-Adsk-Signature] => sha256=ab18a4354ee85c78b940f27eff9698daf00d02dd26da0ae694dd9e8a3523ac8d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754236928191-71887756008038\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 16:02:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754236928191-71887756008038","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"71887756008038","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-03T16:02:08.191Z"},"publishedAt":"2025-08-03T16:02:08.000Z","csn":"5103159758"}
[webhook] [2025-08-03 16:02:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 16:02:37
[webhook] [2025-08-03 16:02:37] [adwsapi_v2.php:36]  Provided signature: sha256=c8d91a75de60587451ae305306f47de5303c93dc18640728cf33f4d7a9e1bedd
[webhook] [2025-08-03 16:02:37] [adwsapi_v2.php:37]  Calculated signature: sha256=ed49e15d11c6ce726cba01298c3d82a5b66de759e7aa1c1272352513af26c81f
[webhook] [2025-08-03 16:02:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5d77a7b5d680679e\n    [X-B3-Traceid] => 688f881b6a343dcd5ce286ce56d406e9\n    [B3] => 688f881b6a343dcd5ce286ce56d406e9-5d77a7b5d680679e-1\n    [Traceparent] => 00-688f881b6a343dcd5ce286ce56d406e9-5d77a7b5d680679e-01\n    [X-Amzn-Trace-Id] => Root=1-688f881b-6a343dcd5ce286ce56d406e9;Parent=5d77a7b5d680679e;Sampled=1\n    [X-Adsk-Signature] => sha256=c8d91a75de60587451ae305306f47de5303c93dc18640728cf33f4d7a9e1bedd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754236955120-55508693518307\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 16:02:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754236955120-55508693518307","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55508693518307","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-03T16:02:35.120Z"},"publishedAt":"2025-08-03T16:02:35.000Z","csn":"5103159758"}
[webhook] [2025-08-03 16:03:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 16:03:11
[webhook] [2025-08-03 16:03:11] [adwsapi_v2.php:36]  Provided signature: sha256=a9844a763c056118ac23ba9217200bf3b842a8350e6a5c94006598b7c09559b3
[webhook] [2025-08-03 16:03:11] [adwsapi_v2.php:37]  Calculated signature: sha256=25d2123f190adce7e72249ed27ed803c8123dfac82da34bb8f34b02836ebd8a3
[webhook] [2025-08-03 16:03:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 98f4186b91fd919d\n    [X-B3-Traceid] => 688f883c0c9c008171bba2a14026533f\n    [B3] => 688f883c0c9c008171bba2a14026533f-98f4186b91fd919d-1\n    [Traceparent] => 00-688f883c0c9c008171bba2a14026533f-98f4186b91fd919d-01\n    [X-Amzn-Trace-Id] => Root=1-688f883c-0c9c008171bba2a14026533f;Parent=98f4186b91fd919d;Sampled=1\n    [X-Adsk-Signature] => sha256=a9844a763c056118ac23ba9217200bf3b842a8350e6a5c94006598b7c09559b3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754236988680-68915687453814\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 16:03:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754236988680-68915687453814","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"68915687453814","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-03T16:03:08.680Z"},"publishedAt":"2025-08-03T16:03:08.000Z","csn":"5103159758"}
[webhook] [2025-08-03 16:03:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 16:03:48
[webhook] [2025-08-03 16:03:48] [adwsapi_v2.php:36]  Provided signature: sha256=5622daa17c9c709d448368f63c5e6252738d4ca31cd9910a22a1d1c574537aa6
[webhook] [2025-08-03 16:03:48] [adwsapi_v2.php:37]  Calculated signature: sha256=5ed59f980d3824e72fd01d9616a2cfccd5fc889fe634606d8474d9b832b8e6dd
[webhook] [2025-08-03 16:03:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7a30e7f37212b701\n    [X-B3-Traceid] => 688f8862336f620913751388400660ec\n    [B3] => 688f8862336f620913751388400660ec-7a30e7f37212b701-1\n    [Traceparent] => 00-688f8862336f620913751388400660ec-7a30e7f37212b701-01\n    [X-Amzn-Trace-Id] => Root=1-688f8862-336f620913751388400660ec;Parent=7a30e7f37212b701;Sampled=1\n    [X-Adsk-Signature] => sha256=5622daa17c9c709d448368f63c5e6252738d4ca31cd9910a22a1d1c574537aa6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754237026433-75006472719688\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 16:03:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754237026433-75006472719688","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75006472719688","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-03T16:03:46.433Z"},"publishedAt":"2025-08-03T16:03:46.000Z","csn":"5103159758"}
[webhook] [2025-08-03 20:01:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 20:01:31
[webhook] [2025-08-03 20:01:31] [adwsapi_v2.php:36]  Provided signature: sha256=2f0e8bc36924332335c9b19941f8001464775b5f095dfbffcd7401c41849a989
[webhook] [2025-08-03 20:01:31] [adwsapi_v2.php:37]  Calculated signature: sha256=ed9a6b00fb863d4a4d3867342bebafc84701ce63325a9e596f45651e81eb24b6
[webhook] [2025-08-03 20:01:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e9d09b6b68c1ed8c\n    [X-B3-Traceid] => 688fc0192bbf264556caf5f155f59ac3\n    [B3] => 688fc0192bbf264556caf5f155f59ac3-e9d09b6b68c1ed8c-1\n    [Traceparent] => 00-688fc0192bbf264556caf5f155f59ac3-e9d09b6b68c1ed8c-01\n    [X-Amzn-Trace-Id] => Root=1-688fc019-2bbf264556caf5f155f59ac3;Parent=e9d09b6b68c1ed8c;Sampled=1\n    [X-Adsk-Signature] => sha256=2f0e8bc36924332335c9b19941f8001464775b5f095dfbffcd7401c41849a989\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754251289182-63170833754989\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 20:01:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754251289182-63170833754989","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63170833754989","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-03T20:01:29.182Z"},"publishedAt":"2025-08-03T20:01:29.000Z","csn":"5103159758"}
[webhook] [2025-08-03 23:02:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 23:02:40
[webhook] [2025-08-03 23:02:40] [adwsapi_v2.php:36]  Provided signature: sha256=5439366142b32d958661dc54f8e110db6e5be6899ee614247759a4570802adac
[webhook] [2025-08-03 23:02:40] [adwsapi_v2.php:37]  Calculated signature: sha256=feffdd7084fa66d394be728dce9fc7c0498f7a9ac8b002e58f4947bd71bb8473
[webhook] [2025-08-03 23:02:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 53d32ebe637ac255\n    [X-B3-Traceid] => 688fea8e17e0a7f21b5cf73f3bfcb260\n    [B3] => 688fea8e17e0a7f21b5cf73f3bfcb260-53d32ebe637ac255-1\n    [Traceparent] => 00-688fea8e17e0a7f21b5cf73f3bfcb260-53d32ebe637ac255-01\n    [X-Amzn-Trace-Id] => Root=1-688fea8e-17e0a7f21b5cf73f3bfcb260;Parent=53d32ebe637ac255;Sampled=1\n    [X-Adsk-Signature] => sha256=5439366142b32d958661dc54f8e110db6e5be6899ee614247759a4570802adac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754262158494-562-96821892\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 23:02:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754262158494-562-96821892","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"562-96821892","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-03T23:02:38.494Z"},"publishedAt":"2025-08-03T23:02:38.000Z","csn":"5103159758"}
[webhook] [2025-08-03 23:03:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-03 23:03:12
[webhook] [2025-08-03 23:03:12] [adwsapi_v2.php:36]  Provided signature: sha256=e66496b0dd12c46fdf7302319aca59d6807480aab427340125d00df737fe6b0b
[webhook] [2025-08-03 23:03:12] [adwsapi_v2.php:37]  Calculated signature: sha256=c2d68bb008d3f56051233b2dc76bea0232e287b72f288e401b39350942dd59aa
[webhook] [2025-08-03 23:03:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5d11cec10f8ba00b\n    [X-B3-Traceid] => 688feaae7ee55ecc5200552300d50af8\n    [B3] => 688feaae7ee55ecc5200552300d50af8-5d11cec10f8ba00b-1\n    [Traceparent] => 00-688feaae7ee55ecc5200552300d50af8-5d11cec10f8ba00b-01\n    [X-Amzn-Trace-Id] => Root=1-688feaae-7ee55ecc5200552300d50af8;Parent=5d11cec10f8ba00b;Sampled=1\n    [X-Adsk-Signature] => sha256=e66496b0dd12c46fdf7302319aca59d6807480aab427340125d00df737fe6b0b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754262190407-574-93279082\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-03 23:03:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754262190407-574-93279082","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-93279082","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-03T23:03:10.407Z"},"publishedAt":"2025-08-03T23:03:10.000Z","csn":"5103159758"}
[webhook] [2025-08-04 06:10:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 06:10:36
[webhook] [2025-08-04 06:10:36] [adwsapi_v2.php:36]  Provided signature: sha256=8ce913003f966d380d330dbb4fa73a409a8ff5e235c4d95ce9108023d242cf80
[webhook] [2025-08-04 06:10:36] [adwsapi_v2.php:37]  Calculated signature: sha256=56b364c73f278ef65e9fad592ccd89ba86632a0c771c2159a5c791b861f2b36f
[webhook] [2025-08-04 06:10:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8ab7c0b83f67a21c\n    [X-B3-Traceid] => 68904eda683c1fc102bf51544acdbda3\n    [B3] => 68904eda683c1fc102bf51544acdbda3-8ab7c0b83f67a21c-1\n    [Traceparent] => 00-68904eda683c1fc102bf51544acdbda3-8ab7c0b83f67a21c-01\n    [X-Amzn-Trace-Id] => Root=1-68904eda-683c1fc102bf51544acdbda3;Parent=8ab7c0b83f67a21c;Sampled=1\n    [X-Adsk-Signature] => sha256=8ce913003f966d380d330dbb4fa73a409a8ff5e235c4d95ce9108023d242cf80\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 79df6998-019d-45e6-a39f-435e9f815600\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 06:10:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"79df6998-019d-45e6-a39f-435e9f815600","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62521806254194","status":"Expired","quantity":2,"message":"subscription status,quantity changed.","modifiedAt":"2025-08-04T05:35:32.000+0000"},"publishedAt":"2025-08-04T06:10:34.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:05:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:05:51
[webhook] [2025-08-04 07:05:51] [adwsapi_v2.php:36]  Provided signature: sha256=8061bed101e5c61b2d5a14262417d7ec4257b3ca2831ae2bacbab7a1f15e40bb
[webhook] [2025-08-04 07:05:51] [adwsapi_v2.php:37]  Calculated signature: sha256=459841c9e573c0ae3e83974a6344bf9716b3fc8377a52ab58583a660a221bc5a
[webhook] [2025-08-04 07:05:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c15f6c05651fcbf2\n    [X-B3-Traceid] => 68905bcc331480e32bef1f6c290de4cd\n    [B3] => 68905bcc331480e32bef1f6c290de4cd-c15f6c05651fcbf2-1\n    [Traceparent] => 00-68905bcc331480e32bef1f6c290de4cd-c15f6c05651fcbf2-01\n    [X-Amzn-Trace-Id] => Root=1-68905bcc-331480e32bef1f6c290de4cd;Parent=c15f6c05651fcbf2;Sampled=1\n    [X-Adsk-Signature] => sha256=8061bed101e5c61b2d5a14262417d7ec4257b3ca2831ae2bacbab7a1f15e40bb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d1842ee9-5739-4335-93ac-a9c215173213\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:05:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"d1842ee9-5739-4335-93ac-a9c215173213","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56440318707446","status":"Active","quantity":1,"endDate":"2026-07-28","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T06:35:46.000+0000"},"publishedAt":"2025-08-04T07:05:48.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:38:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:38:19
[webhook] [2025-08-04 07:38:19] [adwsapi_v2.php:36]  Provided signature: sha256=53d38631fde9c30d257c76e66da228927d704c1712c710acdebaf05aea24b4e2
[webhook] [2025-08-04 07:38:19] [adwsapi_v2.php:37]  Calculated signature: sha256=f3157b7e521d6252e4d7f076a9c853ea749eba8b01e897178486388e0edd7b09
[webhook] [2025-08-04 07:38:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d36948c35d0449b2\n    [X-B3-Traceid] => 6890636900b6a99e64c037176461da7b\n    [B3] => 6890636900b6a99e64c037176461da7b-d36948c35d0449b2-1\n    [Traceparent] => 00-6890636900b6a99e64c037176461da7b-d36948c35d0449b2-01\n    [X-Amzn-Trace-Id] => Root=1-68906369-00b6a99e64c037176461da7b;Parent=d36948c35d0449b2;Sampled=1\n    [X-Adsk-Signature] => sha256=53d38631fde9c30d257c76e66da228927d704c1712c710acdebaf05aea24b4e2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fa85c01d-bb87-4eca-b3ea-625da333a8d5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:38:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"fa85c01d-bb87-4eca-b3ea-625da333a8d5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56440318707446","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T07:08:11.000+0000"},"publishedAt":"2025-08-04T07:38:17.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:56:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:56:19
[webhook] [2025-08-04 07:56:19] [adwsapi_v2.php:36]  Provided signature: sha256=ef8427d74d9321a2b7ecbf7ba22b97fd925ee38521927a07be7bc695b07d2a5d
[webhook] [2025-08-04 07:56:19] [adwsapi_v2.php:37]  Calculated signature: sha256=06141cd343309d6244288378a9439589ebf1e3398c6545091c4f944a3f28a0f3
[webhook] [2025-08-04 07:56:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 38a7712034414167\n    [X-B3-Traceid] => 689067a093b7f5bdf720d4047f8de96c\n    [B3] => 689067a093b7f5bdf720d4047f8de96c-38a7712034414167-1\n    [Traceparent] => 00-689067a093b7f5bdf720d4047f8de96c-38a7712034414167-01\n    [X-Amzn-Trace-Id] => Root=1-689067a0-93b7f5bdf720d4047f8de96c;Parent=38a7712034414167;Sampled=1\n    [X-Adsk-Signature] => sha256=ef8427d74d9321a2b7ecbf7ba22b97fd925ee38521927a07be7bc695b07d2a5d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b6d039a8-b6a1-49fc-aef2-1c4182984063\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:56:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"b6d039a8-b6a1-49fc-aef2-1c4182984063","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979434","transactionId":"046a3ccb-1d58-5a6a-b125-5dc1748af494","quoteStatus":"Draft","message":"Quote# Q-979434 status changed to Draft.","modifiedAt":"2025-08-04T07:56:16.448Z"},"publishedAt":"2025-08-04T07:56:16.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:57:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:57:54
[webhook] [2025-08-04 07:57:54] [adwsapi_v2.php:36]  Provided signature: sha256=e8bf7f23fa82f85fcb9185e003d39916186c08610f004a856554d7171c25f240
[webhook] [2025-08-04 07:57:54] [adwsapi_v2.php:37]  Calculated signature: sha256=63d2a32ba5c186b0bc7d3a7053641b0a7801171b2dc08043a1bb8a7d4d639613
[webhook] [2025-08-04 07:57:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0650e5bfd0121044\n    [X-B3-Traceid] => 689068008ec95d8cc71077b125a1fa85\n    [B3] => 689068008ec95d8cc71077b125a1fa85-0650e5bfd0121044-1\n    [Traceparent] => 00-689068008ec95d8cc71077b125a1fa85-0650e5bfd0121044-01\n    [X-Amzn-Trace-Id] => Root=1-68906800-8ec95d8cc71077b125a1fa85;Parent=0650e5bfd0121044;Sampled=1\n    [X-Adsk-Signature] => sha256=e8bf7f23fa82f85fcb9185e003d39916186c08610f004a856554d7171c25f240\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ef92976b-ee6b-452b-83fc-24aa2da5a0a3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:57:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"ef92976b-ee6b-452b-83fc-24aa2da5a0a3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979434","transactionId":"046a3ccb-1d58-5a6a-b125-5dc1748af494","quoteStatus":"Quoted","message":"Quote# Q-979434 status changed to Quoted.","modifiedAt":"2025-08-04T07:57:51.501Z"},"publishedAt":"2025-08-04T07:57:52.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:58:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:58:30
[webhook] [2025-08-04 07:58:30] [adwsapi_v2.php:36]  Provided signature: sha256=aa7b2d45e8bd95ac58037e64e0a2d0bd05ea594798d01948a4d2554df96bb725
[webhook] [2025-08-04 07:58:30] [adwsapi_v2.php:37]  Calculated signature: sha256=beb22a9cf15da145d02cb8f9926c9b6c75a56f9f2b282a863b4d35a93865e3d6
[webhook] [2025-08-04 07:58:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0ba3926cff8f6e06\n    [X-B3-Traceid] => 68906824e64926d724c052d9064156b4\n    [B3] => 68906824e64926d724c052d9064156b4-0ba3926cff8f6e06-1\n    [Traceparent] => 00-68906824e64926d724c052d9064156b4-0ba3926cff8f6e06-01\n    [X-Amzn-Trace-Id] => Root=1-68906824-e64926d724c052d9064156b4;Parent=0ba3926cff8f6e06;Sampled=1\n    [X-Adsk-Signature] => sha256=aa7b2d45e8bd95ac58037e64e0a2d0bd05ea594798d01948a4d2554df96bb725\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b8e23838-2589-43af-82ae-7c47452db251\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:58:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"b8e23838-2589-43af-82ae-7c47452db251","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979447","transactionId":"b5baf5f0-df1a-5637-a5f8-94fab57ad170","quoteStatus":"Draft","message":"Quote# Q-979447 status changed to Draft.","modifiedAt":"2025-08-04T07:58:28.258Z"},"publishedAt":"2025-08-04T07:58:28.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:58:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:58:59
[webhook] [2025-08-04 07:58:59] [adwsapi_v2.php:36]  Provided signature: sha256=b6152f7802076b9cc743eb92b7a38b4a902f3e7d7891d37d6c8b091a74b0e86e
[webhook] [2025-08-04 07:58:59] [adwsapi_v2.php:37]  Calculated signature: sha256=4d0a8d6fd55ee6fbce7e60dd40115e5ce186afef054f5881e0769b1d2f63324b
[webhook] [2025-08-04 07:58:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 891077e27f105dc8\n    [X-B3-Traceid] => 689068414e9243e05edc13c78efd56fe\n    [B3] => 689068414e9243e05edc13c78efd56fe-891077e27f105dc8-1\n    [Traceparent] => 00-689068414e9243e05edc13c78efd56fe-891077e27f105dc8-01\n    [X-Amzn-Trace-Id] => Root=1-68906841-4e9243e05edc13c78efd56fe;Parent=891077e27f105dc8;Sampled=1\n    [X-Adsk-Signature] => sha256=b6152f7802076b9cc743eb92b7a38b4a902f3e7d7891d37d6c8b091a74b0e86e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 594181b8-ac21-4248-af6c-742959e7e45b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:58:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"594181b8-ac21-4248-af6c-742959e7e45b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979451","transactionId":"86140b3f-8aa6-542a-ae56-6a35e0d76b4b","quoteStatus":"Draft","message":"Quote# Q-979451 status changed to Draft.","modifiedAt":"2025-08-04T07:58:56.598Z"},"publishedAt":"2025-08-04T07:58:57.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:59:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:59:29
[webhook] [2025-08-04 07:59:29] [adwsapi_v2.php:36]  Provided signature: sha256=61823a93a2089b0f5814787fff2bb6f47f52b7aef4c6b7016f1d661e975f9f8d
[webhook] [2025-08-04 07:59:29] [adwsapi_v2.php:37]  Calculated signature: sha256=9b3af514908ee69a8ba05cfb3e97a61322f041cb0f8d06b1206432eb7118c62c
[webhook] [2025-08-04 07:59:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 68fb3ab9ee18d526\n    [X-B3-Traceid] => 6890685e48d19fd91d4d3656c3079cf3\n    [B3] => 6890685e48d19fd91d4d3656c3079cf3-68fb3ab9ee18d526-1\n    [Traceparent] => 00-6890685e48d19fd91d4d3656c3079cf3-68fb3ab9ee18d526-01\n    [X-Amzn-Trace-Id] => Root=1-6890685e-48d19fd91d4d3656c3079cf3;Parent=68fb3ab9ee18d526;Sampled=1\n    [X-Adsk-Signature] => sha256=61823a93a2089b0f5814787fff2bb6f47f52b7aef4c6b7016f1d661e975f9f8d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9f94313a-6b73-4535-93ef-5b61a1083aea\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:59:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"9f94313a-6b73-4535-93ef-5b61a1083aea","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979451","transactionId":"86140b3f-8aa6-542a-ae56-6a35e0d76b4b","quoteStatus":"Quoted","message":"Quote# Q-979451 status changed to Quoted.","modifiedAt":"2025-08-04T07:59:26.432Z"},"publishedAt":"2025-08-04T07:59:26.000Z","csn":"5103159758"}
[webhook] [2025-08-04 07:59:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 07:59:59
[webhook] [2025-08-04 07:59:59] [adwsapi_v2.php:36]  Provided signature: sha256=84e6b29b22331dfc71bbddc695e209553e09a0426f883f65caa5f43b30927d7a
[webhook] [2025-08-04 07:59:59] [adwsapi_v2.php:37]  Calculated signature: sha256=21c9d02de84736459840bdac4ac95867a06548b406d0c02d395b0a123b71015b
[webhook] [2025-08-04 07:59:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fbb6d79ca10f558a\n    [X-B3-Traceid] => 6890687df3ea9530a01b0c0cf29f3f84\n    [B3] => 6890687df3ea9530a01b0c0cf29f3f84-fbb6d79ca10f558a-1\n    [Traceparent] => 00-6890687df3ea9530a01b0c0cf29f3f84-fbb6d79ca10f558a-01\n    [X-Amzn-Trace-Id] => Root=1-6890687d-f3ea9530a01b0c0cf29f3f84;Parent=fbb6d79ca10f558a;Sampled=1\n    [X-Adsk-Signature] => sha256=84e6b29b22331dfc71bbddc695e209553e09a0426f883f65caa5f43b30927d7a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f45a7b84-a6cd-4541-99cd-1f6991e658ec\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 07:59:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"f45a7b84-a6cd-4541-99cd-1f6991e658ec","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979458","transactionId":"99fefe58-8450-5740-bc64-9f52eee08dc4","quoteStatus":"Draft","message":"Quote# Q-979458 status changed to Draft.","modifiedAt":"2025-08-04T07:59:57.541Z"},"publishedAt":"2025-08-04T07:59:57.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:00:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:00:40
[webhook] [2025-08-04 08:00:40] [adwsapi_v2.php:36]  Provided signature: sha256=ea34be265522fe1d628377bf8497fa210deb5521ed9c8dde7da40437c7d00ee8
[webhook] [2025-08-04 08:00:40] [adwsapi_v2.php:37]  Calculated signature: sha256=4ad7019f7097dbd65fb806b24be302a8481c0d61776d7e79647e54ee8dbc49ce
[webhook] [2025-08-04 08:00:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c5ccb52fc5c14409\n    [X-B3-Traceid] => 689068a5bd62a2f7284f264406faa3f5\n    [B3] => 689068a5bd62a2f7284f264406faa3f5-c5ccb52fc5c14409-1\n    [Traceparent] => 00-689068a5bd62a2f7284f264406faa3f5-c5ccb52fc5c14409-01\n    [X-Amzn-Trace-Id] => Root=1-689068a5-bd62a2f7284f264406faa3f5;Parent=c5ccb52fc5c14409;Sampled=1\n    [X-Adsk-Signature] => sha256=ea34be265522fe1d628377bf8497fa210deb5521ed9c8dde7da40437c7d00ee8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd954f5f-5ff9-4be1-bfde-4a356dbbf22b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:00:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd954f5f-5ff9-4be1-bfde-4a356dbbf22b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979458","transactionId":"99fefe58-8450-5740-bc64-9f52eee08dc4","quoteStatus":"Quoted","message":"Quote# Q-979458 status changed to Quoted.","modifiedAt":"2025-08-04T08:00:37.260Z"},"publishedAt":"2025-08-04T08:00:37.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:04:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:04:23
[webhook] [2025-08-04 08:04:23] [adwsapi_v2.php:36]  Provided signature: sha256=dca8f2df24a2280f4b1a6497ca24d9e60d53568441ccf005f89b52d1a4d82bb4
[webhook] [2025-08-04 08:04:23] [adwsapi_v2.php:37]  Calculated signature: sha256=b236ae0a732de2515f471cd6e771fdeaa6d5af1ba25b161bc5c57c531ce1f453
[webhook] [2025-08-04 08:04:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 09dc1a303b24dff6\n    [X-B3-Traceid] => 6890698536e77aca721310109b28576b\n    [B3] => 6890698536e77aca721310109b28576b-09dc1a303b24dff6-1\n    [Traceparent] => 00-6890698536e77aca721310109b28576b-09dc1a303b24dff6-01\n    [X-Amzn-Trace-Id] => Root=1-68906985-36e77aca721310109b28576b;Parent=09dc1a303b24dff6;Sampled=1\n    [X-Adsk-Signature] => sha256=dca8f2df24a2280f4b1a6497ca24d9e60d53568441ccf005f89b52d1a4d82bb4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8231c765-8e7b-402a-8339-8b1245b875cf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:04:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"8231c765-8e7b-402a-8339-8b1245b875cf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979487","transactionId":"0c4a4009-2fe6-507e-a177-210344555a42","quoteStatus":"Draft","message":"Quote# Q-979487 status changed to Draft.","modifiedAt":"2025-08-04T08:04:21.255Z"},"publishedAt":"2025-08-04T08:04:21.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:04:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:04:52
[webhook] [2025-08-04 08:04:52] [adwsapi_v2.php:36]  Provided signature: sha256=8de460d38b72493cb465fed63fbaf3f59de5851861d75f5168e3de81b6a17dbe
[webhook] [2025-08-04 08:04:52] [adwsapi_v2.php:37]  Calculated signature: sha256=a449e67ad21f0d12dd0cad67ae94feb737775e4cbcda950d9ec3bb59fc1803fb
[webhook] [2025-08-04 08:04:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 437ec6f37abd5e72\n    [X-B3-Traceid] => 689069a2bdfeac8cb7809e33dd950885\n    [B3] => 689069a2bdfeac8cb7809e33dd950885-437ec6f37abd5e72-1\n    [Traceparent] => 00-689069a2bdfeac8cb7809e33dd950885-437ec6f37abd5e72-01\n    [X-Amzn-Trace-Id] => Root=1-689069a2-bdfeac8cb7809e33dd950885;Parent=437ec6f37abd5e72;Sampled=1\n    [X-Adsk-Signature] => sha256=8de460d38b72493cb465fed63fbaf3f59de5851861d75f5168e3de81b6a17dbe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0781da3a-d52f-402f-858b-7ece482675a5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:04:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"0781da3a-d52f-402f-858b-7ece482675a5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979487","transactionId":"0c4a4009-2fe6-507e-a177-210344555a42","quoteStatus":"Quoted","message":"Quote# Q-979487 status changed to Quoted.","modifiedAt":"2025-08-04T08:04:50.253Z"},"publishedAt":"2025-08-04T08:04:50.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:07:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:07:46
[webhook] [2025-08-04 08:07:46] [adwsapi_v2.php:36]  Provided signature: sha256=686019797cd4ea5ecc17ee905d1912c37849e7999b30d2872ff57c87f143470d
[webhook] [2025-08-04 08:07:46] [adwsapi_v2.php:37]  Calculated signature: sha256=eb77801541309804c786d24703b0af95b62f1ccf45b2472c48209147d9d6219c
[webhook] [2025-08-04 08:07:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5f0737a8f1419bbb\n    [X-B3-Traceid] => 68906a4fad09c6a36d95a28f261cedaf\n    [B3] => 68906a4fad09c6a36d95a28f261cedaf-5f0737a8f1419bbb-1\n    [Traceparent] => 00-68906a4fad09c6a36d95a28f261cedaf-5f0737a8f1419bbb-01\n    [X-Amzn-Trace-Id] => Root=1-68906a4f-ad09c6a36d95a28f261cedaf;Parent=5f0737a8f1419bbb;Sampled=1\n    [X-Adsk-Signature] => sha256=686019797cd4ea5ecc17ee905d1912c37849e7999b30d2872ff57c87f143470d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86b501aa-cdba-47a8-8227-567e2db6d50b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:07:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"86b501aa-cdba-47a8-8227-567e2db6d50b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979508","transactionId":"53be095e-f7bc-5167-8f04-68549b3bbc39","quoteStatus":"Draft","message":"Quote# Q-979508 status changed to Draft.","modifiedAt":"2025-08-04T08:07:43.680Z"},"publishedAt":"2025-08-04T08:07:43.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:08:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:08:27
[webhook] [2025-08-04 08:08:27] [adwsapi_v2.php:36]  Provided signature: sha256=6057f425f842701496ca54ac9148c823b030b605c803173103b341820310c1d5
[webhook] [2025-08-04 08:08:27] [adwsapi_v2.php:37]  Calculated signature: sha256=134f1fa2f95b423bbdaee686a2d5dafb6ad399088df69c9f2f5da46e86ca1387
[webhook] [2025-08-04 08:08:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c122dc10ff88272d\n    [X-B3-Traceid] => 68906a7961f655ac8ffd17292c4d651a\n    [B3] => 68906a7961f655ac8ffd17292c4d651a-c122dc10ff88272d-1\n    [Traceparent] => 00-68906a7961f655ac8ffd17292c4d651a-c122dc10ff88272d-01\n    [X-Amzn-Trace-Id] => Root=1-68906a79-61f655ac8ffd17292c4d651a;Parent=c122dc10ff88272d;Sampled=1\n    [X-Adsk-Signature] => sha256=6057f425f842701496ca54ac9148c823b030b605c803173103b341820310c1d5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5206b608-6913-4b08-8460-3cce28e5b933\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:08:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"5206b608-6913-4b08-8460-3cce28e5b933","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979512","transactionId":"61fb31b7-9f50-5c17-ae90-50bdf3e36703","quoteStatus":"Draft","message":"Quote# Q-979512 status changed to Draft.","modifiedAt":"2025-08-04T08:08:24.842Z"},"publishedAt":"2025-08-04T08:08:25.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:09:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:09:01
[webhook] [2025-08-04 08:09:01] [adwsapi_v2.php:36]  Provided signature: sha256=c01305a90651aa0e0ad49a5be8f92a053a7d329fa1a06e9d5633bcb392c8b57f
[webhook] [2025-08-04 08:09:01] [adwsapi_v2.php:37]  Calculated signature: sha256=3626ff66c0fd780039824cb37fed76b55b8281d5f1226854c8a40bf2b240269a
[webhook] [2025-08-04 08:09:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c301ff699c700dd4\n    [X-B3-Traceid] => 68906a9b7aabe976c4557b442b550816\n    [B3] => 68906a9b7aabe976c4557b442b550816-c301ff699c700dd4-1\n    [Traceparent] => 00-68906a9b7aabe976c4557b442b550816-c301ff699c700dd4-01\n    [X-Amzn-Trace-Id] => Root=1-68906a9b-7aabe976c4557b442b550816;Parent=c301ff699c700dd4;Sampled=1\n    [X-Adsk-Signature] => sha256=c01305a90651aa0e0ad49a5be8f92a053a7d329fa1a06e9d5633bcb392c8b57f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 18cf00ff-54d7-47f7-b7c4-13d77cf2ab20\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:09:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"18cf00ff-54d7-47f7-b7c4-13d77cf2ab20","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979512","transactionId":"61fb31b7-9f50-5c17-ae90-50bdf3e36703","quoteStatus":"Quoted","message":"Quote# Q-979512 status changed to Quoted.","modifiedAt":"2025-08-04T08:08:59.198Z"},"publishedAt":"2025-08-04T08:08:59.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:12:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:12:26
[webhook] [2025-08-04 08:12:26] [adwsapi_v2.php:36]  Provided signature: sha256=0baa3d5d2c5c2aee261035bad9b93126ea5b040babdd0fa0339dc0cc2c0faf5a
[webhook] [2025-08-04 08:12:26] [adwsapi_v2.php:37]  Calculated signature: sha256=4e0b3cfc0f757a33bddc2f85b3ce3eab7d22ad7cf4ac5cee7411efb426cc9697
[webhook] [2025-08-04 08:12:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e4eaac5773ab6a6a\n    [X-B3-Traceid] => 68906b6778e267c2cbeb92d378c7de6f\n    [B3] => 68906b6778e267c2cbeb92d378c7de6f-e4eaac5773ab6a6a-1\n    [Traceparent] => 00-68906b6778e267c2cbeb92d378c7de6f-e4eaac5773ab6a6a-01\n    [X-Amzn-Trace-Id] => Root=1-68906b67-78e267c2cbeb92d378c7de6f;Parent=e4eaac5773ab6a6a;Sampled=1\n    [X-Adsk-Signature] => sha256=0baa3d5d2c5c2aee261035bad9b93126ea5b040babdd0fa0339dc0cc2c0faf5a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 36dc8a98-6df0-448e-9677-53486e5f5c62\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:12:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"36dc8a98-6df0-448e-9677-53486e5f5c62","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979541","transactionId":"6d41fdac-6f14-5bb5-b4fa-9ca60280c767","quoteStatus":"Draft","message":"Quote# Q-979541 status changed to Draft.","modifiedAt":"2025-08-04T08:12:23.389Z"},"publishedAt":"2025-08-04T08:12:23.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:12:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:12:53
[webhook] [2025-08-04 08:12:53] [adwsapi_v2.php:36]  Provided signature: sha256=b2a17afff7fe8716b3838a5acdca33239a992569ff9480168012ac8438bbf0bb
[webhook] [2025-08-04 08:12:53] [adwsapi_v2.php:37]  Calculated signature: sha256=6703c1a898d821f9690f3ef858217dce9e375ad55bf450749998e3f3570f1490
[webhook] [2025-08-04 08:12:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 30eaa650ccfb057d\n    [X-B3-Traceid] => 68906b8358ed0d61a3ee544dbe8572fe\n    [B3] => 68906b8358ed0d61a3ee544dbe8572fe-30eaa650ccfb057d-1\n    [Traceparent] => 00-68906b8358ed0d61a3ee544dbe8572fe-30eaa650ccfb057d-01\n    [X-Amzn-Trace-Id] => Root=1-68906b83-58ed0d61a3ee544dbe8572fe;Parent=30eaa650ccfb057d;Sampled=1\n    [X-Adsk-Signature] => sha256=b2a17afff7fe8716b3838a5acdca33239a992569ff9480168012ac8438bbf0bb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5d0982e1-170c-47a8-a408-7e92bc3f8546\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:12:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"5d0982e1-170c-47a8-a408-7e92bc3f8546","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979541","transactionId":"6d41fdac-6f14-5bb5-b4fa-9ca60280c767","quoteStatus":"Quoted","message":"Quote# Q-979541 status changed to Quoted.","modifiedAt":"2025-08-04T08:12:51.334Z"},"publishedAt":"2025-08-04T08:12:51.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:17:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:17:11
[webhook] [2025-08-04 08:17:11] [adwsapi_v2.php:36]  Provided signature: sha256=8916adea0a3ee3859624746686c2c1d8c28dffd6f28cd99203d82ca4295b5084
[webhook] [2025-08-04 08:17:11] [adwsapi_v2.php:37]  Calculated signature: sha256=9c980c6ea99a1ff94a1d9e994f4db79a0f64393de6503019c99cc38b83278bdf
[webhook] [2025-08-04 08:17:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1dd594e6cd6f001b\n    [X-B3-Traceid] => 68906c85d012688c334278312fa4e132\n    [B3] => 68906c85d012688c334278312fa4e132-1dd594e6cd6f001b-1\n    [Traceparent] => 00-68906c85d012688c334278312fa4e132-1dd594e6cd6f001b-01\n    [X-Amzn-Trace-Id] => Root=1-68906c85-d012688c334278312fa4e132;Parent=1dd594e6cd6f001b;Sampled=1\n    [X-Adsk-Signature] => sha256=8916adea0a3ee3859624746686c2c1d8c28dffd6f28cd99203d82ca4295b5084\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5869d9f7-b0d3-4a44-915f-1acd9f814aa1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:17:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"5869d9f7-b0d3-4a44-915f-1acd9f814aa1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979570","transactionId":"2c100a45-4b43-5262-82ce-6b4f25824d75","quoteStatus":"Draft","message":"Quote# Q-979570 status changed to Draft.","modifiedAt":"2025-08-04T08:17:09.236Z"},"publishedAt":"2025-08-04T08:17:09.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:21:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:21:20
[webhook] [2025-08-04 08:21:20] [adwsapi_v2.php:36]  Provided signature: sha256=6f8320305e6d10c36bbd4ea5b83f56a8b2b377f1840d0364efc3962e40ea4e2f
[webhook] [2025-08-04 08:21:20] [adwsapi_v2.php:37]  Calculated signature: sha256=e34ff7cf9cc375ec59ca3d85d1d0a1f2011a72731c72d632520bbfafb6abbbbb
[webhook] [2025-08-04 08:21:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 977885178009f897\n    [X-B3-Traceid] => 68906d7e7b758424dfa845772ce2bcb0\n    [B3] => 68906d7e7b758424dfa845772ce2bcb0-977885178009f897-1\n    [Traceparent] => 00-68906d7e7b758424dfa845772ce2bcb0-977885178009f897-01\n    [X-Amzn-Trace-Id] => Root=1-68906d7e-7b758424dfa845772ce2bcb0;Parent=977885178009f897;Sampled=1\n    [X-Adsk-Signature] => sha256=6f8320305e6d10c36bbd4ea5b83f56a8b2b377f1840d0364efc3962e40ea4e2f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bb4446b9-e004-417e-b29e-8002d3db141d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:21:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"bb4446b9-e004-417e-b29e-8002d3db141d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979595","transactionId":"d03ca35c-709a-51b8-aff1-c2561f346a39","quoteStatus":"Draft","message":"Quote# Q-979595 status changed to Draft.","modifiedAt":"2025-08-04T08:21:18.684Z"},"publishedAt":"2025-08-04T08:21:18.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:22:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:22:13
[webhook] [2025-08-04 08:22:13] [adwsapi_v2.php:36]  Provided signature: sha256=0b0e821edf07a5d4536bb069a7ae5eaff3231b1a524d716d279f47b5d2e33e97
[webhook] [2025-08-04 08:22:13] [adwsapi_v2.php:37]  Calculated signature: sha256=25f677967211b367df4c577c7117dcae06fab68d0073db27fb7f5f811b921016
[webhook] [2025-08-04 08:22:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1981994dfc5bfb91\n    [X-B3-Traceid] => 68906db206a7f2353bc55f5ea751ed0d\n    [B3] => 68906db206a7f2353bc55f5ea751ed0d-1981994dfc5bfb91-1\n    [Traceparent] => 00-68906db206a7f2353bc55f5ea751ed0d-1981994dfc5bfb91-01\n    [X-Amzn-Trace-Id] => Root=1-68906db2-06a7f2353bc55f5ea751ed0d;Parent=1981994dfc5bfb91;Sampled=1\n    [X-Adsk-Signature] => sha256=0b0e821edf07a5d4536bb069a7ae5eaff3231b1a524d716d279f47b5d2e33e97\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dc9a07c7-739b-4533-8b59-2e412422ae28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:22:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"dc9a07c7-739b-4533-8b59-2e412422ae28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979595","transactionId":"d03ca35c-709a-51b8-aff1-c2561f346a39","quoteStatus":"Quoted","message":"Quote# Q-979595 status changed to Quoted.","modifiedAt":"2025-08-04T08:22:10.488Z"},"publishedAt":"2025-08-04T08:22:10.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:22:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:22:42
[webhook] [2025-08-04 08:22:42] [adwsapi_v2.php:36]  Provided signature: sha256=0ec8e79a693f63b3272d13b88c99f09f0d2eb312db5ea00f372185eb59c5cfce
[webhook] [2025-08-04 08:22:42] [adwsapi_v2.php:37]  Calculated signature: sha256=91524bae55ac271145d5858c823121416a8ba61c38073c883ea0159b43dab0f4
[webhook] [2025-08-04 08:22:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a4b9a16fe46c6aa3\n    [X-B3-Traceid] => 68906dcf6a65d4e04a90b8b93e11fd03\n    [B3] => 68906dcf6a65d4e04a90b8b93e11fd03-a4b9a16fe46c6aa3-1\n    [Traceparent] => 00-68906dcf6a65d4e04a90b8b93e11fd03-a4b9a16fe46c6aa3-01\n    [X-Amzn-Trace-Id] => Root=1-68906dcf-6a65d4e04a90b8b93e11fd03;Parent=a4b9a16fe46c6aa3;Sampled=1\n    [X-Adsk-Signature] => sha256=0ec8e79a693f63b3272d13b88c99f09f0d2eb312db5ea00f372185eb59c5cfce\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 15332ea1-77d9-40ba-9be8-91afdbc781c6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:22:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"15332ea1-77d9-40ba-9be8-91afdbc781c6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979601","transactionId":"87e453b3-75e8-5ae5-9b2a-32b905e7205d","quoteStatus":"Draft","message":"Quote# Q-979601 status changed to Draft.","modifiedAt":"2025-08-04T08:22:39.883Z"},"publishedAt":"2025-08-04T08:22:40.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:23:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:23:11
[webhook] [2025-08-04 08:23:11] [adwsapi_v2.php:36]  Provided signature: sha256=e4af7484fa7c2d1800a24e0aa092ea915ca193c5c9a0d9d9d8f6f931cdc92c50
[webhook] [2025-08-04 08:23:11] [adwsapi_v2.php:37]  Calculated signature: sha256=789293f2c75100f577bb1a6458b931c5c2a6ce6767ba1641e43189013ab80b5f
[webhook] [2025-08-04 08:23:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 59a8402fa863b25a\n    [X-B3-Traceid] => 68906ded10dd3e3605f8a349303af16b\n    [B3] => 68906ded10dd3e3605f8a349303af16b-59a8402fa863b25a-1\n    [Traceparent] => 00-68906ded10dd3e3605f8a349303af16b-59a8402fa863b25a-01\n    [X-Amzn-Trace-Id] => Root=1-68906ded-10dd3e3605f8a349303af16b;Parent=59a8402fa863b25a;Sampled=1\n    [X-Adsk-Signature] => sha256=e4af7484fa7c2d1800a24e0aa092ea915ca193c5c9a0d9d9d8f6f931cdc92c50\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6b443b6f-4a35-4aa5-bcac-427ad343b286\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:23:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"6b443b6f-4a35-4aa5-bcac-427ad343b286","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979601","transactionId":"87e453b3-75e8-5ae5-9b2a-32b905e7205d","quoteStatus":"Quoted","message":"Quote# Q-979601 status changed to Quoted.","modifiedAt":"2025-08-04T08:23:09.397Z"},"publishedAt":"2025-08-04T08:23:09.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:23:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:23:31
[webhook] [2025-08-04 08:23:31] [adwsapi_v2.php:36]  Provided signature: sha256=18caebdd9a22afbd7da28d7ecbcc123b527b5a4b38167919225f1d519badb248
[webhook] [2025-08-04 08:23:31] [adwsapi_v2.php:37]  Calculated signature: sha256=05aa92e5888cc3cef0be96cc16c1f06645088d1fad3cd98bd1b4f230e42e88ff
[webhook] [2025-08-04 08:23:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5f4ac781b45a05e5\n    [X-B3-Traceid] => 68906e0049bb01a5f854574f719739c0\n    [B3] => 68906e0049bb01a5f854574f719739c0-5f4ac781b45a05e5-1\n    [Traceparent] => 00-68906e0049bb01a5f854574f719739c0-5f4ac781b45a05e5-01\n    [X-Amzn-Trace-Id] => Root=1-68906e00-49bb01a5f854574f719739c0;Parent=5f4ac781b45a05e5;Sampled=1\n    [X-Adsk-Signature] => sha256=18caebdd9a22afbd7da28d7ecbcc123b527b5a4b38167919225f1d519badb248\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a1d5d89b-ab75-44a3-b9da-dedab3629afe\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:23:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"a1d5d89b-ab75-44a3-b9da-dedab3629afe","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979608","transactionId":"19220073-ed75-555b-9159-61799d4006dc","quoteStatus":"Draft","message":"Quote# Q-979608 status changed to Draft.","modifiedAt":"2025-08-04T08:23:28.604Z"},"publishedAt":"2025-08-04T08:23:29.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:24:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:24:05
[webhook] [2025-08-04 08:24:05] [adwsapi_v2.php:36]  Provided signature: sha256=2ea8678df1264b76b6dad32d9e0c6f6a38a9ae8229998d1564c7be5a7f0ba716
[webhook] [2025-08-04 08:24:05] [adwsapi_v2.php:37]  Calculated signature: sha256=5faccfa6558ce2c1ec83ab980905c27f5812e6f989e3e421879f64d92c4cce1e
[webhook] [2025-08-04 08:24:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5b139970f79b386e\n    [X-B3-Traceid] => 68906e2365fbc9b46f142d217f10191b\n    [B3] => 68906e2365fbc9b46f142d217f10191b-5b139970f79b386e-1\n    [Traceparent] => 00-68906e2365fbc9b46f142d217f10191b-5b139970f79b386e-01\n    [X-Amzn-Trace-Id] => Root=1-68906e23-65fbc9b46f142d217f10191b;Parent=5b139970f79b386e;Sampled=1\n    [X-Adsk-Signature] => sha256=2ea8678df1264b76b6dad32d9e0c6f6a38a9ae8229998d1564c7be5a7f0ba716\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f0965d92-8683-4d6e-a97e-6ac50420f9fc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:24:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"f0965d92-8683-4d6e-a97e-6ac50420f9fc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979608","transactionId":"19220073-ed75-555b-9159-61799d4006dc","quoteStatus":"Quoted","message":"Quote# Q-979608 status changed to Quoted.","modifiedAt":"2025-08-04T08:24:03.080Z"},"publishedAt":"2025-08-04T08:24:03.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:24:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:24:48
[webhook] [2025-08-04 08:24:48] [adwsapi_v2.php:36]  Provided signature: sha256=111c1dfc02335d30dc8f29c72bec7843c9fff4f3c0bd74ac2fcf652954963c90
[webhook] [2025-08-04 08:24:48] [adwsapi_v2.php:37]  Calculated signature: sha256=a548c971213c052adaa89a88ca96234d4c03a4072b1dbcdc344156c1ddd15de7
[webhook] [2025-08-04 08:24:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f530b17b2c32a3c8\n    [X-B3-Traceid] => 68906e4e3b01c3ca6bb0ad233c72ff88\n    [B3] => 68906e4e3b01c3ca6bb0ad233c72ff88-f530b17b2c32a3c8-1\n    [Traceparent] => 00-68906e4e3b01c3ca6bb0ad233c72ff88-f530b17b2c32a3c8-01\n    [X-Amzn-Trace-Id] => Root=1-68906e4e-3b01c3ca6bb0ad233c72ff88;Parent=f530b17b2c32a3c8;Sampled=1\n    [X-Adsk-Signature] => sha256=111c1dfc02335d30dc8f29c72bec7843c9fff4f3c0bd74ac2fcf652954963c90\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5aee135a-4ceb-42c8-9d14-d4628c18f9d6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:24:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"5aee135a-4ceb-42c8-9d14-d4628c18f9d6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979616","transactionId":"576afb74-fd9f-556a-ac25-9a7e8d16f607","quoteStatus":"Draft","message":"Quote# Q-979616 status changed to Draft.","modifiedAt":"2025-08-04T08:24:46.376Z"},"publishedAt":"2025-08-04T08:24:46.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:26:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:26:23
[webhook] [2025-08-04 08:26:23] [adwsapi_v2.php:36]  Provided signature: sha256=ae5ea6640a6b5e7bbf9ebd020c6bb6f1f0c1cfe7c6b6109866882c3a655481eb
[webhook] [2025-08-04 08:26:23] [adwsapi_v2.php:37]  Calculated signature: sha256=33928f42b1d71f4a75d74808f57c2b20c0892b68822f6b4f56a4e2f7bbc31b3b
[webhook] [2025-08-04 08:26:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e8976ca4ed6c0601\n    [X-B3-Traceid] => 68906eace8df690d95c8388e8f151001\n    [B3] => 68906eace8df690d95c8388e8f151001-e8976ca4ed6c0601-1\n    [Traceparent] => 00-68906eace8df690d95c8388e8f151001-e8976ca4ed6c0601-01\n    [X-Amzn-Trace-Id] => Root=1-68906eac-e8df690d95c8388e8f151001;Parent=e8976ca4ed6c0601;Sampled=1\n    [X-Adsk-Signature] => sha256=ae5ea6640a6b5e7bbf9ebd020c6bb6f1f0c1cfe7c6b6109866882c3a655481eb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 36d8e6a1-e15f-4237-af27-ea449fedfd1d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:26:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"36d8e6a1-e15f-4237-af27-ea449fedfd1d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979616","transactionId":"576afb74-fd9f-556a-ac25-9a7e8d16f607","quoteStatus":"Under Review","message":"Quote# Q-979616 status changed to Under Review.","modifiedAt":"2025-08-04T08:26:20.632Z"},"publishedAt":"2025-08-04T08:26:20.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:27:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:27:32
[webhook] [2025-08-04 08:27:32] [adwsapi_v2.php:36]  Provided signature: sha256=c4bb5747a8dd24ca5efe21b3e9300f4e599993a58f5d4a414a45100cbf1194a0
[webhook] [2025-08-04 08:27:32] [adwsapi_v2.php:37]  Calculated signature: sha256=3c6425037aa987fd61b4dacfc8f2cc9860ca8ded421619df13da6a939b9ebd3b
[webhook] [2025-08-04 08:27:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 695603a8a44ce9e8\n    [X-B3-Traceid] => 68906ef2a81cde98c6ae22963ac12725\n    [B3] => 68906ef2a81cde98c6ae22963ac12725-695603a8a44ce9e8-1\n    [Traceparent] => 00-68906ef2a81cde98c6ae22963ac12725-695603a8a44ce9e8-01\n    [X-Amzn-Trace-Id] => Root=1-68906ef2-a81cde98c6ae22963ac12725;Parent=695603a8a44ce9e8;Sampled=1\n    [X-Adsk-Signature] => sha256=c4bb5747a8dd24ca5efe21b3e9300f4e599993a58f5d4a414a45100cbf1194a0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dafaaf27-b8c1-406a-b2ef-d629ea0ee6fe\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:27:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"dafaaf27-b8c1-406a-b2ef-d629ea0ee6fe","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979508","transactionId":"53be095e-f7bc-5167-8f04-68549b3bbc39","quoteStatus":"Quoted","message":"Quote# Q-979508 status changed to Quoted.","modifiedAt":"2025-08-04T08:27:29.944Z"},"publishedAt":"2025-08-04T08:27:30.000Z","csn":"5103159758"}
[webhook] [2025-08-04 08:45:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 08:45:59
[webhook] [2025-08-04 08:45:59] [adwsapi_v2.php:36]  Provided signature: sha256=c7f32634d0f4b67f197fe292aa6e504d009c4f1e0be3120ffce6e648ed2aada4
[webhook] [2025-08-04 08:45:59] [adwsapi_v2.php:37]  Calculated signature: sha256=10997b42f9228e528f42030677586cb4a0f05b9d166768235799441b570f9091
[webhook] [2025-08-04 08:45:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 83444a9420481754\n    [X-B3-Traceid] => 689073446367decb161ef82c061d2400\n    [B3] => 689073446367decb161ef82c061d2400-83444a9420481754-1\n    [Traceparent] => 00-689073446367decb161ef82c061d2400-83444a9420481754-01\n    [X-Amzn-Trace-Id] => Root=1-68907344-6367decb161ef82c061d2400;Parent=83444a9420481754;Sampled=1\n    [X-Adsk-Signature] => sha256=c7f32634d0f4b67f197fe292aa6e504d009c4f1e0be3120ffce6e648ed2aada4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 73242d1c-3e14-4de8-9358-99ba502cc779\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 08:45:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"73242d1c-3e14-4de8-9358-99ba502cc779","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66126167207160","status":"Active","quantity":1,"endDate":"2026-09-01","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T08:30:54.000+0000"},"publishedAt":"2025-08-04T08:45:56.000Z","csn":"5103159758"}
[webhook] [2025-08-04 09:10:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 09:10:09
[webhook] [2025-08-04 09:10:09] [adwsapi_v2.php:36]  Provided signature: sha256=cd650aba410554e507a920969fe118ceae35612f7f36dbb1a63b84496beb2a98
[webhook] [2025-08-04 09:10:09] [adwsapi_v2.php:37]  Calculated signature: sha256=06d5b343f389033e4e5215eda2263b009684cdac82a3d6264adfba52e9accc90
[webhook] [2025-08-04 09:10:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fcfb558473f80813\n    [X-B3-Traceid] => 689078ef1df2fc9e384e8d50340a0a4a\n    [B3] => 689078ef1df2fc9e384e8d50340a0a4a-fcfb558473f80813-1\n    [Traceparent] => 00-689078ef1df2fc9e384e8d50340a0a4a-fcfb558473f80813-01\n    [X-Amzn-Trace-Id] => Root=1-689078ef-1df2fc9e384e8d50340a0a4a;Parent=fcfb558473f80813;Sampled=1\n    [X-Adsk-Signature] => sha256=cd650aba410554e507a920969fe118ceae35612f7f36dbb1a63b84496beb2a98\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 56d1aab8-f732-4f51-94cf-53ec2de64753\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 09:10:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"56d1aab8-f732-4f51-94cf-53ec2de64753","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55595157491612","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-04T08:55:05.000+0000"},"publishedAt":"2025-08-04T09:10:07.000Z","csn":"5103159758"}
[webhook] [2025-08-04 09:12:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 09:12:50
[webhook] [2025-08-04 09:12:50] [adwsapi_v2.php:36]  Provided signature: sha256=10d4cc90a9ac71c2d3100afd02c957dd6cb2707fce269815a63305aee2137bde
[webhook] [2025-08-04 09:12:50] [adwsapi_v2.php:37]  Calculated signature: sha256=365ddc2d6f982d5c7490aa392b9e27ab4d1c7fbce0177a4a7d6fbbf2599d8fb6
[webhook] [2025-08-04 09:12:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c840a9d07a8e43a8\n    [X-B3-Traceid] => 689079907ff8966ea664c5bd01e53d6e\n    [B3] => 689079907ff8966ea664c5bd01e53d6e-c840a9d07a8e43a8-1\n    [Traceparent] => 00-689079907ff8966ea664c5bd01e53d6e-c840a9d07a8e43a8-01\n    [X-Amzn-Trace-Id] => Root=1-68907990-7ff8966ea664c5bd01e53d6e;Parent=c840a9d07a8e43a8;Sampled=1\n    [X-Adsk-Signature] => sha256=10d4cc90a9ac71c2d3100afd02c957dd6cb2707fce269815a63305aee2137bde\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e356e7be-b4f8-4132-9b31-fb5ffb22da27\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 09:12:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"e356e7be-b4f8-4132-9b31-fb5ffb22da27","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979872","transactionId":"8df7a347-b6db-5ef0-959b-c24dcd13db69","quoteStatus":"Draft","message":"Quote# Q-979872 status changed to Draft.","modifiedAt":"2025-08-04T09:12:47.878Z"},"publishedAt":"2025-08-04T09:12:48.000Z","csn":"5103159758"}
[webhook] [2025-08-04 09:13:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 09:13:42
[webhook] [2025-08-04 09:13:42] [adwsapi_v2.php:36]  Provided signature: sha256=3d18c7f962d2b345e4077d65238595ea4a3c4ff4af89d3de16000c31ad1c52f9
[webhook] [2025-08-04 09:13:42] [adwsapi_v2.php:37]  Calculated signature: sha256=ad5c667fe7a99085f1b316978a54a14c3253fce0bc1bca8cbe36322bfe65f23f
[webhook] [2025-08-04 09:13:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c7f55bd68eb6cef9\n    [X-B3-Traceid] => 689079c4443fb7948af32a39e937c834\n    [B3] => 689079c4443fb7948af32a39e937c834-c7f55bd68eb6cef9-1\n    [Traceparent] => 00-689079c4443fb7948af32a39e937c834-c7f55bd68eb6cef9-01\n    [X-Amzn-Trace-Id] => Root=1-689079c4-443fb7948af32a39e937c834;Parent=c7f55bd68eb6cef9;Sampled=1\n    [X-Adsk-Signature] => sha256=3d18c7f962d2b345e4077d65238595ea4a3c4ff4af89d3de16000c31ad1c52f9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 449d0ad7-c7c2-4e03-96cd-afbf6b331b4a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 09:13:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"449d0ad7-c7c2-4e03-96cd-afbf6b331b4a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979872","transactionId":"8df7a347-b6db-5ef0-959b-c24dcd13db69","quoteStatus":"Quoted","message":"Quote# Q-979872 status changed to Quoted.","modifiedAt":"2025-08-04T09:13:39.999Z"},"publishedAt":"2025-08-04T09:13:40.000Z","csn":"5103159758"}
[webhook] [2025-08-04 09:39:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 09:39:24
[webhook] [2025-08-04 09:39:24] [adwsapi_v2.php:36]  Provided signature: sha256=63e8068182c3d2ee1c77da06c1745094300bf0898c4ef88ccf7cc4729a02f886
[webhook] [2025-08-04 09:39:24] [adwsapi_v2.php:37]  Calculated signature: sha256=b191f69980001d555dd82f5354ebd0a3feba6444844711b5fcd3f3a2b102072e
[webhook] [2025-08-04 09:39:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f1bc4d1328cbba46\n    [X-B3-Traceid] => 68907fc97563048441a5f7ea8958beed\n    [B3] => 68907fc97563048441a5f7ea8958beed-f1bc4d1328cbba46-1\n    [Traceparent] => 00-68907fc97563048441a5f7ea8958beed-f1bc4d1328cbba46-01\n    [X-Amzn-Trace-Id] => Root=1-68907fc9-7563048441a5f7ea8958beed;Parent=f1bc4d1328cbba46;Sampled=1\n    [X-Adsk-Signature] => sha256=63e8068182c3d2ee1c77da06c1745094300bf0898c4ef88ccf7cc4729a02f886\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e22403af-327f-4f52-a3a4-16d0b36ba537\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 09:39:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"e22403af-327f-4f52-a3a4-16d0b36ba537","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979872","transactionId":"8df7a347-b6db-5ef0-959b-c24dcd13db69","quoteStatus":"Order Submitted","message":"Quote# Q-979872 status changed to Order Submitted.","modifiedAt":"2025-08-04T09:39:21.710Z"},"publishedAt":"2025-08-04T09:39:22.000Z","csn":"5103159758"}
[webhook] [2025-08-04 09:39:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 09:39:26
[webhook] [2025-08-04 09:39:26] [adwsapi_v2.php:36]  Provided signature: sha256=507fdbff8094e5645ea0eb826e462f7cf8ab3132cb4a8ba7ed830ca2c06681c8
[webhook] [2025-08-04 09:39:26] [adwsapi_v2.php:37]  Calculated signature: sha256=eb4e2f986a795cfdb8eec141c20499b8373b55c1d18dfeebaf617fb3fd8d2737
[webhook] [2025-08-04 09:39:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 874c6436178ca551\n    [X-B3-Traceid] => 68907fcb0975a38cff3cf799395d6f3e\n    [B3] => 68907fcb0975a38cff3cf799395d6f3e-874c6436178ca551-1\n    [Traceparent] => 00-68907fcb0975a38cff3cf799395d6f3e-874c6436178ca551-01\n    [X-Amzn-Trace-Id] => Root=1-68907fcb-0975a38cff3cf799395d6f3e;Parent=874c6436178ca551;Sampled=1\n    [X-Adsk-Signature] => sha256=507fdbff8094e5645ea0eb826e462f7cf8ab3132cb4a8ba7ed830ca2c06681c8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5e420e40-b5ed-45f1-8198-9ca62e64f393\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 09:39:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"5e420e40-b5ed-45f1-8198-9ca62e64f393","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979872","transactionId":"8df7a347-b6db-5ef0-959b-c24dcd13db69","quoteStatus":"Ordered","message":"Quote# Q-979872 status changed to Ordered.","modifiedAt":"2025-08-04T09:39:23.820Z"},"publishedAt":"2025-08-04T09:39:24.000Z","csn":"5103159758"}
[webhook] [2025-08-04 09:43:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 09:43:19
[webhook] [2025-08-04 09:43:19] [adwsapi_v2.php:36]  Provided signature: sha256=09cf6a82c2553d35bd464e95e44f9b1e633e1d24ad6b455572afc9bfd6219d41
[webhook] [2025-08-04 09:43:19] [adwsapi_v2.php:37]  Calculated signature: sha256=5c8dd8c50cc0b6c69d2c5418f1c7c95fc94fd1d936125a1e46e42bb3c62e83b1
[webhook] [2025-08-04 09:43:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b3bcaef5f3febf39\n    [X-B3-Traceid] => 689080b445091b393ffa15a91427a4bf\n    [B3] => 689080b445091b393ffa15a91427a4bf-b3bcaef5f3febf39-1\n    [Traceparent] => 00-689080b445091b393ffa15a91427a4bf-b3bcaef5f3febf39-01\n    [X-Amzn-Trace-Id] => Root=1-689080b4-45091b393ffa15a91427a4bf;Parent=b3bcaef5f3febf39;Sampled=1\n    [X-Adsk-Signature] => sha256=09cf6a82c2553d35bd464e95e44f9b1e633e1d24ad6b455572afc9bfd6219d41\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b564ca54-3c7b-4b8c-be9c-8914c9d8f331\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 09:43:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"b564ca54-3c7b-4b8c-be9c-8914c9d8f331","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"54341697443157","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-04T09:28:14.000+0000"},"publishedAt":"2025-08-04T09:43:17.000Z","csn":"5103159758"}
[webhook] [2025-08-04 10:09:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 10:09:35
[webhook] [2025-08-04 10:09:35] [adwsapi_v2.php:36]  Provided signature: sha256=f3d654753e0f02bea85b13cf2462af2acfba190d77d00e2ffb7be427bb60678d
[webhook] [2025-08-04 10:09:35] [adwsapi_v2.php:37]  Calculated signature: sha256=fd48e3913a7353338da104a8bc2274cf3dcb6977604daa5d1d29a1a2c5e3c95d
[webhook] [2025-08-04 10:09:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 95c88d140b588c17\n    [X-B3-Traceid] => 689086dc4ed7129e5e34409f2ae706a3\n    [B3] => 689086dc4ed7129e5e34409f2ae706a3-95c88d140b588c17-1\n    [Traceparent] => 00-689086dc4ed7129e5e34409f2ae706a3-95c88d140b588c17-01\n    [X-Amzn-Trace-Id] => Root=1-689086dc-4ed7129e5e34409f2ae706a3;Parent=95c88d140b588c17;Sampled=1\n    [X-Adsk-Signature] => sha256=f3d654753e0f02bea85b13cf2462af2acfba190d77d00e2ffb7be427bb60678d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => efe5ced5-2222-4a8a-911a-b74ad53700ba\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 10:09:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"efe5ced5-2222-4a8a-911a-b74ad53700ba","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56743625158902","status":"Active","quantity":1,"endDate":"2026-09-01","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T09:39:30.000+0000"},"publishedAt":"2025-08-04T10:09:32.000Z","csn":"5103159758"}
[webhook] [2025-08-04 10:53:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 10:53:02
[webhook] [2025-08-04 10:53:02] [adwsapi_v2.php:36]  Provided signature: sha256=671f5bc30d01fd9b1affb2b7db4696062024d09208be3c406d825ea939cc6090
[webhook] [2025-08-04 10:53:02] [adwsapi_v2.php:37]  Calculated signature: sha256=a7fca326d665b215547cf3d1a1e87304e108fd3660d495bd84fb3be307fe81c1
[webhook] [2025-08-04 10:53:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5634f15d38ee87e4\n    [X-B3-Traceid] => 6890910c902446fb3415a061baa1de74\n    [B3] => 6890910c902446fb3415a061baa1de74-5634f15d38ee87e4-1\n    [Traceparent] => 00-6890910c902446fb3415a061baa1de74-5634f15d38ee87e4-01\n    [X-Amzn-Trace-Id] => Root=1-6890910c-902446fb3415a061baa1de74;Parent=5634f15d38ee87e4;Sampled=1\n    [X-Adsk-Signature] => sha256=671f5bc30d01fd9b1affb2b7db4696062024d09208be3c406d825ea939cc6090\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8e37a30d-8903-4c03-8d45-f4b728056bfb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 10:53:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"8e37a30d-8903-4c03-8d45-f4b728056bfb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979434","transactionId":"046a3ccb-1d58-5a6a-b125-5dc1748af494","quoteStatus":"Order Submitted","message":"Quote# Q-979434 status changed to Order Submitted.","modifiedAt":"2025-08-04T10:52:59.706Z"},"publishedAt":"2025-08-04T10:53:00.000Z","csn":"5103159758"}
[webhook] [2025-08-04 10:53:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 10:53:04
[webhook] [2025-08-04 10:53:04] [adwsapi_v2.php:36]  Provided signature: sha256=0230248f0541a006a2019d3639cf415468934b00774c586ba8aaccca43153f28
[webhook] [2025-08-04 10:53:04] [adwsapi_v2.php:37]  Calculated signature: sha256=0611c93ce1fee0ab8f5760eee342f9dcabd376a249b42be853d5942d24843c60
[webhook] [2025-08-04 10:53:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 92225127640d31c4\n    [X-B3-Traceid] => 6890910d1bf5adf841fcec460b7134a2\n    [B3] => 6890910d1bf5adf841fcec460b7134a2-92225127640d31c4-1\n    [Traceparent] => 00-6890910d1bf5adf841fcec460b7134a2-92225127640d31c4-01\n    [X-Amzn-Trace-Id] => Root=1-6890910d-1bf5adf841fcec460b7134a2;Parent=92225127640d31c4;Sampled=1\n    [X-Adsk-Signature] => sha256=0230248f0541a006a2019d3639cf415468934b00774c586ba8aaccca43153f28\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d51dc120-85a1-4a31-8f7b-196e13560844\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 10:53:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"d51dc120-85a1-4a31-8f7b-196e13560844","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979434","transactionId":"046a3ccb-1d58-5a6a-b125-5dc1748af494","quoteStatus":"Ordered","message":"Quote# Q-979434 status changed to Ordered.","modifiedAt":"2025-08-04T10:53:01.672Z"},"publishedAt":"2025-08-04T10:53:02.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:13:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:13:06
[webhook] [2025-08-04 11:13:06] [adwsapi_v2.php:36]  Provided signature: sha256=b293aef9b390ded80c987e1a1305737c11e6a6c5d0de927ab0a095da01a1a374
[webhook] [2025-08-04 11:13:06] [adwsapi_v2.php:37]  Calculated signature: sha256=f0c258d3e8f5e3576b023c56fbde7d208ee970f45b8146e3fb8a40c935720dd8
[webhook] [2025-08-04 11:13:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a3b211c4b7201cff\n    [X-B3-Traceid] => 689095bf1072ee2a48c0c87778da8300\n    [B3] => 689095bf1072ee2a48c0c87778da8300-a3b211c4b7201cff-1\n    [Traceparent] => 00-689095bf1072ee2a48c0c87778da8300-a3b211c4b7201cff-01\n    [X-Amzn-Trace-Id] => Root=1-689095bf-1072ee2a48c0c87778da8300;Parent=a3b211c4b7201cff;Sampled=1\n    [X-Adsk-Signature] => sha256=b293aef9b390ded80c987e1a1305737c11e6a6c5d0de927ab0a095da01a1a374\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3176b2c6-ebf6-45d1-966d-761bfa650ff2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:13:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"3176b2c6-ebf6-45d1-966d-761bfa650ff2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69381769773020","status":"Active","quantity":1,"endDate":"2026-09-03","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T10:53:08.000+0000"},"publishedAt":"2025-08-04T11:13:03.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:15:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:15:01
[webhook] [2025-08-04 11:15:01] [adwsapi_v2.php:36]  Provided signature: sha256=82473a528d745144bc621348e8d9c8d0bb17b1d6ee177dd66382dbb3f7e08e3f
[webhook] [2025-08-04 11:15:01] [adwsapi_v2.php:37]  Calculated signature: sha256=536f14faa9aaf1c5b169c4ae64f3bf8e737723efc47fa23d48b65c019d780f4b
[webhook] [2025-08-04 11:15:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 791007717d6c5d17\n    [X-B3-Traceid] => 689096329d8ef4bc31e22e925c6ff7f9\n    [B3] => 689096329d8ef4bc31e22e925c6ff7f9-791007717d6c5d17-1\n    [Traceparent] => 00-689096329d8ef4bc31e22e925c6ff7f9-791007717d6c5d17-01\n    [X-Amzn-Trace-Id] => Root=1-68909632-9d8ef4bc31e22e925c6ff7f9;Parent=791007717d6c5d17;Sampled=1\n    [X-Adsk-Signature] => sha256=82473a528d745144bc621348e8d9c8d0bb17b1d6ee177dd66382dbb3f7e08e3f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7e658abd-0464-4f2d-a9fe-3d08dc930924\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:15:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"7e658abd-0464-4f2d-a9fe-3d08dc930924","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-980363","transactionId":"afbabe9a-6dc7-5fc5-8d17-df90f5a88454","quoteStatus":"Draft","message":"Quote# Q-980363 status changed to Draft.","modifiedAt":"2025-08-04T11:14:58.536Z"},"publishedAt":"2025-08-04T11:14:58.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:16:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:16:25
[webhook] [2025-08-04 11:16:25] [adwsapi_v2.php:36]  Provided signature: sha256=ab5278c15426268f3304cc80e7b8d3c6ac466d4c8e39e0909d4412a44190d31c
[webhook] [2025-08-04 11:16:25] [adwsapi_v2.php:37]  Calculated signature: sha256=75be68f601f8a153fb21cb8828ab61dfafead5dc8927371a215bcf6218eec766
[webhook] [2025-08-04 11:16:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f1f852c3ea42d7b9\n    [X-B3-Traceid] => 689096878d153ceef5f44c4396097c5f\n    [B3] => 689096878d153ceef5f44c4396097c5f-f1f852c3ea42d7b9-1\n    [Traceparent] => 00-689096878d153ceef5f44c4396097c5f-f1f852c3ea42d7b9-01\n    [X-Amzn-Trace-Id] => Root=1-68909687-8d153ceef5f44c4396097c5f;Parent=f1f852c3ea42d7b9;Sampled=1\n    [X-Adsk-Signature] => sha256=ab5278c15426268f3304cc80e7b8d3c6ac466d4c8e39e0909d4412a44190d31c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8dd34380-a1bf-42ef-ace4-9aaf99ccb811\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:16:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"8dd34380-a1bf-42ef-ace4-9aaf99ccb811","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-980363","transactionId":"afbabe9a-6dc7-5fc5-8d17-df90f5a88454","quoteStatus":"Quoted","message":"Quote# Q-980363 status changed to Quoted.","modifiedAt":"2025-08-04T11:16:22.803Z"},"publishedAt":"2025-08-04T11:16:23.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:37:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:37:40
[webhook] [2025-08-04 11:37:40] [adwsapi_v2.php:36]  Provided signature: sha256=3aa09ac883229f05459c6b60ac1e7f93a31f880c94aecff92c2f1a40f9896159
[webhook] [2025-08-04 11:37:40] [adwsapi_v2.php:37]  Calculated signature: sha256=9467bf5d60022f69f328642b0c95ddc82c25180024ef7fcf9b013f8389705dc1
[webhook] [2025-08-04 11:37:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 51b7d06ba1fef062\n    [X-B3-Traceid] => 68909b822e9d6dbe0167cdd711d206ac\n    [B3] => 68909b822e9d6dbe0167cdd711d206ac-51b7d06ba1fef062-1\n    [Traceparent] => 00-68909b822e9d6dbe0167cdd711d206ac-51b7d06ba1fef062-01\n    [X-Amzn-Trace-Id] => Root=1-68909b82-2e9d6dbe0167cdd711d206ac;Parent=51b7d06ba1fef062;Sampled=1\n    [X-Adsk-Signature] => sha256=3aa09ac883229f05459c6b60ac1e7f93a31f880c94aecff92c2f1a40f9896159\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5f6787f5-f539-497c-8507-6e4342f897f5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:37:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"5f6787f5-f539-497c-8507-6e4342f897f5","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56743625158902","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T11:10:55.000+0000"},"publishedAt":"2025-08-04T11:37:38.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:44:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:44:39
[webhook] [2025-08-04 11:44:39] [adwsapi_v2.php:36]  Provided signature: sha256=d71f2c77cd38ecd69b1b6b8364ff878b36bf3812254950e29a6f3be84da94d40
[webhook] [2025-08-04 11:44:39] [adwsapi_v2.php:37]  Calculated signature: sha256=a05a66479a8868246348409af277cf1ea48eba55a070567564b114e01c7aae9d
[webhook] [2025-08-04 11:44:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 98143b1baf99cf77\n    [X-B3-Traceid] => 68909d2542fa50530f2d130113d5da43\n    [B3] => 68909d2542fa50530f2d130113d5da43-98143b1baf99cf77-1\n    [Traceparent] => 00-68909d2542fa50530f2d130113d5da43-98143b1baf99cf77-01\n    [X-Amzn-Trace-Id] => Root=1-68909d25-42fa50530f2d130113d5da43;Parent=98143b1baf99cf77;Sampled=1\n    [X-Adsk-Signature] => sha256=d71f2c77cd38ecd69b1b6b8364ff878b36bf3812254950e29a6f3be84da94d40\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ec14cf25-a591-4efe-af21-4c71da8e55f4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:44:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"ec14cf25-a591-4efe-af21-4c71da8e55f4","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66126167207160","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T11:13:08.000+0000"},"publishedAt":"2025-08-04T11:44:37.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:48:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:48:59
[webhook] [2025-08-04 11:48:59] [adwsapi_v2.php:36]  Provided signature: sha256=1d52c98498d0d379c160453ec0c94090f596080e879444bc724277cf12668dc9
[webhook] [2025-08-04 11:48:59] [adwsapi_v2.php:37]  Calculated signature: sha256=0c5c812d3661db2444ec6c36e2d9ca471daf253d65f1f132744a869051f4f321
[webhook] [2025-08-04 11:48:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 786992ee4fc6440e\n    [X-B3-Traceid] => 68909e2959e358ad2fe7b5fe3798e7a5\n    [B3] => 68909e2959e358ad2fe7b5fe3798e7a5-786992ee4fc6440e-1\n    [Traceparent] => 00-68909e2959e358ad2fe7b5fe3798e7a5-786992ee4fc6440e-01\n    [X-Amzn-Trace-Id] => Root=1-68909e29-59e358ad2fe7b5fe3798e7a5;Parent=786992ee4fc6440e;Sampled=1\n    [X-Adsk-Signature] => sha256=1d52c98498d0d379c160453ec0c94090f596080e879444bc724277cf12668dc9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d08ee09f-86fc-48b8-8638-cad2a146fd59\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:48:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"d08ee09f-86fc-48b8-8638-cad2a146fd59","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69381769773020","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T11:13:55.000+0000"},"publishedAt":"2025-08-04T11:48:57.000Z","csn":"5103159758"}
[webhook] [2025-08-04 11:53:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 11:53:12
[webhook] [2025-08-04 11:53:12] [adwsapi_v2.php:36]  Provided signature: sha256=4c3cbc3eec5201269d9e7cab8f55f19b8266a1998db2d08253f9bd05922bf3c3
[webhook] [2025-08-04 11:53:12] [adwsapi_v2.php:37]  Calculated signature: sha256=d3cc916a5e9e97497a019885edc3d2720ccca303d4fb98a53cf80e9277fec8c0
[webhook] [2025-08-04 11:53:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 37c8364f85e5431e\n    [X-B3-Traceid] => 68909f2617e4563868ae625f796ad07a\n    [B3] => 68909f2617e4563868ae625f796ad07a-37c8364f85e5431e-1\n    [Traceparent] => 00-68909f2617e4563868ae625f796ad07a-37c8364f85e5431e-01\n    [X-Amzn-Trace-Id] => Root=1-68909f26-17e4563868ae625f796ad07a;Parent=37c8364f85e5431e;Sampled=1\n    [X-Adsk-Signature] => sha256=4c3cbc3eec5201269d9e7cab8f55f19b8266a1998db2d08253f9bd05922bf3c3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df080430-ea86-4ec7-bb7c-d2bdc811d0ae\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 11:53:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"df080430-ea86-4ec7-bb7c-d2bdc811d0ae","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75429970383863","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T11:17:33.000+0000"},"publishedAt":"2025-08-04T11:53:10.000Z","csn":"5103159758"}
[webhook] [2025-08-04 12:07:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 12:07:17
[webhook] [2025-08-04 12:07:17] [adwsapi_v2.php:36]  Provided signature: sha256=9db7b8f81d23e0b46236d3a1ac5ef4a2db14f1f8665a6a9656baaebf565442dd
[webhook] [2025-08-04 12:07:17] [adwsapi_v2.php:37]  Calculated signature: sha256=28656fba46e0717004b35fa2ca1bce8d1ac7e0beb35caec0570b2781dd3261c2
[webhook] [2025-08-04 12:07:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2dc3d459f2cf0f7c\n    [X-B3-Traceid] => 6890a273354ad055739c4c6e4005fe9b\n    [B3] => 6890a273354ad055739c4c6e4005fe9b-2dc3d459f2cf0f7c-1\n    [Traceparent] => 00-6890a273354ad055739c4c6e4005fe9b-2dc3d459f2cf0f7c-01\n    [X-Amzn-Trace-Id] => Root=1-6890a273-354ad055739c4c6e4005fe9b;Parent=2dc3d459f2cf0f7c;Sampled=1\n    [X-Adsk-Signature] => sha256=9db7b8f81d23e0b46236d3a1ac5ef4a2db14f1f8665a6a9656baaebf565442dd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754309235500-56440318707446\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 12:07:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754309235500-56440318707446","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56440318707446","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T12:07:15.500Z"},"publishedAt":"2025-08-04T12:07:15.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:04:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:04:06
[webhook] [2025-08-04 16:04:06] [adwsapi_v2.php:36]  Provided signature: sha256=7aeafa4938a5afd98c568d5c2da5933ef852c0c93c5f7204479ffc602d201766
[webhook] [2025-08-04 16:04:06] [adwsapi_v2.php:37]  Calculated signature: sha256=21da6941e382bc06b9a683b7428c23e3c12150dcae09064675371e1c0f333499
[webhook] [2025-08-04 16:04:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4bc1035a920269ae\n    [X-B3-Traceid] => 6890d9f4472a03b339c1848f3a6ab561\n    [B3] => 6890d9f4472a03b339c1848f3a6ab561-4bc1035a920269ae-1\n    [Traceparent] => 00-6890d9f4472a03b339c1848f3a6ab561-4bc1035a920269ae-01\n    [X-Amzn-Trace-Id] => Root=1-6890d9f4-472a03b339c1848f3a6ab561;Parent=4bc1035a920269ae;Sampled=1\n    [X-Adsk-Signature] => sha256=7aeafa4938a5afd98c568d5c2da5933ef852c0c93c5f7204479ffc602d201766\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754323444138-69381769773020\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:04:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754323444138-69381769773020","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69381769773020","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T16:04:04.138Z"},"publishedAt":"2025-08-04T16:04:04.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:10:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:10:16
[webhook] [2025-08-04 16:10:16] [adwsapi_v2.php:36]  Provided signature: sha256=eab4deadaea3bcbf59303f0b59b79c9fec29d48640600f523dda404a4530cd60
[webhook] [2025-08-04 16:10:16] [adwsapi_v2.php:37]  Calculated signature: sha256=71012d28d9f9982a32dde6a1d1b91455241d290e2229e9ef07ddb230c0fc81e0
[webhook] [2025-08-04 16:10:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 34f121dc926725a7\n    [X-B3-Traceid] => 6890db6648360d040adaa7526e4265ec\n    [B3] => 6890db6648360d040adaa7526e4265ec-34f121dc926725a7-1\n    [Traceparent] => 00-6890db6648360d040adaa7526e4265ec-34f121dc926725a7-01\n    [X-Amzn-Trace-Id] => Root=1-6890db66-48360d040adaa7526e4265ec;Parent=34f121dc926725a7;Sampled=1\n    [X-Adsk-Signature] => sha256=eab4deadaea3bcbf59303f0b59b79c9fec29d48640600f523dda404a4530cd60\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754323814143-56743625158902\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:10:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754323814143-56743625158902","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56743625158902","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T16:10:14.143Z"},"publishedAt":"2025-08-04T16:10:14.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:11:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:11:13
[webhook] [2025-08-04 16:11:13] [adwsapi_v2.php:36]  Provided signature: sha256=83f63d5d160faeda954024bbef6b8afd2c132f685a4cc5afd543faff1d5f0cee
[webhook] [2025-08-04 16:11:13] [adwsapi_v2.php:37]  Calculated signature: sha256=e4fe802c4637dc40a5005244f7c8c1ccddb64b829a88d26ccbb97058145cf09f
[webhook] [2025-08-04 16:11:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a109046dab4be27d\n    [X-B3-Traceid] => 6890db9e206dd42e76c58a0c0e95d981\n    [B3] => 6890db9e206dd42e76c58a0c0e95d981-a109046dab4be27d-1\n    [Traceparent] => 00-6890db9e206dd42e76c58a0c0e95d981-a109046dab4be27d-01\n    [X-Amzn-Trace-Id] => Root=1-6890db9e-206dd42e76c58a0c0e95d981;Parent=a109046dab4be27d;Sampled=1\n    [X-Adsk-Signature] => sha256=83f63d5d160faeda954024bbef6b8afd2c132f685a4cc5afd543faff1d5f0cee\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754323870274-75429970383863\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:11:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754323870274-75429970383863","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75429970383863","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T16:11:10.274Z"},"publishedAt":"2025-08-04T16:11:10.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:11:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:11:52
[webhook] [2025-08-04 16:11:52] [adwsapi_v2.php:36]  Provided signature: sha256=932599e198417cd6917e7c76d55caf8996308e7d89a98112cf2dee64b4097761
[webhook] [2025-08-04 16:11:52] [adwsapi_v2.php:37]  Calculated signature: sha256=5f991fbc4e6fd268548e5c99fbb8249a4e8831f711592ba96661f79d1a0b37fb
[webhook] [2025-08-04 16:11:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d7355dd252cd1fdb\n    [X-B3-Traceid] => 6890dbc6259641f95359f33d37bcad8c\n    [B3] => 6890dbc6259641f95359f33d37bcad8c-d7355dd252cd1fdb-1\n    [Traceparent] => 00-6890dbc6259641f95359f33d37bcad8c-d7355dd252cd1fdb-01\n    [X-Amzn-Trace-Id] => Root=1-6890dbc6-259641f95359f33d37bcad8c;Parent=d7355dd252cd1fdb;Sampled=1\n    [X-Adsk-Signature] => sha256=932599e198417cd6917e7c76d55caf8996308e7d89a98112cf2dee64b4097761\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => eca43743-e2fe-4b37-99fd-c476bffad8ed\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:11:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"eca43743-e2fe-4b37-99fd-c476bffad8ed","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"70169625842721","quantity":1,"autoRenew":"ON","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-04T15:56:47.000+0000"},"publishedAt":"2025-08-04T16:11:50.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:15:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:15:36
[webhook] [2025-08-04 16:15:36] [adwsapi_v2.php:36]  Provided signature: sha256=c63a747599cda95a9f33df8e5723204f8a7fe94d8a4042857236f6db36ee49c5
[webhook] [2025-08-04 16:15:36] [adwsapi_v2.php:37]  Calculated signature: sha256=4d3315d0ffde8fbee9c3e22fef0735fe2e3ad1c539e696482c5304b3ea0e50f0
[webhook] [2025-08-04 16:15:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6ac4b78900a3b8b0\n    [X-B3-Traceid] => 6890dca60943445f0fc393a30adb6c2d\n    [B3] => 6890dca60943445f0fc393a30adb6c2d-6ac4b78900a3b8b0-1\n    [Traceparent] => 00-6890dca60943445f0fc393a30adb6c2d-6ac4b78900a3b8b0-01\n    [X-Amzn-Trace-Id] => Root=1-6890dca6-0943445f0fc393a30adb6c2d;Parent=6ac4b78900a3b8b0;Sampled=1\n    [X-Adsk-Signature] => sha256=c63a747599cda95a9f33df8e5723204f8a7fe94d8a4042857236f6db36ee49c5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754324134487-66126167207160\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:15:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754324134487-66126167207160","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66126167207160","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T16:15:34.487Z"},"publishedAt":"2025-08-04T16:15:34.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:37:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:37:36
[webhook] [2025-08-04 16:37:36] [adwsapi_v2.php:36]  Provided signature: sha256=4709c3d1682a341fc7a1df8a2074af794be38422e214c5aafd50ab9f8b5ae966
[webhook] [2025-08-04 16:37:36] [adwsapi_v2.php:37]  Calculated signature: sha256=18b316feba23c4b021cfbc6f60dc128e46d3272ad72756364c2fd7dfd3454488
[webhook] [2025-08-04 16:37:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1387e302f5aaab5e\n    [X-B3-Traceid] => 6890e1cd7cd348f82941314c0ebede75\n    [B3] => 6890e1cd7cd348f82941314c0ebede75-1387e302f5aaab5e-1\n    [Traceparent] => 00-6890e1cd7cd348f82941314c0ebede75-1387e302f5aaab5e-01\n    [X-Amzn-Trace-Id] => Root=1-6890e1cd-7cd348f82941314c0ebede75;Parent=1387e302f5aaab5e;Sampled=1\n    [X-Adsk-Signature] => sha256=4709c3d1682a341fc7a1df8a2074af794be38422e214c5aafd50ab9f8b5ae966\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 16b6e9db-6427-4f8a-b07b-56808b5d362c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:37:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"16b6e9db-6427-4f8a-b07b-56808b5d362c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65969915309711","status":"Active","quantity":1,"endDate":"2026-08-04","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T16:07:32.000+0000"},"publishedAt":"2025-08-04T16:37:33.000Z","csn":"5103159758"}
[webhook] [2025-08-04 16:41:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 16:41:18
[webhook] [2025-08-04 16:41:18] [adwsapi_v2.php:36]  Provided signature: sha256=a09e43f84b1a223bb39b04be10c0c1db4c387f6d621c39ebaeee35ee2f59430d
[webhook] [2025-08-04 16:41:18] [adwsapi_v2.php:37]  Calculated signature: sha256=fc6d8fe70b8472c3d23c561a1171fd9f1b84e8410650dc2efb1e2ee3485d93ac
[webhook] [2025-08-04 16:41:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8d2bb4924cde8f73\n    [X-B3-Traceid] => 6890e2ab46288c91052658b1456266cf\n    [B3] => 6890e2ab46288c91052658b1456266cf-8d2bb4924cde8f73-1\n    [Traceparent] => 00-6890e2ab46288c91052658b1456266cf-8d2bb4924cde8f73-01\n    [X-Amzn-Trace-Id] => Root=1-6890e2ab-46288c91052658b1456266cf;Parent=8d2bb4924cde8f73;Sampled=1\n    [X-Adsk-Signature] => sha256=a09e43f84b1a223bb39b04be10c0c1db4c387f6d621c39ebaeee35ee2f59430d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d065fc45-4449-4322-a072-6cc85262f7bd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 16:41:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"d065fc45-4449-4322-a072-6cc85262f7bd","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56987383140626","status":"Active","quantity":1,"endDate":"2026-07-09","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T16:11:13.000+0000"},"publishedAt":"2025-08-04T16:41:15.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:27:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:27:09
[webhook] [2025-08-04 19:27:09] [adwsapi_v2.php:36]  Provided signature: sha256=c7d384a37d406d7e25dbc5253ce6fda657db9d7a5cfd8acd8bf38d0711f2b19c
[webhook] [2025-08-04 19:27:09] [adwsapi_v2.php:37]  Calculated signature: sha256=180f60607d447703ead56afc11965bae2a30f827d4636fa1fe2bf656718dd37c
[webhook] [2025-08-04 19:27:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 86a895db238be17d\n    [X-B3-Traceid] => 6891098bd1288e8118887374d89bce34\n    [B3] => 6891098bd1288e8118887374d89bce34-86a895db238be17d-1\n    [Traceparent] => 00-6891098bd1288e8118887374d89bce34-86a895db238be17d-01\n    [X-Amzn-Trace-Id] => Root=1-6891098b-d1288e8118887374d89bce34;Parent=86a895db238be17d;Sampled=1\n    [X-Adsk-Signature] => sha256=c7d384a37d406d7e25dbc5253ce6fda657db9d7a5cfd8acd8bf38d0711f2b19c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 869a7f00-deaa-4b71-855f-ccc1cfcf1ea7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:27:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"869a7f00-deaa-4b71-855f-ccc1cfcf1ea7","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-962901","transactionId":"edc2c304-d529-52dc-8805-a205a621babf","quoteStatus":"Order Submitted","message":"Quote# Q-962901 status changed to Order Submitted.","modifiedAt":"2025-08-04T19:27:07.076Z"},"publishedAt":"2025-08-04T19:27:07.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:27:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:27:12
[webhook] [2025-08-04 19:27:12] [adwsapi_v2.php:36]  Provided signature: sha256=418b33e4e66e660d1b02abb3ff4661bcae46bef28d9e2d725e5c3ab3e32af945
[webhook] [2025-08-04 19:27:12] [adwsapi_v2.php:37]  Calculated signature: sha256=61fd1e8d038b50598f09494c028776b98aafcdac87e150143c364ab8e525aebd
[webhook] [2025-08-04 19:27:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d69b486bd784ca00\n    [X-B3-Traceid] => 6891098d759920dab1218e8c0e35ee12\n    [B3] => 6891098d759920dab1218e8c0e35ee12-d69b486bd784ca00-1\n    [Traceparent] => 00-6891098d759920dab1218e8c0e35ee12-d69b486bd784ca00-01\n    [X-Amzn-Trace-Id] => Root=1-6891098d-759920dab1218e8c0e35ee12;Parent=d69b486bd784ca00;Sampled=1\n    [X-Adsk-Signature] => sha256=418b33e4e66e660d1b02abb3ff4661bcae46bef28d9e2d725e5c3ab3e32af945\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ba7650c0-75c3-4663-b3b5-5b7248e0ecf4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:27:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"ba7650c0-75c3-4663-b3b5-5b7248e0ecf4","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-962901","transactionId":"edc2c304-d529-52dc-8805-a205a621babf","quoteStatus":"Ordered","message":"Quote# Q-962901 status changed to Ordered.","modifiedAt":"2025-08-04T19:27:09.765Z"},"publishedAt":"2025-08-04T19:27:10.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:37:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:37:38
[webhook] [2025-08-04 19:37:38] [adwsapi_v2.php:36]  Provided signature: sha256=cff054b5b878351c1966c889589628c961fd256ad2fa32a0a36161c182588061
[webhook] [2025-08-04 19:37:38] [adwsapi_v2.php:37]  Calculated signature: sha256=587c5310c570a7f0becb981bf9d1a6a0fd8a7487a4b8ec9a9aa8d802c1059dec
[webhook] [2025-08-04 19:37:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fc7337cf50ff2b14\n    [X-B3-Traceid] => 68910bff7e53c19365e64eed0929e979\n    [B3] => 68910bff7e53c19365e64eed0929e979-fc7337cf50ff2b14-1\n    [Traceparent] => 00-68910bff7e53c19365e64eed0929e979-fc7337cf50ff2b14-01\n    [X-Amzn-Trace-Id] => Root=1-68910bff-7e53c19365e64eed0929e979;Parent=fc7337cf50ff2b14;Sampled=1\n    [X-Adsk-Signature] => sha256=cff054b5b878351c1966c889589628c961fd256ad2fa32a0a36161c182588061\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5580ab76-777c-4e4b-9892-a767f790e529\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:37:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"5580ab76-777c-4e4b-9892-a767f790e529","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62851504013355","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-04T19:12:33.000+0000"},"publishedAt":"2025-08-04T19:37:35.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:37:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:37:39
[webhook] [2025-08-04 19:37:39] [adwsapi_v2.php:36]  Provided signature: sha256=6e49011e7a983048fad5f19f095a11d8aa1dad330284c6ec66cf4816dcc821d0
[webhook] [2025-08-04 19:37:39] [adwsapi_v2.php:37]  Calculated signature: sha256=17c8380d65efce5cdff9d9b9d9e8e3e01fb93f7c989b958febf6c695d5ed7981
[webhook] [2025-08-04 19:37:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a6a3ca88618911d8\n    [X-B3-Traceid] => 68910c002eb664e42ab233aa0181a6e6\n    [B3] => 68910c002eb664e42ab233aa0181a6e6-a6a3ca88618911d8-1\n    [Traceparent] => 00-68910c002eb664e42ab233aa0181a6e6-a6a3ca88618911d8-01\n    [X-Amzn-Trace-Id] => Root=1-68910c00-2eb664e42ab233aa0181a6e6;Parent=a6a3ca88618911d8;Sampled=1\n    [X-Adsk-Signature] => sha256=6e49011e7a983048fad5f19f095a11d8aa1dad330284c6ec66cf4816dcc821d0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b15a52df-c258-4d2b-8fd9-d8dc73a48917\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:37:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"b15a52df-c258-4d2b-8fd9-d8dc73a48917","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62851791696292","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-04T19:12:34.000+0000"},"publishedAt":"2025-08-04T19:37:36.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:40:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:40:21
[webhook] [2025-08-04 19:40:21] [adwsapi_v2.php:36]  Provided signature: sha256=b80666e74eabc1adf18b1604504cbcbbbbf0c3441a13e5aca62d5ce03537c206
[webhook] [2025-08-04 19:40:21] [adwsapi_v2.php:37]  Calculated signature: sha256=1b7a4f83b35f1c8ce94e2d81d3afd372a5b4710473bd1cc560fc85660e1ffe07
[webhook] [2025-08-04 19:40:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3c6e37da31d46615\n    [X-B3-Traceid] => 68910ca34b8d95ee6ccf6bd2523f348c\n    [B3] => 68910ca34b8d95ee6ccf6bd2523f348c-3c6e37da31d46615-1\n    [Traceparent] => 00-68910ca34b8d95ee6ccf6bd2523f348c-3c6e37da31d46615-01\n    [X-Amzn-Trace-Id] => Root=1-68910ca3-4b8d95ee6ccf6bd2523f348c;Parent=3c6e37da31d46615;Sampled=1\n    [X-Adsk-Signature] => sha256=b80666e74eabc1adf18b1604504cbcbbbbf0c3441a13e5aca62d5ce03537c206\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6f2c4316-fe5d-4a16-8a7a-4bab273af78d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:40:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"6f2c4316-fe5d-4a16-8a7a-4bab273af78d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56987383140626","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T19:10:09.000+0000"},"publishedAt":"2025-08-04T19:40:19.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:41:23] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:41:23
[webhook] [2025-08-04 19:41:23] [adwsapi_v2.php:36]  Provided signature: sha256=2c9c77dce9feebb51d78ee61cfd4aebe2b90639656656ba8f35e3d7c9ede5ec6
[webhook] [2025-08-04 19:41:23] [adwsapi_v2.php:37]  Calculated signature: sha256=bccf9a9f35ce9cc5b2d6b2cc8a984f1f28adc87de6968ef4330244534dbdfdec
[webhook] [2025-08-04 19:41:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8c0452fd30dfc25c\n    [X-B3-Traceid] => 68910ce0477b21ea2a857edd3d0c2f36\n    [B3] => 68910ce0477b21ea2a857edd3d0c2f36-8c0452fd30dfc25c-1\n    [Traceparent] => 00-68910ce0477b21ea2a857edd3d0c2f36-8c0452fd30dfc25c-01\n    [X-Amzn-Trace-Id] => Root=1-68910ce0-477b21ea2a857edd3d0c2f36;Parent=8c0452fd30dfc25c;Sampled=1\n    [X-Adsk-Signature] => sha256=2c9c77dce9feebb51d78ee61cfd4aebe2b90639656656ba8f35e3d7c9ede5ec6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 59cb8147-3a5b-4ab5-b058-20030752973c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:41:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"59cb8147-3a5b-4ab5-b058-20030752973c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65969915309711","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T19:11:14.000+0000"},"publishedAt":"2025-08-04T19:41:20.000Z","csn":"5103159758"}
[webhook] [2025-08-04 19:42:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 19:42:21
[webhook] [2025-08-04 19:42:21] [adwsapi_v2.php:36]  Provided signature: sha256=325e01bbbbfc87c6ac2f8346ca8b72d130cce414a53f2c959b4fe70504ded5fb
[webhook] [2025-08-04 19:42:21] [adwsapi_v2.php:37]  Calculated signature: sha256=c168838a6dbd6b92b931275747b02f55131341c7d93aba2ee977753d6ad9719a
[webhook] [2025-08-04 19:42:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a4ee3422276ed8ba\n    [X-B3-Traceid] => 68910d1b4b3cf14f767fc40e0d43e5bc\n    [B3] => 68910d1b4b3cf14f767fc40e0d43e5bc-a4ee3422276ed8ba-1\n    [Traceparent] => 00-68910d1b4b3cf14f767fc40e0d43e5bc-a4ee3422276ed8ba-01\n    [X-Amzn-Trace-Id] => Root=1-68910d1b-4b3cf14f767fc40e0d43e5bc;Parent=a4ee3422276ed8ba;Sampled=1\n    [X-Adsk-Signature] => sha256=325e01bbbbfc87c6ac2f8346ca8b72d130cce414a53f2c959b4fe70504ded5fb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c8e5bbe0-75df-4d1b-8f27-aed238e87314\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 19:42:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"c8e5bbe0-75df-4d1b-8f27-aed238e87314","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63091949302189","status":"Active","quantity":1,"endDate":"2026-09-05","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-04T19:27:16.000+0000"},"publishedAt":"2025-08-04T19:42:19.000Z","csn":"5103159758"}
[webhook] [2025-08-04 20:09:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 20:09:52
[webhook] [2025-08-04 20:09:52] [adwsapi_v2.php:36]  Provided signature: sha256=0fdc5bf696510fe79257b1bbb4e65e7f2e0b01c02fba98d43f935561dd48e2c6
[webhook] [2025-08-04 20:09:52] [adwsapi_v2.php:37]  Calculated signature: sha256=4976d0e397f42fc60f2d66f3b69b4f9c283ee2ba149352e3b6fdf212bf4808a2
[webhook] [2025-08-04 20:09:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f48607a3ff4f2355\n    [X-B3-Traceid] => 6891138d6ab40c8718d2e44e1a0adf71\n    [B3] => 6891138d6ab40c8718d2e44e1a0adf71-f48607a3ff4f2355-1\n    [Traceparent] => 00-6891138d6ab40c8718d2e44e1a0adf71-f48607a3ff4f2355-01\n    [X-Amzn-Trace-Id] => Root=1-6891138d-6ab40c8718d2e44e1a0adf71;Parent=f48607a3ff4f2355;Sampled=1\n    [X-Adsk-Signature] => sha256=0fdc5bf696510fe79257b1bbb4e65e7f2e0b01c02fba98d43f935561dd48e2c6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754338189897-69381769773020\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 20:09:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754338189897-69381769773020","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69381769773020","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T20:09:49.897Z"},"publishedAt":"2025-08-04T20:09:49.000Z","csn":"5103159758"}
[webhook] [2025-08-04 20:13:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 20:13:36
[webhook] [2025-08-04 20:13:36] [adwsapi_v2.php:36]  Provided signature: sha256=85d5391b67eb7bde950808f12b5223132afd9508e74668656519d186315d86e2
[webhook] [2025-08-04 20:13:36] [adwsapi_v2.php:37]  Calculated signature: sha256=01bbb4e031524924affb8a94a15b27ec1189a2b939a9cbf3196af8182a9d6aa7
[webhook] [2025-08-04 20:13:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e6b0e813c1396cc3\n    [X-B3-Traceid] => 6891146e3688338b0ec4ed8464488487\n    [B3] => 6891146e3688338b0ec4ed8464488487-e6b0e813c1396cc3-1\n    [Traceparent] => 00-6891146e3688338b0ec4ed8464488487-e6b0e813c1396cc3-01\n    [X-Amzn-Trace-Id] => Root=1-6891146e-3688338b0ec4ed8464488487;Parent=e6b0e813c1396cc3;Sampled=1\n    [X-Adsk-Signature] => sha256=85d5391b67eb7bde950808f12b5223132afd9508e74668656519d186315d86e2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754338414293-66126167207160\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 20:13:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754338414293-66126167207160","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66126167207160","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T20:13:34.293Z"},"publishedAt":"2025-08-04T20:13:34.000Z","csn":"5103159758"}
[webhook] [2025-08-04 20:14:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 20:14:59
[webhook] [2025-08-04 20:14:59] [adwsapi_v2.php:36]  Provided signature: sha256=fbc409efb8dddd8a7ce3ab7a391ed572510868c11d2c845086446f09e92660ca
[webhook] [2025-08-04 20:14:59] [adwsapi_v2.php:37]  Calculated signature: sha256=1f5ef6377ab2641903b774458e970958f138613dfec0a432721d871e0a41bf87
[webhook] [2025-08-04 20:14:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 64e8def104d0cafd\n    [X-B3-Traceid] => 689114c1123f924471484aa473c608c9\n    [B3] => 689114c1123f924471484aa473c608c9-64e8def104d0cafd-1\n    [Traceparent] => 00-689114c1123f924471484aa473c608c9-64e8def104d0cafd-01\n    [X-Amzn-Trace-Id] => Root=1-689114c1-123f924471484aa473c608c9;Parent=64e8def104d0cafd;Sampled=1\n    [X-Adsk-Signature] => sha256=fbc409efb8dddd8a7ce3ab7a391ed572510868c11d2c845086446f09e92660ca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754338497644-75429970383863\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 20:14:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754338497644-75429970383863","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75429970383863","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T20:14:57.644Z"},"publishedAt":"2025-08-04T20:14:57.000Z","csn":"5103159758"}
[webhook] [2025-08-04 20:18:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 20:18:27
[webhook] [2025-08-04 20:18:27] [adwsapi_v2.php:36]  Provided signature: sha256=0b1c45a2c5cce0fd9977b5f751dc4aacdf54951fb83567baed010b63aa5bbe97
[webhook] [2025-08-04 20:18:27] [adwsapi_v2.php:37]  Calculated signature: sha256=2c42d4addc6440776f6f47b1cf297cff6f4cf4c5aa2bb1d3156aabf675fc644f
[webhook] [2025-08-04 20:18:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cc0d58b671fc6df1\n    [X-B3-Traceid] => 6891159168264b6d031f4da6243c9253\n    [B3] => 6891159168264b6d031f4da6243c9253-cc0d58b671fc6df1-1\n    [Traceparent] => 00-6891159168264b6d031f4da6243c9253-cc0d58b671fc6df1-01\n    [X-Amzn-Trace-Id] => Root=1-68911591-68264b6d031f4da6243c9253;Parent=cc0d58b671fc6df1;Sampled=1\n    [X-Adsk-Signature] => sha256=0b1c45a2c5cce0fd9977b5f751dc4aacdf54951fb83567baed010b63aa5bbe97\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754338705279-56743625158902\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 20:18:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754338705279-56743625158902","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56743625158902","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-04T20:18:25.279Z"},"publishedAt":"2025-08-04T20:18:25.000Z","csn":"5103159758"}
[webhook] [2025-08-04 23:03:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 23:03:07
[webhook] [2025-08-04 23:03:07] [adwsapi_v2.php:36]  Provided signature: sha256=4e104fbebe2403c6b14a00875657ed19694a73a0b0fc77d8e27912721948a745
[webhook] [2025-08-04 23:03:07] [adwsapi_v2.php:37]  Calculated signature: sha256=bde152f196556ac6f292c8b5768557f2addf0351bc1c2c23f56e3467d46d93a3
[webhook] [2025-08-04 23:03:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c93510f3d0374fee\n    [X-B3-Traceid] => 68913c286abc242a4869293c4717e422\n    [B3] => 68913c286abc242a4869293c4717e422-c93510f3d0374fee-1\n    [Traceparent] => 00-68913c286abc242a4869293c4717e422-c93510f3d0374fee-01\n    [X-Amzn-Trace-Id] => Root=1-68913c28-6abc242a4869293c4717e422;Parent=c93510f3d0374fee;Sampled=1\n    [X-Adsk-Signature] => sha256=4e104fbebe2403c6b14a00875657ed19694a73a0b0fc77d8e27912721948a745\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754348584840-569-57473650\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 23:03:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754348584840-569-57473650","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"569-57473650","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-04T23:03:04.840Z"},"publishedAt":"2025-08-04T23:03:04.000Z","csn":"5103159758"}
[webhook] [2025-08-04 23:03:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 23:03:17
[webhook] [2025-08-04 23:03:17] [adwsapi_v2.php:36]  Provided signature: sha256=26e4c9ed8a9b3628a7a3e9feffa43a383a2f05a0d718fd25b983c90fe6408e6b
[webhook] [2025-08-04 23:03:17] [adwsapi_v2.php:37]  Calculated signature: sha256=c38669e7a1162b0f0cff573cd12ebbe82dede4957680ce582e6169917842f5a5
[webhook] [2025-08-04 23:03:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e948594ecfb1cd6d\n    [X-B3-Traceid] => 68913c331760d6554930469c3c389a26\n    [B3] => 68913c331760d6554930469c3c389a26-e948594ecfb1cd6d-1\n    [Traceparent] => 00-68913c331760d6554930469c3c389a26-e948594ecfb1cd6d-01\n    [X-Amzn-Trace-Id] => Root=1-68913c33-1760d6554930469c3c389a26;Parent=e948594ecfb1cd6d;Sampled=1\n    [X-Adsk-Signature] => sha256=26e4c9ed8a9b3628a7a3e9feffa43a383a2f05a0d718fd25b983c90fe6408e6b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754348595431-573-17118867\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 23:03:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754348595431-573-17118867","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"573-17118867","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-04T23:03:15.431Z"},"publishedAt":"2025-08-04T23:03:15.000Z","csn":"5103159758"}
[webhook] [2025-08-04 23:40:16] [adwsapi_v2.php:20]  Webhook request received at 2025-08-04 23:40:16
[webhook] [2025-08-04 23:40:16] [adwsapi_v2.php:36]  Provided signature: sha256=5740303f6b33970abf1eb9d23820d02857c9bc497888cba0c2ef1f95a2898e89
[webhook] [2025-08-04 23:40:16] [adwsapi_v2.php:37]  Calculated signature: sha256=bf03ab60c5fa35518bb67ff746b58646a38fca233a068ba6d41c69b9b747c604
[webhook] [2025-08-04 23:40:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 95aefd07292a4c6d\n    [X-B3-Traceid] => 689144de5b5f85215b227f2719220991\n    [B3] => 689144de5b5f85215b227f2719220991-95aefd07292a4c6d-1\n    [Traceparent] => 00-689144de5b5f85215b227f2719220991-95aefd07292a4c6d-01\n    [X-Amzn-Trace-Id] => Root=1-689144de-5b5f85215b227f2719220991;Parent=95aefd07292a4c6d;Sampled=1\n    [X-Adsk-Signature] => sha256=5740303f6b33970abf1eb9d23820d02857c9bc497888cba0c2ef1f95a2898e89\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => eeea9e82-eb2d-4686-9a3c-c98b5fd5ddf1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-04 23:40:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"eeea9e82-eb2d-4686-9a3c-c98b5fd5ddf1","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63091949302189","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-04T23:05:09.000+0000"},"publishedAt":"2025-08-04T23:40:14.000Z","csn":"5103159758"}
[webhook] [2025-08-05 00:02:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 00:02:45
[webhook] [2025-08-05 00:02:45] [adwsapi_v2.php:36]  Provided signature: sha256=226bc27fda552adde1903711b8de6c57f4b6db3da49233a46bbb45372aa050e7
[webhook] [2025-08-05 00:02:45] [adwsapi_v2.php:37]  Calculated signature: sha256=8658a2d39d505f98c5c48e9279f3dbc4859dc96f1aa07d28026c3637fd650d12
[webhook] [2025-08-05 00:02:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 73f3633c9c88ad80\n    [X-B3-Traceid] => 68914a237e5fe7b51b5af4f7496018a9\n    [B3] => 68914a237e5fe7b51b5af4f7496018a9-73f3633c9c88ad80-1\n    [Traceparent] => 00-68914a237e5fe7b51b5af4f7496018a9-73f3633c9c88ad80-01\n    [X-Amzn-Trace-Id] => Root=1-68914a23-7e5fe7b51b5af4f7496018a9;Parent=73f3633c9c88ad80;Sampled=1\n    [X-Adsk-Signature] => sha256=226bc27fda552adde1903711b8de6c57f4b6db3da49233a46bbb45372aa050e7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754352163099-56987383140626\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 00:02:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754352163099-56987383140626","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56987383140626","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T00:02:43.099Z"},"publishedAt":"2025-08-05T00:02:43.000Z","csn":"5103159758"}
[webhook] [2025-08-05 00:03:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 00:03:24
[webhook] [2025-08-05 00:03:24] [adwsapi_v2.php:36]  Provided signature: sha256=c0f2d4d2467c8c1622fb3b3bbcad5d30cf7f2155c3b6b1fbf39e4028b308ba95
[webhook] [2025-08-05 00:03:24] [adwsapi_v2.php:37]  Calculated signature: sha256=49abe6299103e16a76ecf1dce01dc30d1f074516f35b1084181d49e97c4c6196
[webhook] [2025-08-05 00:03:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f66c148773f5028e\n    [X-B3-Traceid] => 68914a4a287d876e1be10f66504b1277\n    [B3] => 68914a4a287d876e1be10f66504b1277-f66c148773f5028e-1\n    [Traceparent] => 00-68914a4a287d876e1be10f66504b1277-f66c148773f5028e-01\n    [X-Amzn-Trace-Id] => Root=1-68914a4a-287d876e1be10f66504b1277;Parent=f66c148773f5028e;Sampled=1\n    [X-Adsk-Signature] => sha256=c0f2d4d2467c8c1622fb3b3bbcad5d30cf7f2155c3b6b1fbf39e4028b308ba95\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754352202561-65969915309711\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 00:03:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754352202561-65969915309711","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65969915309711","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T00:03:22.561Z"},"publishedAt":"2025-08-05T00:03:22.000Z","csn":"5103159758"}
[webhook] [2025-08-05 00:07:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 00:07:01
[webhook] [2025-08-05 00:07:01] [adwsapi_v2.php:36]  Provided signature: sha256=fa6167d10afe02c658592cd2f990fdbeca05786c20d7bacee0370c09f5bd032e
[webhook] [2025-08-05 00:07:01] [adwsapi_v2.php:37]  Calculated signature: sha256=3a295de78bf773ffed1d7f4f08ab20b379c675c12fc30c0cda46cc20f562385d
[webhook] [2025-08-05 00:07:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 44fab2bedce2d2f0\n    [X-B3-Traceid] => 68914b2349b67c44373e37f817aa8074\n    [B3] => 68914b2349b67c44373e37f817aa8074-44fab2bedce2d2f0-1\n    [Traceparent] => 00-68914b2349b67c44373e37f817aa8074-44fab2bedce2d2f0-01\n    [X-Amzn-Trace-Id] => Root=1-68914b23-49b67c44373e37f817aa8074;Parent=44fab2bedce2d2f0;Sampled=1\n    [X-Adsk-Signature] => sha256=fa6167d10afe02c658592cd2f990fdbeca05786c20d7bacee0370c09f5bd032e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754352419582-63091949302189\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 00:07:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754352419582-63091949302189","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63091949302189","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T00:06:59.582Z"},"publishedAt":"2025-08-05T00:06:59.000Z","csn":"5103159758"}
[webhook] [2025-08-05 03:24:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 03:24:22
[webhook] [2025-08-05 03:24:22] [adwsapi_v2.php:36]  Provided signature: sha256=f6534c4ef48d78530556090779d9b244ee61f9e21ee09bc3d6c7b6c2150dd120
[webhook] [2025-08-05 03:24:22] [adwsapi_v2.php:37]  Calculated signature: sha256=a11ca6a128c49203cedb29904b0af77ea384fd6f60e0094285e607552b332940
[webhook] [2025-08-05 03:24:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 052fdd462b62b611\n    [X-B3-Traceid] => 689179636155a61cd6d0e6688082bb59\n    [B3] => 689179636155a61cd6d0e6688082bb59-052fdd462b62b611-1\n    [Traceparent] => 00-689179636155a61cd6d0e6688082bb59-052fdd462b62b611-01\n    [X-Amzn-Trace-Id] => Root=1-68917963-6155a61cd6d0e6688082bb59;Parent=052fdd462b62b611;Sampled=1\n    [X-Adsk-Signature] => sha256=f6534c4ef48d78530556090779d9b244ee61f9e21ee09bc3d6c7b6c2150dd120\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ea2a3d40-a7f1-4274-b364-84055e917e2f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 03:24:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"ea2a3d40-a7f1-4274-b364-84055e917e2f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979616","transactionId":"576afb74-fd9f-556a-ac25-9a7e8d16f607","quoteStatus":"Quoted","message":"Quote# Q-979616 status changed to Quoted.","modifiedAt":"2025-08-05T03:24:18.902Z"},"publishedAt":"2025-08-05T03:24:19.000Z","csn":"5103159758"}
[webhook] [2025-08-05 04:04:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 04:04:49
[webhook] [2025-08-05 04:04:49] [adwsapi_v2.php:36]  Provided signature: sha256=43674762854b4d5292c3ca3dc9d8d65dacc41acdbff8fa2ad6bed9bb7b5c15fe
[webhook] [2025-08-05 04:04:49] [adwsapi_v2.php:37]  Calculated signature: sha256=29ff7a1c7b69091466d717e626866a04ba4c0e8a16b622bae7a2ee387b881abb
[webhook] [2025-08-05 04:04:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b0df4247d2303b1b\n    [X-B3-Traceid] => 689182df149d0a8c34c927fd62ba9f3f\n    [B3] => 689182df149d0a8c34c927fd62ba9f3f-b0df4247d2303b1b-1\n    [Traceparent] => 00-689182df149d0a8c34c927fd62ba9f3f-b0df4247d2303b1b-01\n    [X-Amzn-Trace-Id] => Root=1-689182df-149d0a8c34c927fd62ba9f3f;Parent=b0df4247d2303b1b;Sampled=1\n    [X-Adsk-Signature] => sha256=43674762854b4d5292c3ca3dc9d8d65dacc41acdbff8fa2ad6bed9bb7b5c15fe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754366687147-63091949302189\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 04:04:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754366687147-63091949302189","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"63091949302189","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T04:04:47.147Z"},"publishedAt":"2025-08-05T04:04:47.000Z","csn":"5103159758"}
[webhook] [2025-08-05 07:00:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 07:00:59
[webhook] [2025-08-05 07:00:59] [adwsapi_v2.php:36]  Provided signature: sha256=c6932bc0f6951a7db822bb422dd00f2912cdd37f3921b06cd8989ed0263b0320
[webhook] [2025-08-05 07:00:59] [adwsapi_v2.php:37]  Calculated signature: sha256=335ad9ea1fab3fea827112da8b7b659fb4f4a462d829ca1c393953e52a8d1838
[webhook] [2025-08-05 07:00:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4961d331e2189987\n    [X-B3-Traceid] => 6891ac287decd7505eae9d22124c29f9\n    [B3] => 6891ac287decd7505eae9d22124c29f9-4961d331e2189987-1\n    [Traceparent] => 00-6891ac287decd7505eae9d22124c29f9-4961d331e2189987-01\n    [X-Amzn-Trace-Id] => Root=1-6891ac28-7decd7505eae9d22124c29f9;Parent=4961d331e2189987;Sampled=1\n    [X-Adsk-Signature] => sha256=c6932bc0f6951a7db822bb422dd00f2912cdd37f3921b06cd8989ed0263b0320\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 51a7290f-a29f-414e-8f55-cd4153254101\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 07:00:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"51a7290f-a29f-414e-8f55-cd4153254101","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-663559","transactionId":"4edf6bb7-8cdd-559f-abf7-0bda231d2510","quoteStatus":"Expired","message":"Quote# Q-663559 status changed to Expired.","modifiedAt":"2025-08-05T07:00:53.449Z"},"publishedAt":"2025-08-05T07:00:57.000Z","csn":"5103159758"}
[webhook] [2025-08-05 07:09:24] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 07:09:24
[webhook] [2025-08-05 07:09:24] [adwsapi_v2.php:36]  Provided signature: sha256=a5c70fdde14274c1880c340b36637729044ff214f38f73735de5a17257b3dd28
[webhook] [2025-08-05 07:09:24] [adwsapi_v2.php:37]  Calculated signature: sha256=c0b27bbfc9744e1cef4432e1baab34ba6db3aad9153352d5fce21a88d0cebd3f
[webhook] [2025-08-05 07:09:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f6d3b5a9ea02ac2e\n    [X-B3-Traceid] => 6891ae22400e41a71105a20622e842d5\n    [B3] => 6891ae22400e41a71105a20622e842d5-f6d3b5a9ea02ac2e-1\n    [Traceparent] => 00-6891ae22400e41a71105a20622e842d5-f6d3b5a9ea02ac2e-01\n    [X-Amzn-Trace-Id] => Root=1-6891ae22-400e41a71105a20622e842d5;Parent=f6d3b5a9ea02ac2e;Sampled=1\n    [X-Adsk-Signature] => sha256=a5c70fdde14274c1880c340b36637729044ff214f38f73735de5a17257b3dd28\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bedcf3ce-1944-4920-8d3d-7770eecb3a01\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 07:09:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"bedcf3ce-1944-4920-8d3d-7770eecb3a01","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75337738787712","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T06:44:19.000+0000"},"publishedAt":"2025-08-05T07:09:22.000Z","csn":"5103159758"}
[webhook] [2025-08-05 07:09:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 07:09:30
[webhook] [2025-08-05 07:09:30] [adwsapi_v2.php:36]  Provided signature: sha256=0a280bcc14841ce5134ddbf491dbc04c7a909adbc54e88588bd213e80dfdc9cf
[webhook] [2025-08-05 07:09:30] [adwsapi_v2.php:37]  Calculated signature: sha256=b19b4e9ff1b76f469e0d5e9617911dafb8e218aaebd5946df07e52226162f5e7
[webhook] [2025-08-05 07:09:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ea2c93b05f612057\n    [X-B3-Traceid] => 6891ae271fa6576056caeda735bcd64d\n    [B3] => 6891ae271fa6576056caeda735bcd64d-ea2c93b05f612057-1\n    [Traceparent] => 00-6891ae271fa6576056caeda735bcd64d-ea2c93b05f612057-01\n    [X-Amzn-Trace-Id] => Root=1-6891ae27-1fa6576056caeda735bcd64d;Parent=ea2c93b05f612057;Sampled=1\n    [X-Adsk-Signature] => sha256=0a280bcc14841ce5134ddbf491dbc04c7a909adbc54e88588bd213e80dfdc9cf\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 44ae5ec2-c8a2-411d-9d9d-c6457ceec0f6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 07:09:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"44ae5ec2-c8a2-411d-9d9d-c6457ceec0f6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75146432676914","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-05T06:44:25.000+0000"},"publishedAt":"2025-08-05T07:09:28.000Z","csn":"5103159758"}
[webhook] [2025-08-05 07:09:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 07:09:42
[webhook] [2025-08-05 07:09:42] [adwsapi_v2.php:36]  Provided signature: sha256=7babed3a81c1d527c4cec10c54b65b42a4a86156fff832f933efdb401b06aa2b
[webhook] [2025-08-05 07:09:42] [adwsapi_v2.php:37]  Calculated signature: sha256=1f59fe17b17762c7551033413be9f8a8a3812cfd5df20bd1e10751a3792cb7b2
[webhook] [2025-08-05 07:09:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bf64e683043e8164\n    [X-B3-Traceid] => 6891ae34408573721580f3392332ae16\n    [B3] => 6891ae34408573721580f3392332ae16-bf64e683043e8164-1\n    [Traceparent] => 00-6891ae34408573721580f3392332ae16-bf64e683043e8164-01\n    [X-Amzn-Trace-Id] => Root=1-6891ae34-408573721580f3392332ae16;Parent=bf64e683043e8164;Sampled=1\n    [X-Adsk-Signature] => sha256=7babed3a81c1d527c4cec10c54b65b42a4a86156fff832f933efdb401b06aa2b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 94393bb5-0e0e-4bfd-92a2-f09ea91a61c4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 07:09:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"94393bb5-0e0e-4bfd-92a2-f09ea91a61c4","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75136903143720","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-05T06:54:38.000+0000"},"publishedAt":"2025-08-05T07:09:40.000Z","csn":"5103159758"}
[webhook] [2025-08-05 07:37:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 07:37:35
[webhook] [2025-08-05 07:37:35] [adwsapi_v2.php:36]  Provided signature: sha256=3a66409fe611cd533b09c98a97ba44e601c17faf7dc63977be07a1b19b184b09
[webhook] [2025-08-05 07:37:35] [adwsapi_v2.php:37]  Calculated signature: sha256=ee9046c38d3ca82ffc4b40c512aefe0c2c6e18ec6a21879d65cf7f5961e378ab
[webhook] [2025-08-05 07:37:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 831d96ce7db7d62a\n    [X-B3-Traceid] => 6891b4bc66f52a201356463e72dce54d\n    [B3] => 6891b4bc66f52a201356463e72dce54d-831d96ce7db7d62a-1\n    [Traceparent] => 00-6891b4bc66f52a201356463e72dce54d-831d96ce7db7d62a-01\n    [X-Amzn-Trace-Id] => Root=1-6891b4bc-66f52a201356463e72dce54d;Parent=831d96ce7db7d62a;Sampled=1\n    [X-Adsk-Signature] => sha256=3a66409fe611cd533b09c98a97ba44e601c17faf7dc63977be07a1b19b184b09\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5cc5ad86-b425-474f-b54b-fd4ed816854d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 07:37:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"5cc5ad86-b425-474f-b54b-fd4ed816854d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75205764633108","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T07:02:29.000+0000"},"publishedAt":"2025-08-05T07:37:32.000Z","csn":"5103159758"}
[webhook] [2025-08-05 08:00:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 08:00:48
[webhook] [2025-08-05 08:00:48] [adwsapi_v2.php:36]  Provided signature: sha256=98f926612ff483e8a770b34b738604206b62ac54e4ba37373ba5167197047a0f
[webhook] [2025-08-05 08:00:48] [adwsapi_v2.php:37]  Calculated signature: sha256=5bf2b07cd824c05e04b0038cbb12ee73a34529069782a3560af14eb5ddb8dcb9
[webhook] [2025-08-05 08:00:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d867bf76aaac22a0\n    [X-B3-Traceid] => 6891ba2ee2faa2f4d1e34038efe59cf9\n    [B3] => 6891ba2ee2faa2f4d1e34038efe59cf9-d867bf76aaac22a0-1\n    [Traceparent] => 00-6891ba2ee2faa2f4d1e34038efe59cf9-d867bf76aaac22a0-01\n    [X-Amzn-Trace-Id] => Root=1-6891ba2e-e2faa2f4d1e34038efe59cf9;Parent=d867bf76aaac22a0;Sampled=1\n    [X-Adsk-Signature] => sha256=98f926612ff483e8a770b34b738604206b62ac54e4ba37373ba5167197047a0f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1892b2b6-1b52-4b6d-a818-c644d20240c2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 08:00:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1892b2b6-1b52-4b6d-a818-c644d20240c2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983134","transactionId":"24ae45d0-faee-5810-a445-24db468cce3d","quoteStatus":"Draft","message":"Quote# Q-983134 status changed to Draft.","modifiedAt":"2025-08-05T08:00:46.002Z"},"publishedAt":"2025-08-05T08:00:46.000Z","csn":"5103159758"}
[webhook] [2025-08-05 08:01:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 08:01:20
[webhook] [2025-08-05 08:01:20] [adwsapi_v2.php:36]  Provided signature: sha256=0c86ee8dcc4707a76eff58a471daa9d656e05da90a50030046e8a3ec04e2c7e1
[webhook] [2025-08-05 08:01:20] [adwsapi_v2.php:37]  Calculated signature: sha256=5ad624806620c87b5cd67b76bc1b10f7467e631f702d4974b3168b28402ef59b
[webhook] [2025-08-05 08:01:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6a80d213a5ebd62e\n    [X-B3-Traceid] => 6891ba4e736ff479f5d2fad68eaaccfe\n    [B3] => 6891ba4e736ff479f5d2fad68eaaccfe-6a80d213a5ebd62e-1\n    [Traceparent] => 00-6891ba4e736ff479f5d2fad68eaaccfe-6a80d213a5ebd62e-01\n    [X-Amzn-Trace-Id] => Root=1-6891ba4e-736ff479f5d2fad68eaaccfe;Parent=6a80d213a5ebd62e;Sampled=1\n    [X-Adsk-Signature] => sha256=0c86ee8dcc4707a76eff58a471daa9d656e05da90a50030046e8a3ec04e2c7e1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8cdbf5e9-07f5-4b77-8a4b-3a21115115b7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 08:01:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"8cdbf5e9-07f5-4b77-8a4b-3a21115115b7","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983134","transactionId":"24ae45d0-faee-5810-a445-24db468cce3d","quoteStatus":"Quoted","message":"Quote# Q-983134 status changed to Quoted.","modifiedAt":"2025-08-05T08:01:17.904Z"},"publishedAt":"2025-08-05T08:01:18.000Z","csn":"5103159758"}
[webhook] [2025-08-05 08:35:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 08:35:02
[webhook] [2025-08-05 08:35:02] [adwsapi_v2.php:36]  Provided signature: sha256=4a4c553bf6cac23a01b8d30814cc47100cee8cf85598774ebecc8bac03addf2b
[webhook] [2025-08-05 08:35:02] [adwsapi_v2.php:37]  Calculated signature: sha256=7a6421c2dd0ae560dae428793691efabae388ba03fd04fa8cc1d972dce5abbb3
[webhook] [2025-08-05 08:35:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => dd8fd698bd64a22f\n    [X-B3-Traceid] => 6891c233c76f2e74804ceebcea38324d\n    [B3] => 6891c233c76f2e74804ceebcea38324d-dd8fd698bd64a22f-1\n    [Traceparent] => 00-6891c233c76f2e74804ceebcea38324d-dd8fd698bd64a22f-01\n    [X-Amzn-Trace-Id] => Root=1-6891c233-c76f2e74804ceebcea38324d;Parent=dd8fd698bd64a22f;Sampled=1\n    [X-Adsk-Signature] => sha256=4a4c553bf6cac23a01b8d30814cc47100cee8cf85598774ebecc8bac03addf2b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3b29563c-0665-4710-a6e2-f725e7dc1ab5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 08:35:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"3b29563c-0665-4710-a6e2-f725e7dc1ab5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-970477","transactionId":"ce524ae2-06f4-548c-9273-2be63c8f9e52","quoteStatus":"Order Submitted","message":"Quote# Q-970477 status changed to Order Submitted.","modifiedAt":"2025-08-05T08:34:59.482Z"},"publishedAt":"2025-08-05T08:34:59.000Z","csn":"5103159758"}
[webhook] [2025-08-05 08:35:03] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 08:35:03
[webhook] [2025-08-05 08:35:03] [adwsapi_v2.php:36]  Provided signature: sha256=74eb804d16612af05adc0e583c90fb6a8c83ed56e8827f7098990950a424a8c4
[webhook] [2025-08-05 08:35:03] [adwsapi_v2.php:37]  Calculated signature: sha256=197ed45a51d0cf1fd0dd48195d2fcf0070dbb10748c3e314b553b03e8504cf65
[webhook] [2025-08-05 08:35:03] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2cfd24e93d494984\n    [X-B3-Traceid] => 6891c234f149789e725f6516aa299c9a\n    [B3] => 6891c234f149789e725f6516aa299c9a-2cfd24e93d494984-1\n    [Traceparent] => 00-6891c234f149789e725f6516aa299c9a-2cfd24e93d494984-01\n    [X-Amzn-Trace-Id] => Root=1-6891c234-f149789e725f6516aa299c9a;Parent=2cfd24e93d494984;Sampled=1\n    [X-Adsk-Signature] => sha256=74eb804d16612af05adc0e583c90fb6a8c83ed56e8827f7098990950a424a8c4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 85ae9937-b85e-4047-8061-19066f161c66\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 08:35:03] [adwsapi_v2.php:57]  Received webhook data: {"id":"85ae9937-b85e-4047-8061-19066f161c66","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-970477","transactionId":"ce524ae2-06f4-548c-9273-2be63c8f9e52","quoteStatus":"Ordered","message":"Quote# Q-970477 status changed to Ordered.","modifiedAt":"2025-08-05T08:35:00.309Z"},"publishedAt":"2025-08-05T08:35:00.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:05:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:05:12
[webhook] [2025-08-05 09:05:12] [adwsapi_v2.php:36]  Provided signature: sha256=4ce27368e4573027c85f22c90b526d1982602f9770a2380f44083925c501c370
[webhook] [2025-08-05 09:05:12] [adwsapi_v2.php:37]  Calculated signature: sha256=81e43346403bc34fbc0af740c144df4ee925674eb9b6210e25c4d7f8f065bb09
[webhook] [2025-08-05 09:05:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 72432eb3bd317be4\n    [X-B3-Traceid] => 6891c9465df0d199614717c13fc0194e\n    [B3] => 6891c9465df0d199614717c13fc0194e-72432eb3bd317be4-1\n    [Traceparent] => 00-6891c9465df0d199614717c13fc0194e-72432eb3bd317be4-01\n    [X-Amzn-Trace-Id] => Root=1-6891c946-5df0d199614717c13fc0194e;Parent=72432eb3bd317be4;Sampled=1\n    [X-Adsk-Signature] => sha256=4ce27368e4573027c85f22c90b526d1982602f9770a2380f44083925c501c370\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f283a654-06fa-42e8-b3ff-031ebf38d68f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:05:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"f283a654-06fa-42e8-b3ff-031ebf38d68f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","status":"Active","quantity":1,"endDate":"2028-08-15","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-05T08:35:08.000+0000"},"publishedAt":"2025-08-05T09:05:10.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:09:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:09:30
[webhook] [2025-08-05 09:09:30] [adwsapi_v2.php:36]  Provided signature: sha256=0248f62f5404e937d95f8a3ca1ebb41846f5c35b82565023bd61a3cfd9426722
[webhook] [2025-08-05 09:09:30] [adwsapi_v2.php:37]  Calculated signature: sha256=35fd51c0df63f72fc16e9b93a042d94b9de3256222698aa9ecc735931fc2e5c8
[webhook] [2025-08-05 09:09:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 66d0ab8dc3380f65\n    [X-B3-Traceid] => 6891ca488bb4e138e0a522d858f68c29\n    [B3] => 6891ca488bb4e138e0a522d858f68c29-66d0ab8dc3380f65-1\n    [Traceparent] => 00-6891ca488bb4e138e0a522d858f68c29-66d0ab8dc3380f65-01\n    [X-Amzn-Trace-Id] => Root=1-6891ca48-8bb4e138e0a522d858f68c29;Parent=66d0ab8dc3380f65;Sampled=1\n    [X-Adsk-Signature] => sha256=0248f62f5404e937d95f8a3ca1ebb41846f5c35b82565023bd61a3cfd9426722\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7a94a0ab-33e5-4dd2-bd4c-ebed2071a06a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:09:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"7a94a0ab-33e5-4dd2-bd4c-ebed2071a06a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-955794","transactionId":"95c187f0-e0f1-5e78-9331-e70cc635a209","quoteStatus":"Order Submitted","message":"Quote# Q-955794 status changed to Order Submitted.","modifiedAt":"2025-08-05T09:09:28.107Z"},"publishedAt":"2025-08-05T09:09:28.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:09:32] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:09:32
[webhook] [2025-08-05 09:09:32] [adwsapi_v2.php:36]  Provided signature: sha256=8bc3894b185c13ea70e78c792ac7618187b2307be37787f3dd7d1fe71b134edb
[webhook] [2025-08-05 09:09:32] [adwsapi_v2.php:37]  Calculated signature: sha256=66e0f78ec86f2e8005254be6f796d55b24eb68ec394c0a95b605e147bafc7964
[webhook] [2025-08-05 09:09:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e94a0e57f2118bea\n    [X-B3-Traceid] => 6891ca4a010456235240d94d5e660558\n    [B3] => 6891ca4a010456235240d94d5e660558-e94a0e57f2118bea-1\n    [Traceparent] => 00-6891ca4a010456235240d94d5e660558-e94a0e57f2118bea-01\n    [X-Amzn-Trace-Id] => Root=1-6891ca4a-010456235240d94d5e660558;Parent=e94a0e57f2118bea;Sampled=1\n    [X-Adsk-Signature] => sha256=8bc3894b185c13ea70e78c792ac7618187b2307be37787f3dd7d1fe71b134edb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 084b4e54-8a1a-466f-809d-50406dccc010\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:09:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"084b4e54-8a1a-466f-809d-50406dccc010","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-955794","transactionId":"95c187f0-e0f1-5e78-9331-e70cc635a209","quoteStatus":"Ordered","message":"Quote# Q-955794 status changed to Ordered.","modifiedAt":"2025-08-05T09:09:30.110Z"},"publishedAt":"2025-08-05T09:09:30.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:36:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:36:30
[webhook] [2025-08-05 09:36:30] [adwsapi_v2.php:36]  Provided signature: sha256=38ac111c2a66533f8c9c0a3ac4551478ac7a430397129b76cb378886d075bd64
[webhook] [2025-08-05 09:36:30] [adwsapi_v2.php:37]  Calculated signature: sha256=85c2d94d4bf8f840920f366df41247bb0fac4dedfb07cbfb91a2e6b58196bb46
[webhook] [2025-08-05 09:36:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ceda3f9e4f05e55f\n    [X-B3-Traceid] => 6891d09ca9bf159449b88053838a38c7\n    [B3] => 6891d09ca9bf159449b88053838a38c7-ceda3f9e4f05e55f-1\n    [Traceparent] => 00-6891d09ca9bf159449b88053838a38c7-ceda3f9e4f05e55f-01\n    [X-Amzn-Trace-Id] => Root=1-6891d09c-a9bf159449b88053838a38c7;Parent=ceda3f9e4f05e55f;Sampled=1\n    [X-Adsk-Signature] => sha256=38ac111c2a66533f8c9c0a3ac4551478ac7a430397129b76cb378886d075bd64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3f77723e-e5ed-463b-8d74-b4fa53c66e27\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:36:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"3f77723e-e5ed-463b-8d74-b4fa53c66e27","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976696","transactionId":"f3e71575-8ad8-5ff4-9ef1-7473436e6dfa","quoteStatus":"Order Submitted","message":"Quote# Q-976696 status changed to Order Submitted.","modifiedAt":"2025-08-05T09:36:28.153Z"},"publishedAt":"2025-08-05T09:36:28.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:36:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:36:31
[webhook] [2025-08-05 09:36:31] [adwsapi_v2.php:36]  Provided signature: sha256=774ed12f518a40b6b43111cdf22a1396e0b73450269dcddede18d83443bbbc2d
[webhook] [2025-08-05 09:36:31] [adwsapi_v2.php:37]  Calculated signature: sha256=d331bd063cee1a1a4e2fedc8df5d16a64f1f170696aaa764cc860655dab7c903
[webhook] [2025-08-05 09:36:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 54ccf141321acf4c\n    [X-B3-Traceid] => 6891d09d855782d6679f21456783f329\n    [B3] => 6891d09d855782d6679f21456783f329-54ccf141321acf4c-1\n    [Traceparent] => 00-6891d09d855782d6679f21456783f329-54ccf141321acf4c-01\n    [X-Amzn-Trace-Id] => Root=1-6891d09d-855782d6679f21456783f329;Parent=54ccf141321acf4c;Sampled=1\n    [X-Adsk-Signature] => sha256=774ed12f518a40b6b43111cdf22a1396e0b73450269dcddede18d83443bbbc2d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a16d6f96-71c6-4c25-920b-220340d4a594\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:36:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"a16d6f96-71c6-4c25-920b-220340d4a594","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-976696","transactionId":"f3e71575-8ad8-5ff4-9ef1-7473436e6dfa","quoteStatus":"Ordered","message":"Quote# Q-976696 status changed to Ordered.","modifiedAt":"2025-08-05T09:36:29.431Z"},"publishedAt":"2025-08-05T09:36:29.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:39:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:39:41
[webhook] [2025-08-05 09:39:41] [adwsapi_v2.php:36]  Provided signature: sha256=d46b5abf7de19e8edb8e4ae2605086cb423ff760d8cd2edb22c952777974a8aa
[webhook] [2025-08-05 09:39:41] [adwsapi_v2.php:37]  Calculated signature: sha256=312833b65777bfbd7746f38fc741ce838deb5b4760d87cb9eb9713d9b3426d2c
[webhook] [2025-08-05 09:39:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cc3b16af819d527d\n    [X-B3-Traceid] => 6891d15b0cc496e91d99e11b24adc88b\n    [B3] => 6891d15b0cc496e91d99e11b24adc88b-cc3b16af819d527d-1\n    [Traceparent] => 00-6891d15b0cc496e91d99e11b24adc88b-cc3b16af819d527d-01\n    [X-Amzn-Trace-Id] => Root=1-6891d15b-0cc496e91d99e11b24adc88b;Parent=cc3b16af819d527d;Sampled=1\n    [X-Adsk-Signature] => sha256=d46b5abf7de19e8edb8e4ae2605086cb423ff760d8cd2edb22c952777974a8aa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 02186d2e-161c-4d79-8dd2-b9b5d11ff236\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:39:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"02186d2e-161c-4d79-8dd2-b9b5d11ff236","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62867699489049","status":"Active","quantity":1,"endDate":"2026-08-10","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-05T09:09:36.000+0000"},"publishedAt":"2025-08-05T09:39:39.000Z","csn":"5103159758"}
[webhook] [2025-08-05 09:40:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 09:40:15
[webhook] [2025-08-05 09:40:15] [adwsapi_v2.php:36]  Provided signature: sha256=c09ebe7ceca62df385d8fc227823f0f50acea4987c64e9fe31b08f46d5eb4b6c
[webhook] [2025-08-05 09:40:15] [adwsapi_v2.php:37]  Calculated signature: sha256=4010f83f111b1cefe8c6dd05999b189c6936a09af929c0e202a0dfbdfee3127d
[webhook] [2025-08-05 09:40:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 638bb31accf4d1ec\n    [X-B3-Traceid] => 6891d17d7d49cf8b582d983003e6fd2a\n    [B3] => 6891d17d7d49cf8b582d983003e6fd2a-638bb31accf4d1ec-1\n    [Traceparent] => 00-6891d17d7d49cf8b582d983003e6fd2a-638bb31accf4d1ec-01\n    [X-Amzn-Trace-Id] => Root=1-6891d17d-7d49cf8b582d983003e6fd2a;Parent=638bb31accf4d1ec;Sampled=1\n    [X-Adsk-Signature] => sha256=c09ebe7ceca62df385d8fc227823f0f50acea4987c64e9fe31b08f46d5eb4b6c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ece7561f-4ac1-4f8f-872c-96bbc6efdfb2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 09:40:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"ece7561f-4ac1-4f8f-872c-96bbc6efdfb2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55554012159617","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-05T09:05:10.000+0000"},"publishedAt":"2025-08-05T09:40:13.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:39:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:39:14
[webhook] [2025-08-05 11:39:14] [adwsapi_v2.php:36]  Provided signature: sha256=ede41831c1b9847788aeebc0b769f3d7340533e1fa3b14b6ded5d09161dc88d1
[webhook] [2025-08-05 11:39:14] [adwsapi_v2.php:37]  Calculated signature: sha256=4d3bd43cfe504b2c74c9473edb38f321734b6ef6fc3d3d2f7bcfa998c9224791
[webhook] [2025-08-05 11:39:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a33edd87fae45be7\n    [X-B3-Traceid] => 6891ed5f345f72762153de027b3069f6\n    [B3] => 6891ed5f345f72762153de027b3069f6-a33edd87fae45be7-1\n    [Traceparent] => 00-6891ed5f345f72762153de027b3069f6-a33edd87fae45be7-01\n    [X-Amzn-Trace-Id] => Root=1-6891ed5f-345f72762153de027b3069f6;Parent=a33edd87fae45be7;Sampled=1\n    [X-Adsk-Signature] => sha256=ede41831c1b9847788aeebc0b769f3d7340533e1fa3b14b6ded5d09161dc88d1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 09295e4a-984b-45fb-b347-113891241575\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:39:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"09295e4a-984b-45fb-b347-113891241575","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75438658522256","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T11:24:06.000+0000"},"publishedAt":"2025-08-05T11:39:11.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:39:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:39:58
[webhook] [2025-08-05 11:39:58] [adwsapi_v2.php:36]  Provided signature: sha256=8725967252b915990f6331e23d3b5b1720bef83253e65bbf24c5ff5ca6720cca
[webhook] [2025-08-05 11:39:58] [adwsapi_v2.php:37]  Calculated signature: sha256=0d07649e591f586f391da4d72bf92d143149096f8bb3a4894328d443f617b7ff
[webhook] [2025-08-05 11:39:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => da2a05198b393323\n    [X-B3-Traceid] => 6891ed8c502c784110088f26283793af\n    [B3] => 6891ed8c502c784110088f26283793af-da2a05198b393323-1\n    [Traceparent] => 00-6891ed8c502c784110088f26283793af-da2a05198b393323-01\n    [X-Amzn-Trace-Id] => Root=1-6891ed8c-502c784110088f26283793af;Parent=da2a05198b393323;Sampled=1\n    [X-Adsk-Signature] => sha256=8725967252b915990f6331e23d3b5b1720bef83253e65bbf24c5ff5ca6720cca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b2595929-d620-4429-80e8-2027cb302c37\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:39:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"b2595929-d620-4429-80e8-2027cb302c37","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62867699489049","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T11:19:50.000+0000"},"publishedAt":"2025-08-05T11:39:56.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:40:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:40:29
[webhook] [2025-08-05 11:40:29] [adwsapi_v2.php:36]  Provided signature: sha256=f6f392be79ca669de8bb4897458fd08d46cf076cd10d01b0336ef462a47cece8
[webhook] [2025-08-05 11:40:29] [adwsapi_v2.php:37]  Calculated signature: sha256=464f7caf4e5121f775ddb6ca26423a714329bdf152334c69fd8cb91b3f460e91
[webhook] [2025-08-05 11:40:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 45cf4e8077736377\n    [X-B3-Traceid] => 6891edaa23c2020157f20a0f1325c7ef\n    [B3] => 6891edaa23c2020157f20a0f1325c7ef-45cf4e8077736377-1\n    [Traceparent] => 00-6891edaa23c2020157f20a0f1325c7ef-45cf4e8077736377-01\n    [X-Amzn-Trace-Id] => Root=1-6891edaa-23c2020157f20a0f1325c7ef;Parent=45cf4e8077736377;Sampled=1\n    [X-Adsk-Signature] => sha256=f6f392be79ca669de8bb4897458fd08d46cf076cd10d01b0336ef462a47cece8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 615e75a4-3595-4f8a-a180-dd5f89ceadbe\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:40:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"615e75a4-3595-4f8a-a180-dd5f89ceadbe","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T11:20:20.000+0000"},"publishedAt":"2025-08-05T11:40:27.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:43:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:43:59
[webhook] [2025-08-05 11:43:59] [adwsapi_v2.php:36]  Provided signature: sha256=31bbddc1efd0510952cfa7b91221f8ac57e289ec4edb67b786050ca2e08b22c3
[webhook] [2025-08-05 11:43:59] [adwsapi_v2.php:37]  Calculated signature: sha256=1f665d6d1ade9f2d74da3bded3d57eddbc6d83c5bbd2ec3728aed6982104940d
[webhook] [2025-08-05 11:43:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3e71674d765babef\n    [X-B3-Traceid] => 6891ee7ced462f4bc4cac744ab26387d\n    [B3] => 6891ee7ced462f4bc4cac744ab26387d-3e71674d765babef-1\n    [Traceparent] => 00-6891ee7ced462f4bc4cac744ab26387d-3e71674d765babef-01\n    [X-Amzn-Trace-Id] => Root=1-6891ee7c-ed462f4bc4cac744ab26387d;Parent=3e71674d765babef;Sampled=1\n    [X-Adsk-Signature] => sha256=31bbddc1efd0510952cfa7b91221f8ac57e289ec4edb67b786050ca2e08b22c3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fe35ed1f-f5e4-47c5-9852-42ae6ab512dd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:43:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"fe35ed1f-f5e4-47c5-9852-42ae6ab512dd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983948","transactionId":"4eed2cf9-fd17-5477-baf3-4df20da2aa7a","quoteStatus":"Draft","message":"Quote# Q-983948 status changed to Draft.","modifiedAt":"2025-08-05T11:43:56.720Z"},"publishedAt":"2025-08-05T11:43:56.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:44:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:44:45
[webhook] [2025-08-05 11:44:45] [adwsapi_v2.php:36]  Provided signature: sha256=f16b62dcef866debfb4ac5801a648b187078d6d7b78c68a1f36f9ecbbc04d8a5
[webhook] [2025-08-05 11:44:45] [adwsapi_v2.php:37]  Calculated signature: sha256=8629e72e03efc6fb8690d6ba9e97fc8650770ae5f373bc3ceb3cfbb35bd43c89
[webhook] [2025-08-05 11:44:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b128b2fb19101349\n    [X-B3-Traceid] => 6891eeaab9c76b25a4dc527569123294\n    [B3] => 6891eeaab9c76b25a4dc527569123294-b128b2fb19101349-1\n    [Traceparent] => 00-6891eeaab9c76b25a4dc527569123294-b128b2fb19101349-01\n    [X-Amzn-Trace-Id] => Root=1-6891eeaa-b9c76b25a4dc527569123294;Parent=b128b2fb19101349;Sampled=1\n    [X-Adsk-Signature] => sha256=f16b62dcef866debfb4ac5801a648b187078d6d7b78c68a1f36f9ecbbc04d8a5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3603461d-033e-4983-9437-54cf15b91a74\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:44:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"3603461d-033e-4983-9437-54cf15b91a74","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983948","transactionId":"4eed2cf9-fd17-5477-baf3-4df20da2aa7a","quoteStatus":"Quoted","message":"Quote# Q-983948 status changed to Quoted.","modifiedAt":"2025-08-05T11:44:42.484Z"},"publishedAt":"2025-08-05T11:44:43.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:45:09] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:45:09
[webhook] [2025-08-05 11:45:09] [adwsapi_v2.php:36]  Provided signature: sha256=68d313ef7a14e5a1f817edfa1dc812383e8e7a7e58d939be18e599113e41a1f4
[webhook] [2025-08-05 11:45:09] [adwsapi_v2.php:37]  Calculated signature: sha256=bac9c0e32d46ff8eca9022247d5967a8a9757e95db17f47f172ae3b00221a0bc
[webhook] [2025-08-05 11:45:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 17c21f916b9f1ac1\n    [X-B3-Traceid] => 6891eec269d6acd6dbad29664df22e44\n    [B3] => 6891eec269d6acd6dbad29664df22e44-17c21f916b9f1ac1-1\n    [Traceparent] => 00-6891eec269d6acd6dbad29664df22e44-17c21f916b9f1ac1-01\n    [X-Amzn-Trace-Id] => Root=1-6891eec2-69d6acd6dbad29664df22e44;Parent=17c21f916b9f1ac1;Sampled=1\n    [X-Adsk-Signature] => sha256=68d313ef7a14e5a1f817edfa1dc812383e8e7a7e58d939be18e599113e41a1f4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d29381e0-4ae7-4c6b-a3d5-667334d60feb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:45:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"d29381e0-4ae7-4c6b-a3d5-667334d60feb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983952","transactionId":"f96020a0-8e75-5bc9-9168-41ab7e8ad80e","quoteStatus":"Draft","message":"Quote# Q-983952 status changed to Draft.","modifiedAt":"2025-08-05T11:45:06.457Z"},"publishedAt":"2025-08-05T11:45:06.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:46:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:46:11
[webhook] [2025-08-05 11:46:11] [adwsapi_v2.php:36]  Provided signature: sha256=5f0fb280727185f4606d2b382c3258e6e8fe87c3717d7973b48d8ac6f5790b9d
[webhook] [2025-08-05 11:46:11] [adwsapi_v2.php:37]  Calculated signature: sha256=7439cf6d44779cff811951dbf2b59c82cca14f7f3b3e9e4669e41710df3a301f
[webhook] [2025-08-05 11:46:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ea86f08365b22ab0\n    [X-B3-Traceid] => 6891ef00c08e1ee525db49867c5b112d\n    [B3] => 6891ef00c08e1ee525db49867c5b112d-ea86f08365b22ab0-1\n    [Traceparent] => 00-6891ef00c08e1ee525db49867c5b112d-ea86f08365b22ab0-01\n    [X-Amzn-Trace-Id] => Root=1-6891ef00-c08e1ee525db49867c5b112d;Parent=ea86f08365b22ab0;Sampled=1\n    [X-Adsk-Signature] => sha256=5f0fb280727185f4606d2b382c3258e6e8fe87c3717d7973b48d8ac6f5790b9d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a736dbbc-0329-4119-9bb9-185d3d6e9c9d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:46:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"a736dbbc-0329-4119-9bb9-185d3d6e9c9d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983952","transactionId":"f96020a0-8e75-5bc9-9168-41ab7e8ad80e","quoteStatus":"Quoted","message":"Quote# Q-983952 status changed to Quoted.","modifiedAt":"2025-08-05T11:46:08.430Z"},"publishedAt":"2025-08-05T11:46:08.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:50:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:50:36
[webhook] [2025-08-05 11:50:36] [adwsapi_v2.php:36]  Provided signature: sha256=2609f06cea0dfcf32fb0ad94b3cce8bcd24c940e7e8154e75c01b960b2617881
[webhook] [2025-08-05 11:50:36] [adwsapi_v2.php:37]  Calculated signature: sha256=61d5617aa11d8e8bb19bca69bcbb5cc3c289ab6739a1e4b6dfec8de4f4871573
[webhook] [2025-08-05 11:50:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9dde1935c6b4006a\n    [X-B3-Traceid] => 6891f00aa5a496be6d08f83825e0fb3b\n    [B3] => 6891f00aa5a496be6d08f83825e0fb3b-9dde1935c6b4006a-1\n    [Traceparent] => 00-6891f00aa5a496be6d08f83825e0fb3b-9dde1935c6b4006a-01\n    [X-Amzn-Trace-Id] => Root=1-6891f00a-a5a496be6d08f83825e0fb3b;Parent=9dde1935c6b4006a;Sampled=1\n    [X-Adsk-Signature] => sha256=2609f06cea0dfcf32fb0ad94b3cce8bcd24c940e7e8154e75c01b960b2617881\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 68f08f66-d24a-42e0-9cd2-95ced0fe59fa\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:50:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"68f08f66-d24a-42e0-9cd2-95ced0fe59fa","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-948973","transactionId":"929c5233-60fe-5d3d-a75b-32ca12b2d314","quoteStatus":"Order Submitted","message":"Quote# Q-948973 status changed to Order Submitted.","modifiedAt":"2025-08-05T11:50:34.057Z"},"publishedAt":"2025-08-05T11:50:34.000Z","csn":"5103159758"}
[webhook] [2025-08-05 11:50:37] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 11:50:37
[webhook] [2025-08-05 11:50:37] [adwsapi_v2.php:36]  Provided signature: sha256=d8981bc597378d9b4be0edea4fc50fd105a2f594b9682f5d111c136892beed4d
[webhook] [2025-08-05 11:50:37] [adwsapi_v2.php:37]  Calculated signature: sha256=d6b9618cf426b8053fa6982bacb1d9195b27c388af61d47132b946ffc9dba783
[webhook] [2025-08-05 11:50:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a62d9d95f6c116bb\n    [X-B3-Traceid] => 6891f00be940f7b52e0f363dad40114f\n    [B3] => 6891f00be940f7b52e0f363dad40114f-a62d9d95f6c116bb-1\n    [Traceparent] => 00-6891f00be940f7b52e0f363dad40114f-a62d9d95f6c116bb-01\n    [X-Amzn-Trace-Id] => Root=1-6891f00b-e940f7b52e0f363dad40114f;Parent=a62d9d95f6c116bb;Sampled=1\n    [X-Adsk-Signature] => sha256=d8981bc597378d9b4be0edea4fc50fd105a2f594b9682f5d111c136892beed4d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b4e9710f-6d85-4cec-bf23-22d1c6a56396\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 11:50:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"b4e9710f-6d85-4cec-bf23-22d1c6a56396","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-948973","transactionId":"929c5233-60fe-5d3d-a75b-32ca12b2d314","quoteStatus":"Ordered","message":"Quote# Q-948973 status changed to Ordered.","modifiedAt":"2025-08-05T11:50:35.048Z"},"publishedAt":"2025-08-05T11:50:35.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:10:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:10:28
[webhook] [2025-08-05 12:10:28] [adwsapi_v2.php:36]  Provided signature: sha256=e3b20c5b8c433a75d85b2eb0fbbffe72731bf7ea1734fc5c4f0e380eaa66e444
[webhook] [2025-08-05 12:10:28] [adwsapi_v2.php:37]  Calculated signature: sha256=1ebe22b420d0011f3822140439f3c037f5cf6193a1c9ea13946b6c24277b55c2
[webhook] [2025-08-05 12:10:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d8ab99d4645f4f5f\n    [X-B3-Traceid] => 6891f4b25cc4388c0d4f03c05127857c\n    [B3] => 6891f4b25cc4388c0d4f03c05127857c-d8ab99d4645f4f5f-1\n    [Traceparent] => 00-6891f4b25cc4388c0d4f03c05127857c-d8ab99d4645f4f5f-01\n    [X-Amzn-Trace-Id] => Root=1-6891f4b2-5cc4388c0d4f03c05127857c;Parent=d8ab99d4645f4f5f;Sampled=1\n    [X-Adsk-Signature] => sha256=e3b20c5b8c433a75d85b2eb0fbbffe72731bf7ea1734fc5c4f0e380eaa66e444\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754395826697-75136903143720\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:10:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754395826697-75136903143720","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75136903143720","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T12:10:26.697Z"},"publishedAt":"2025-08-05T12:10:26.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:10:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:10:48
[webhook] [2025-08-05 12:10:48] [adwsapi_v2.php:36]  Provided signature: sha256=cedb5e36f12d9fb26324b34486a06d4b65ebd42baf0331acbbeb28b27c2af23a
[webhook] [2025-08-05 12:10:48] [adwsapi_v2.php:37]  Calculated signature: sha256=95f6fc3f4d9184b67d43b7cc0413b3c1652108020d57fc565100f4b72bfbcd7c
[webhook] [2025-08-05 12:10:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b78edce5deca7ab8\n    [X-B3-Traceid] => 6891f4c5095185d739bcbd097e83880e\n    [B3] => 6891f4c5095185d739bcbd097e83880e-b78edce5deca7ab8-1\n    [Traceparent] => 00-6891f4c5095185d739bcbd097e83880e-b78edce5deca7ab8-01\n    [X-Amzn-Trace-Id] => Root=1-6891f4c5-095185d739bcbd097e83880e;Parent=b78edce5deca7ab8;Sampled=1\n    [X-Adsk-Signature] => sha256=cedb5e36f12d9fb26324b34486a06d4b65ebd42baf0331acbbeb28b27c2af23a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4ab71e6f-94f7-4f0a-b6e9-a00d26ca1ad7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:10:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"4ab71e6f-94f7-4f0a-b6e9-a00d26ca1ad7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995599209008","status":"Active","quantity":1,"endDate":"2026-08-07","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-05T11:50:43.000+0000"},"publishedAt":"2025-08-05T12:10:46.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:12:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:12:02
[webhook] [2025-08-05 12:12:02] [adwsapi_v2.php:36]  Provided signature: sha256=e61706d955a701a24a360987613c993e98abc96dc6d8aa95af251416d1bda828
[webhook] [2025-08-05 12:12:02] [adwsapi_v2.php:37]  Calculated signature: sha256=d91c4499754a6ace6284f85b7d2be6d0cfc93e5b46ea6c2e15d8b88aebabe908
[webhook] [2025-08-05 12:12:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3b57a2fdc4c5e9ad\n    [X-B3-Traceid] => 6891f510aaa87151d34b0cc626e72664\n    [B3] => 6891f510aaa87151d34b0cc626e72664-3b57a2fdc4c5e9ad-1\n    [Traceparent] => 00-6891f510aaa87151d34b0cc626e72664-3b57a2fdc4c5e9ad-01\n    [X-Amzn-Trace-Id] => Root=1-6891f510-aaa87151d34b0cc626e72664;Parent=3b57a2fdc4c5e9ad;Sampled=1\n    [X-Adsk-Signature] => sha256=e61706d955a701a24a360987613c993e98abc96dc6d8aa95af251416d1bda828\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4b4b4913-cb71-45ce-a8c0-e37337df35f3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:12:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"4b4b4913-cb71-45ce-a8c0-e37337df35f3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983952","transactionId":"f96020a0-8e75-5bc9-9168-41ab7e8ad80e","quoteStatus":"Order Submitted","message":"Quote# Q-983952 status changed to Order Submitted.","modifiedAt":"2025-08-05T12:12:00.521Z"},"publishedAt":"2025-08-05T12:12:00.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:12:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:12:07
[webhook] [2025-08-05 12:12:07] [adwsapi_v2.php:36]  Provided signature: sha256=89cdb00d41a8dd9f1e91c15a7e42c74091963eee805681b9f5b64c923479ec93
[webhook] [2025-08-05 12:12:07] [adwsapi_v2.php:37]  Calculated signature: sha256=c4e82b43965c7d8e042379a352d0d40cfbf5af71fe4d40f024b6b6978dd853d7
[webhook] [2025-08-05 12:12:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fbfe330ad936ef60\n    [X-B3-Traceid] => 6891f51543bf26ef56cc9457e0af6202\n    [B3] => 6891f51543bf26ef56cc9457e0af6202-fbfe330ad936ef60-1\n    [Traceparent] => 00-6891f51543bf26ef56cc9457e0af6202-fbfe330ad936ef60-01\n    [X-Amzn-Trace-Id] => Root=1-6891f515-43bf26ef56cc9457e0af6202;Parent=fbfe330ad936ef60;Sampled=1\n    [X-Adsk-Signature] => sha256=89cdb00d41a8dd9f1e91c15a7e42c74091963eee805681b9f5b64c923479ec93\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2915f78e-b71c-4b8d-9f45-c6142fbbe580\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:12:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"2915f78e-b71c-4b8d-9f45-c6142fbbe580","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983952","transactionId":"f96020a0-8e75-5bc9-9168-41ab7e8ad80e","quoteStatus":"Ordered","message":"Quote# Q-983952 status changed to Ordered.","modifiedAt":"2025-08-05T12:12:04.799Z"},"publishedAt":"2025-08-05T12:12:05.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:18:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:18:06
[webhook] [2025-08-05 12:18:06] [adwsapi_v2.php:36]  Provided signature: sha256=e45c8e70aa72b679e19e3e887e2d28b3ee27fb7a0305de23770cc03884828992
[webhook] [2025-08-05 12:18:06] [adwsapi_v2.php:37]  Calculated signature: sha256=256463ae3f5edc45410640087591d7241439d4d2dfa319739159eb15b6d2090b
[webhook] [2025-08-05 12:18:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2cf27c26311293b0\n    [X-B3-Traceid] => 6891f67c5376ea7c508df8b019482267\n    [B3] => 6891f67c5376ea7c508df8b019482267-2cf27c26311293b0-1\n    [Traceparent] => 00-6891f67c5376ea7c508df8b019482267-2cf27c26311293b0-01\n    [X-Amzn-Trace-Id] => Root=1-6891f67c-5376ea7c508df8b019482267;Parent=2cf27c26311293b0;Sampled=1\n    [X-Adsk-Signature] => sha256=e45c8e70aa72b679e19e3e887e2d28b3ee27fb7a0305de23770cc03884828992\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754396284405-75205764633108\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:18:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754396284405-75205764633108","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75205764633108","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T12:18:04.405Z"},"publishedAt":"2025-08-05T12:18:04.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:24:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:24:42
[webhook] [2025-08-05 12:24:42] [adwsapi_v2.php:36]  Provided signature: sha256=c3dd7dcb7b8ec770b68ba57d21b8f35561413edbe001ba919297d3a51d2ccb23
[webhook] [2025-08-05 12:24:42] [adwsapi_v2.php:37]  Calculated signature: sha256=c4a11d3ac56cdd35d656ba02d33a6337354bcf16f894070898adc737d2a3ddb5
[webhook] [2025-08-05 12:24:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b1cfe03c3ac80128\n    [X-B3-Traceid] => 6891f80816f039ab5a15c26e011e0c22\n    [B3] => 6891f80816f039ab5a15c26e011e0c22-b1cfe03c3ac80128-1\n    [Traceparent] => 00-6891f80816f039ab5a15c26e011e0c22-b1cfe03c3ac80128-01\n    [X-Amzn-Trace-Id] => Root=1-6891f808-16f039ab5a15c26e011e0c22;Parent=b1cfe03c3ac80128;Sampled=1\n    [X-Adsk-Signature] => sha256=c3dd7dcb7b8ec770b68ba57d21b8f35561413edbe001ba919297d3a51d2ccb23\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754396680311-75337738787712\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:24:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754396680311-75337738787712","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75337738787712","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T12:24:40.311Z"},"publishedAt":"2025-08-05T12:24:40.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:25:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:25:55
[webhook] [2025-08-05 12:25:55] [adwsapi_v2.php:36]  Provided signature: sha256=e2b56652f0f7ca22c0caaa19363048973d6ce3865706f61c418349318989d2dc
[webhook] [2025-08-05 12:25:55] [adwsapi_v2.php:37]  Calculated signature: sha256=854323d7820194c695e075270db9f0bdf4e91765e74f9f777fc08e098963fa1d
[webhook] [2025-08-05 12:25:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d1682f4ed84bf493\n    [X-B3-Traceid] => 6891f85104235e560e734c586bd84264\n    [B3] => 6891f85104235e560e734c586bd84264-d1682f4ed84bf493-1\n    [Traceparent] => 00-6891f85104235e560e734c586bd84264-d1682f4ed84bf493-01\n    [X-Amzn-Trace-Id] => Root=1-6891f851-04235e560e734c586bd84264;Parent=d1682f4ed84bf493;Sampled=1\n    [X-Adsk-Signature] => sha256=e2b56652f0f7ca22c0caaa19363048973d6ce3865706f61c418349318989d2dc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754396753085-75146432676914\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:25:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754396753085-75146432676914","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75146432676914","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T12:25:53.085Z"},"publishedAt":"2025-08-05T12:25:53.000Z","csn":"5103159758"}
[webhook] [2025-08-05 12:37:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 12:37:25
[webhook] [2025-08-05 12:37:25] [adwsapi_v2.php:36]  Provided signature: sha256=5ade239292327c8e152ae9ad96e7629df022829cc4a8c8f5dee11cd5d167abd0
[webhook] [2025-08-05 12:37:25] [adwsapi_v2.php:37]  Calculated signature: sha256=0ef3ecad39158e73993f67e46250ab3905d08634b707c0d99599d75df898cd89
[webhook] [2025-08-05 12:37:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f3b4ab136dd619cf\n    [X-B3-Traceid] => 6891fb0228968a16119fdab31e052826\n    [B3] => 6891fb0228968a16119fdab31e052826-f3b4ab136dd619cf-1\n    [Traceparent] => 00-6891fb0228968a16119fdab31e052826-f3b4ab136dd619cf-01\n    [X-Amzn-Trace-Id] => Root=1-6891fb02-28968a16119fdab31e052826;Parent=f3b4ab136dd619cf;Sampled=1\n    [X-Adsk-Signature] => sha256=5ade239292327c8e152ae9ad96e7629df022829cc4a8c8f5dee11cd5d167abd0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0f16795c-dce9-4846-bf1c-d8766167f4ea\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 12:37:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"0f16795c-dce9-4846-bf1c-d8766167f4ea","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55787120285135","status":"Active","quantity":2,"endDate":"2026-08-08","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-05T12:12:09.000+0000"},"publishedAt":"2025-08-05T12:37:22.000Z","csn":"5103159758"}
[webhook] [2025-08-05 13:42:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 13:42:12
[webhook] [2025-08-05 13:42:12] [adwsapi_v2.php:36]  Provided signature: sha256=e0a7bc3763f0c427a50f641dd710e858b7eadd656b5d3afa377c353214a3d039
[webhook] [2025-08-05 13:42:12] [adwsapi_v2.php:37]  Calculated signature: sha256=82abc29d9aad721bdada3fa811128a75ae36ceee9a9025c9cf46c1f6fb09fdaf
[webhook] [2025-08-05 13:42:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2e6ecd72bc861516\n    [X-B3-Traceid] => 68920a314c9f929e78d0547841872e16\n    [B3] => 68920a314c9f929e78d0547841872e16-2e6ecd72bc861516-1\n    [Traceparent] => 00-68920a314c9f929e78d0547841872e16-2e6ecd72bc861516-01\n    [X-Amzn-Trace-Id] => Root=1-68920a31-4c9f929e78d0547841872e16;Parent=2e6ecd72bc861516;Sampled=1\n    [X-Adsk-Signature] => sha256=e0a7bc3763f0c427a50f641dd710e858b7eadd656b5d3afa377c353214a3d039\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 14017d1a-bf59-4395-8b91-82d0ccc4fdbb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 13:42:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"14017d1a-bf59-4395-8b91-82d0ccc4fdbb","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69054087093103","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-05T13:07:06.000+0000"},"publishedAt":"2025-08-05T13:42:09.000Z","csn":"5103159758"}
[webhook] [2025-08-05 14:38:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 14:38:13
[webhook] [2025-08-05 14:38:13] [adwsapi_v2.php:36]  Provided signature: sha256=98e853256fb4446c4e0a5d9f3a3d25d5408cb3c91b5f6f51b332016269f2a6ac
[webhook] [2025-08-05 14:38:13] [adwsapi_v2.php:37]  Calculated signature: sha256=4e0e318c9ff3dd16d1e1a195a9502792c109b79b2718f5d550e7894ae7873d9e
[webhook] [2025-08-05 14:38:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b468ae0b12548a8d\n    [X-B3-Traceid] => 6892175353d1773233593bc86cdb5637\n    [B3] => 6892175353d1773233593bc86cdb5637-b468ae0b12548a8d-1\n    [Traceparent] => 00-6892175353d1773233593bc86cdb5637-b468ae0b12548a8d-01\n    [X-Amzn-Trace-Id] => Root=1-68921753-53d1773233593bc86cdb5637;Parent=b468ae0b12548a8d;Sampled=1\n    [X-Adsk-Signature] => sha256=98e853256fb4446c4e0a5d9f3a3d25d5408cb3c91b5f6f51b332016269f2a6ac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 56697628-648a-457f-a7aa-b40002a170f8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 14:38:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"56697628-648a-457f-a7aa-b40002a170f8","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56805113260413","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T14:13:08.000+0000"},"publishedAt":"2025-08-05T14:38:11.000Z","csn":"5103159758"}
[webhook] [2025-08-05 14:44:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 14:44:56
[webhook] [2025-08-05 14:44:56] [adwsapi_v2.php:36]  Provided signature: sha256=22c3be0a00a25098e7e01918b60d0f101299a4b91f1e6126bc6788be3daf33de
[webhook] [2025-08-05 14:44:56] [adwsapi_v2.php:37]  Calculated signature: sha256=04a360fbe562626d1dc6936ad6c22a67a81ae7608a7fadc61c4420aab5f0362c
[webhook] [2025-08-05 14:44:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8977aac4c78bb2a5\n    [X-B3-Traceid] => 689218e682f0adfae58a9bdb28561bd0\n    [B3] => 689218e682f0adfae58a9bdb28561bd0-8977aac4c78bb2a5-1\n    [Traceparent] => 00-689218e682f0adfae58a9bdb28561bd0-8977aac4c78bb2a5-01\n    [X-Amzn-Trace-Id] => Root=1-689218e6-82f0adfae58a9bdb28561bd0;Parent=8977aac4c78bb2a5;Sampled=1\n    [X-Adsk-Signature] => sha256=22c3be0a00a25098e7e01918b60d0f101299a4b91f1e6126bc6788be3daf33de\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 82a12feb-84d0-4782-af1f-b67797467d5f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 14:44:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"82a12feb-84d0-4782-af1f-b67797467d5f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-984562","transactionId":"65694390-4883-5634-93f1-229c5391befa","quoteStatus":"Draft","message":"Quote# Q-984562 status changed to Draft.","modifiedAt":"2025-08-05T14:44:53.987Z"},"publishedAt":"2025-08-05T14:44:54.000Z","csn":"5103159758"}
[webhook] [2025-08-05 14:46:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 14:46:13
[webhook] [2025-08-05 14:46:13] [adwsapi_v2.php:36]  Provided signature: sha256=553331b17d5b1c4972cccdb6ebbc45373b2892b06806817892bfd3aa8e8363a9
[webhook] [2025-08-05 14:46:13] [adwsapi_v2.php:37]  Calculated signature: sha256=e5e80f2fd204fd9560cbef40f5906bb1419df85701df1813f4b241f8cfaf5680
[webhook] [2025-08-05 14:46:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 215ec6a37596cc6a\n    [X-B3-Traceid] => 68921932699f1072e751ae649ea8428d\n    [B3] => 68921932699f1072e751ae649ea8428d-215ec6a37596cc6a-1\n    [Traceparent] => 00-68921932699f1072e751ae649ea8428d-215ec6a37596cc6a-01\n    [X-Amzn-Trace-Id] => Root=1-68921932-699f1072e751ae649ea8428d;Parent=215ec6a37596cc6a;Sampled=1\n    [X-Adsk-Signature] => sha256=553331b17d5b1c4972cccdb6ebbc45373b2892b06806817892bfd3aa8e8363a9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c360ac3c-ecce-430e-ae4e-2448d8da9c52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 14:46:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"c360ac3c-ecce-430e-ae4e-2448d8da9c52","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-984562","transactionId":"65694390-4883-5634-93f1-229c5391befa","quoteStatus":"Quoted","message":"Quote# Q-984562 status changed to Quoted.","modifiedAt":"2025-08-05T14:46:09.847Z"},"publishedAt":"2025-08-05T14:46:10.000Z","csn":"5103159758"}
[webhook] [2025-08-05 14:55:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 14:55:26
[webhook] [2025-08-05 14:55:26] [adwsapi_v2.php:36]  Provided signature: sha256=b532f62c969e42c7241943459aacc3233b0c50bda7ec4749cf9f61ae5434645b
[webhook] [2025-08-05 14:55:26] [adwsapi_v2.php:37]  Calculated signature: sha256=b6928e63ae76b37edbf7e9381e58c3ab17dee909c90bded69a974b90de266c7a
[webhook] [2025-08-05 14:55:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3eacf48c043d4b69\n    [X-B3-Traceid] => 68921b5b9f5f39248cd33c08ccfb895b\n    [B3] => 68921b5b9f5f39248cd33c08ccfb895b-3eacf48c043d4b69-1\n    [Traceparent] => 00-68921b5b9f5f39248cd33c08ccfb895b-3eacf48c043d4b69-01\n    [X-Amzn-Trace-Id] => Root=1-68921b5b-9f5f39248cd33c08ccfb895b;Parent=3eacf48c043d4b69;Sampled=1\n    [X-Adsk-Signature] => sha256=b532f62c969e42c7241943459aacc3233b0c50bda7ec4749cf9f61ae5434645b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 47d86b04-40a3-4321-8efa-f0f6eff43c14\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 14:55:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"47d86b04-40a3-4321-8efa-f0f6eff43c14","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-984562","transactionId":"65694390-4883-5634-93f1-229c5391befa","quoteStatus":"Order Submitted","message":"Quote# Q-984562 status changed to Order Submitted.","modifiedAt":"2025-08-05T14:55:23.632Z"},"publishedAt":"2025-08-05T14:55:23.000Z","csn":"5103159758"}
[webhook] [2025-08-05 14:55:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 14:55:30
[webhook] [2025-08-05 14:55:30] [adwsapi_v2.php:36]  Provided signature: sha256=e3e0dbecd220e2b556fd1a09ab68925d99849d61c601b075fb389bbeed2b5018
[webhook] [2025-08-05 14:55:30] [adwsapi_v2.php:37]  Calculated signature: sha256=f77d8a158acacda9fb771eba0df085efcc52f054f9ca899e24b9bfc3fce7de3f
[webhook] [2025-08-05 14:55:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 15814f355a122109\n    [X-B3-Traceid] => 68921b5f8fe7a529fee0e49bcf8e8a93\n    [B3] => 68921b5f8fe7a529fee0e49bcf8e8a93-15814f355a122109-1\n    [Traceparent] => 00-68921b5f8fe7a529fee0e49bcf8e8a93-15814f355a122109-01\n    [X-Amzn-Trace-Id] => Root=1-68921b5f-8fe7a529fee0e49bcf8e8a93;Parent=15814f355a122109;Sampled=1\n    [X-Adsk-Signature] => sha256=e3e0dbecd220e2b556fd1a09ab68925d99849d61c601b075fb389bbeed2b5018\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 880c97eb-c188-4cf5-a283-fdc78a5b7dbc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 14:55:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"880c97eb-c188-4cf5-a283-fdc78a5b7dbc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-984562","transactionId":"65694390-4883-5634-93f1-229c5391befa","quoteStatus":"Ordered","message":"Quote# Q-984562 status changed to Ordered.","modifiedAt":"2025-08-05T14:55:26.857Z"},"publishedAt":"2025-08-05T14:55:27.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:07:34] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:07:34
[webhook] [2025-08-05 15:07:34] [adwsapi_v2.php:36]  Provided signature: sha256=63511ec361b2fd95dccd0de9e3a248015648621ed987b248c28c674bca74ca12
[webhook] [2025-08-05 15:07:34] [adwsapi_v2.php:37]  Calculated signature: sha256=98544ef78656b075fa70adbd42e728d740df18b18a2316ea6276345c1dfae0e7
[webhook] [2025-08-05 15:07:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4be59b681fc05e62\n    [X-B3-Traceid] => 68921e346784b0d80b54c4a1438d9605\n    [B3] => 68921e346784b0d80b54c4a1438d9605-4be59b681fc05e62-1\n    [Traceparent] => 00-68921e346784b0d80b54c4a1438d9605-4be59b681fc05e62-01\n    [X-Amzn-Trace-Id] => Root=1-68921e34-6784b0d80b54c4a1438d9605;Parent=4be59b681fc05e62;Sampled=1\n    [X-Adsk-Signature] => sha256=63511ec361b2fd95dccd0de9e3a248015648621ed987b248c28c674bca74ca12\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 510782e8-efd1-4e0e-a561-700b60b1b32c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:07:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"510782e8-efd1-4e0e-a561-700b60b1b32c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"68510614450605","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-05T14:47:29.000+0000"},"publishedAt":"2025-08-05T15:07:32.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:07:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:07:35
[webhook] [2025-08-05 15:07:35] [adwsapi_v2.php:36]  Provided signature: sha256=9a5ad956653034bad3b1a76ffec516c902b81e752d265265fc54d4d6244dfa39
[webhook] [2025-08-05 15:07:35] [adwsapi_v2.php:37]  Calculated signature: sha256=ddf076916eb5f5cb69856bd80e3b67767b11e8ea477339589e5a1a9fa08e7dbd
[webhook] [2025-08-05 15:07:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8f455b773cf2bbcd\n    [X-B3-Traceid] => 68921e3567e6fe69002e4a6c1f5b65af\n    [B3] => 68921e3567e6fe69002e4a6c1f5b65af-8f455b773cf2bbcd-1\n    [Traceparent] => 00-68921e3567e6fe69002e4a6c1f5b65af-8f455b773cf2bbcd-01\n    [X-Amzn-Trace-Id] => Root=1-68921e35-67e6fe69002e4a6c1f5b65af;Parent=8f455b773cf2bbcd;Sampled=1\n    [X-Adsk-Signature] => sha256=9a5ad956653034bad3b1a76ffec516c902b81e752d265265fc54d4d6244dfa39\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 960682ac-7f86-4bf6-b3b0-de903be54db6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:07:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"960682ac-7f86-4bf6-b3b0-de903be54db6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"64198805055631","quantity":3,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-05T14:47:30.000+0000"},"publishedAt":"2025-08-05T15:07:33.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:10:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:10:40
[webhook] [2025-08-05 15:10:40] [adwsapi_v2.php:36]  Provided signature: sha256=83d23efd4ca576b172597525e18caea858b107a7bcebe165712b81c50b04735a
[webhook] [2025-08-05 15:10:40] [adwsapi_v2.php:37]  Calculated signature: sha256=a9a188a43f8c6ee49abce02a172dc6b40b46b10300f8f62cd52c064d0fddbbf2
[webhook] [2025-08-05 15:10:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 84f4febc5462be74\n    [X-B3-Traceid] => 68921eed2f72f00c790ef4ea37eaf858\n    [B3] => 68921eed2f72f00c790ef4ea37eaf858-84f4febc5462be74-1\n    [Traceparent] => 00-68921eed2f72f00c790ef4ea37eaf858-84f4febc5462be74-01\n    [X-Amzn-Trace-Id] => Root=1-68921eed-2f72f00c790ef4ea37eaf858;Parent=84f4febc5462be74;Sampled=1\n    [X-Adsk-Signature] => sha256=83d23efd4ca576b172597525e18caea858b107a7bcebe165712b81c50b04735a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 039e920c-280b-4886-b4e4-649e6a44904d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:10:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"039e920c-280b-4886-b4e4-649e6a44904d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72359337050211","status":"Active","quantity":5,"endDate":"2026-08-12","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-05T14:55:35.000+0000"},"publishedAt":"2025-08-05T15:10:37.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:36:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:36:46
[webhook] [2025-08-05 15:36:46] [adwsapi_v2.php:36]  Provided signature: sha256=8645a7b6739d70126aec7423b8a8c9043abe29e90226bbd920441be71dfe032e
[webhook] [2025-08-05 15:36:46] [adwsapi_v2.php:37]  Calculated signature: sha256=2b90b389e350069cb774acc0effa52d7d2757e69182ea55728c004720ec95525
[webhook] [2025-08-05 15:36:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7af69a8e332d4ec4\n    [X-B3-Traceid] => 6892250b371409532561c3d63fe86f6a\n    [B3] => 6892250b371409532561c3d63fe86f6a-7af69a8e332d4ec4-1\n    [Traceparent] => 00-6892250b371409532561c3d63fe86f6a-7af69a8e332d4ec4-01\n    [X-Amzn-Trace-Id] => Root=1-6892250b-371409532561c3d63fe86f6a;Parent=7af69a8e332d4ec4;Sampled=1\n    [X-Adsk-Signature] => sha256=8645a7b6739d70126aec7423b8a8c9043abe29e90226bbd920441be71dfe032e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 38e703cd-0cce-479f-9952-427884025bd9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:36:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"38e703cd-0cce-479f-9952-427884025bd9","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995599209008","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T15:11:36.000+0000"},"publishedAt":"2025-08-05T15:36:43.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:37:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:37:40
[webhook] [2025-08-05 15:37:40] [adwsapi_v2.php:36]  Provided signature: sha256=c1e3ed80c99f6c8a0b44336dd374db562666a299f5824476536937bf29f2827b
[webhook] [2025-08-05 15:37:40] [adwsapi_v2.php:37]  Calculated signature: sha256=5f175fdf33137fd803ce4253b4efa7a9f5a728c3ad9c0be834cf5c67181ce2c8
[webhook] [2025-08-05 15:37:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5d4667e792188d00\n    [X-B3-Traceid] => 6892254167809cc639cc26972c3e7ce9\n    [B3] => 6892254167809cc639cc26972c3e7ce9-5d4667e792188d00-1\n    [Traceparent] => 00-6892254167809cc639cc26972c3e7ce9-5d4667e792188d00-01\n    [X-Amzn-Trace-Id] => Root=1-68922541-67809cc639cc26972c3e7ce9;Parent=5d4667e792188d00;Sampled=1\n    [X-Adsk-Signature] => sha256=c1e3ed80c99f6c8a0b44336dd374db562666a299f5824476536937bf29f2827b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bec2d0bb-3b4c-405e-b2cc-8f13dcb4ca2c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:37:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"bec2d0bb-3b4c-405e-b2cc-8f13dcb4ca2c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995907157636","status":"Active","quantity":1,"endDate":"2026-08-07","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-05T15:22:34.000+0000"},"publishedAt":"2025-08-05T15:37:37.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:38:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:38:19
[webhook] [2025-08-05 15:38:19] [adwsapi_v2.php:36]  Provided signature: sha256=b481852e250fb1a8d9dcb999adebe324c04649884d681ed5ed0040419b69d4b7
[webhook] [2025-08-05 15:38:19] [adwsapi_v2.php:37]  Calculated signature: sha256=e1f45bac14fd20fa0e599848fcb50523bb3858266a47d53fd1332f2427519a5d
[webhook] [2025-08-05 15:38:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4a2ce81b26ba2881\n    [X-B3-Traceid] => 68922569460bf62a4012f08e6a8dc561\n    [B3] => 68922569460bf62a4012f08e6a8dc561-4a2ce81b26ba2881-1\n    [Traceparent] => 00-68922569460bf62a4012f08e6a8dc561-4a2ce81b26ba2881-01\n    [X-Amzn-Trace-Id] => Root=1-68922569-460bf62a4012f08e6a8dc561;Parent=4a2ce81b26ba2881;Sampled=1\n    [X-Adsk-Signature] => sha256=b481852e250fb1a8d9dcb999adebe324c04649884d681ed5ed0040419b69d4b7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8242eeed-15b3-44be-8aee-a30b1a8d267e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:38:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"8242eeed-15b3-44be-8aee-a30b1a8d267e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72359337050211","quantity":5,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T15:13:10.000+0000"},"publishedAt":"2025-08-05T15:38:17.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:39:18] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:39:18
[webhook] [2025-08-05 15:39:18] [adwsapi_v2.php:36]  Provided signature: sha256=fc792f8d9605da7e29a89c2e7d35aa87f8687c55631b69ad8f0e6ab72c254949
[webhook] [2025-08-05 15:39:18] [adwsapi_v2.php:37]  Calculated signature: sha256=a49237db44d4d11d69e1f927169cca21fd3a3d32b92b91efd1f54cdb40be5a10
[webhook] [2025-08-05 15:39:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec052fb83d669505\n    [X-B3-Traceid] => 689225a41d669dcd522d405b2874716f\n    [B3] => 689225a41d669dcd522d405b2874716f-ec052fb83d669505-1\n    [Traceparent] => 00-689225a41d669dcd522d405b2874716f-ec052fb83d669505-01\n    [X-Amzn-Trace-Id] => Root=1-689225a4-1d669dcd522d405b2874716f;Parent=ec052fb83d669505;Sampled=1\n    [X-Adsk-Signature] => sha256=fc792f8d9605da7e29a89c2e7d35aa87f8687c55631b69ad8f0e6ab72c254949\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 005d5619-3e1d-4712-956c-0fd6a776b76b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:39:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"005d5619-3e1d-4712-956c-0fd6a776b76b","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55787120285135","quantity":2,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T15:09:09.000+0000"},"publishedAt":"2025-08-05T15:39:16.000Z","csn":"5103159758"}
[webhook] [2025-08-05 15:41:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 15:41:19
[webhook] [2025-08-05 15:41:19] [adwsapi_v2.php:36]  Provided signature: sha256=23fbb4957d6a6607f9ff864fa17bce4474d87b2a4375c5674831b10638e185f5
[webhook] [2025-08-05 15:41:19] [adwsapi_v2.php:37]  Calculated signature: sha256=28bf5d2f993bc8487bcddf3d46444956574efa802dcc87721107d794112385e8
[webhook] [2025-08-05 15:41:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 58eea41e43fbb059\n    [X-B3-Traceid] => 6892261c11ac3ad02dbacc14553078c9\n    [B3] => 6892261c11ac3ad02dbacc14553078c9-58eea41e43fbb059-1\n    [Traceparent] => 00-6892261c11ac3ad02dbacc14553078c9-58eea41e43fbb059-01\n    [X-Amzn-Trace-Id] => Root=1-6892261c-11ac3ad02dbacc14553078c9;Parent=58eea41e43fbb059;Sampled=1\n    [X-Adsk-Signature] => sha256=23fbb4957d6a6607f9ff864fa17bce4474d87b2a4375c5674831b10638e185f5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b06671fb-005d-4e4f-b601-f1bfa33f6aa9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 15:41:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"b06671fb-005d-4e4f-b601-f1bfa33f6aa9","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72359337050211","quantity":5,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T15:26:14.000+0000"},"publishedAt":"2025-08-05T15:41:16.000Z","csn":"5103159758"}
[webhook] [2025-08-05 16:02:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 16:02:15
[webhook] [2025-08-05 16:02:15] [adwsapi_v2.php:36]  Provided signature: sha256=45b50195d208250f015457a755e0baf683e4051a9e9ded5b0eb53dd443a54686
[webhook] [2025-08-05 16:02:15] [adwsapi_v2.php:37]  Calculated signature: sha256=3189318820aec4f6afd0b969807e2fadf17bc574d2fd6b6957c584baed8850dc
[webhook] [2025-08-05 16:02:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fb468249b9a9b19f\n    [X-B3-Traceid] => 68922b05188b64ab6f1d5ad927344b11\n    [B3] => 68922b05188b64ab6f1d5ad927344b11-fb468249b9a9b19f-1\n    [Traceparent] => 00-68922b05188b64ab6f1d5ad927344b11-fb468249b9a9b19f-01\n    [X-Amzn-Trace-Id] => Root=1-68922b05-188b64ab6f1d5ad927344b11;Parent=fb468249b9a9b19f;Sampled=1\n    [X-Adsk-Signature] => sha256=45b50195d208250f015457a755e0baf683e4051a9e9ded5b0eb53dd443a54686\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754409733109-75438658522256\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 16:02:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754409733109-75438658522256","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75438658522256","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T16:02:13.109Z"},"publishedAt":"2025-08-05T16:02:13.000Z","csn":"5103159758"}
[webhook] [2025-08-05 16:02:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 16:02:19
[webhook] [2025-08-05 16:02:19] [adwsapi_v2.php:36]  Provided signature: sha256=7d9b50a3bed27e2d2db9ce49e7842420339e88bad2df87d5b64602a324cc6139
[webhook] [2025-08-05 16:02:19] [adwsapi_v2.php:37]  Calculated signature: sha256=4df1bd56a1560417af081be04cc2fc92af4a1f4c068779d2498ee8e716598436
[webhook] [2025-08-05 16:02:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b4c9b0730da1dfcf\n    [X-B3-Traceid] => 68922b0913a565dd4c5eb4593e7a5470\n    [B3] => 68922b0913a565dd4c5eb4593e7a5470-b4c9b0730da1dfcf-1\n    [Traceparent] => 00-68922b0913a565dd4c5eb4593e7a5470-b4c9b0730da1dfcf-01\n    [X-Amzn-Trace-Id] => Root=1-68922b09-13a565dd4c5eb4593e7a5470;Parent=b4c9b0730da1dfcf;Sampled=1\n    [X-Adsk-Signature] => sha256=7d9b50a3bed27e2d2db9ce49e7842420339e88bad2df87d5b64602a324cc6139\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754409737874-66065226907420\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 16:02:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754409737874-66065226907420","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T16:02:17.874Z"},"publishedAt":"2025-08-05T16:02:17.000Z","csn":"5103159758"}
[webhook] [2025-08-05 16:07:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 16:07:20
[webhook] [2025-08-05 16:07:20] [adwsapi_v2.php:36]  Provided signature: sha256=a231533fb01750e7bd0511b218a34427684a67d2612ae610645caee8dd3c6433
[webhook] [2025-08-05 16:07:20] [adwsapi_v2.php:37]  Calculated signature: sha256=b54b399eb316aefb5a94c436ebb00b99e9539f5de9174bfb695233bf2c54da75
[webhook] [2025-08-05 16:07:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 39e653b242da6380\n    [X-B3-Traceid] => 68922c36246caf1b713ced966ee8e958\n    [B3] => 68922c36246caf1b713ced966ee8e958-39e653b242da6380-1\n    [Traceparent] => 00-68922c36246caf1b713ced966ee8e958-39e653b242da6380-01\n    [X-Amzn-Trace-Id] => Root=1-68922c36-246caf1b713ced966ee8e958;Parent=39e653b242da6380;Sampled=1\n    [X-Adsk-Signature] => sha256=a231533fb01750e7bd0511b218a34427684a67d2612ae610645caee8dd3c6433\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754410038115-62867699489049\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 16:07:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754410038115-62867699489049","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62867699489049","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T16:07:18.115Z"},"publishedAt":"2025-08-05T16:07:18.000Z","csn":"5103159758"}
[webhook] [2025-08-05 16:10:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 16:10:40
[webhook] [2025-08-05 16:10:40] [adwsapi_v2.php:36]  Provided signature: sha256=9b40ed6c0c0e24539f1d0614f830ffd675d9f0ffab9b08e8a3608869eb88fbfd
[webhook] [2025-08-05 16:10:40] [adwsapi_v2.php:37]  Calculated signature: sha256=672ae924f27a571a581588f6c2ee517120564a458150b0797c5144af09a050a9
[webhook] [2025-08-05 16:10:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 770cb8f499fbc57c\n    [X-B3-Traceid] => 68922cfe4b4b1dd05566583041c3b892\n    [B3] => 68922cfe4b4b1dd05566583041c3b892-770cb8f499fbc57c-1\n    [Traceparent] => 00-68922cfe4b4b1dd05566583041c3b892-770cb8f499fbc57c-01\n    [X-Amzn-Trace-Id] => Root=1-68922cfe-4b4b1dd05566583041c3b892;Parent=770cb8f499fbc57c;Sampled=1\n    [X-Adsk-Signature] => sha256=9b40ed6c0c0e24539f1d0614f830ffd675d9f0ffab9b08e8a3608869eb88fbfd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754410238296-65995599209008\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 16:10:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754410238296-65995599209008","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995599209008","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T16:10:38.296Z"},"publishedAt":"2025-08-05T16:10:38.000Z","csn":"5103159758"}
[webhook] [2025-08-05 16:11:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 16:11:29
[webhook] [2025-08-05 16:11:29] [adwsapi_v2.php:36]  Provided signature: sha256=e3b9ff164a92607730e4fe898ca254c608471c4325620eb00e960efc190572ba
[webhook] [2025-08-05 16:11:29] [adwsapi_v2.php:37]  Calculated signature: sha256=14655951e0963d7a180e4c3e3c61cc0fc84b83eea04dc8851f2c448ba2b0237f
[webhook] [2025-08-05 16:11:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fa593517e051dd69\n    [X-B3-Traceid] => 68922d2f0d271d6b718b58c2395a03ad\n    [B3] => 68922d2f0d271d6b718b58c2395a03ad-fa593517e051dd69-1\n    [Traceparent] => 00-68922d2f0d271d6b718b58c2395a03ad-fa593517e051dd69-01\n    [X-Amzn-Trace-Id] => Root=1-68922d2f-0d271d6b718b58c2395a03ad;Parent=fa593517e051dd69;Sampled=1\n    [X-Adsk-Signature] => sha256=e3b9ff164a92607730e4fe898ca254c608471c4325620eb00e960efc190572ba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754410287196-55787120285135\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 16:11:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754410287196-55787120285135","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55787120285135","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T16:11:27.196Z"},"publishedAt":"2025-08-05T16:11:27.000Z","csn":"5103159758"}
[webhook] [2025-08-05 16:37:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 16:37:27
[webhook] [2025-08-05 16:37:27] [adwsapi_v2.php:36]  Provided signature: sha256=abe6f124c2e43a627b39dd9b43f183eb46b8310ef016e205b15474c8f9e2bb73
[webhook] [2025-08-05 16:37:27] [adwsapi_v2.php:37]  Calculated signature: sha256=d7e135311b34ddb1a76e25c1c473796dadc9814b74d467c0e02a97910bf4dd85
[webhook] [2025-08-05 16:37:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 352a8ea21b1d3f46\n    [X-B3-Traceid] => 689233442e51e3fc2956b9f203b3a9f2\n    [B3] => 689233442e51e3fc2956b9f203b3a9f2-352a8ea21b1d3f46-1\n    [Traceparent] => 00-689233442e51e3fc2956b9f203b3a9f2-352a8ea21b1d3f46-01\n    [X-Amzn-Trace-Id] => Root=1-68923344-2e51e3fc2956b9f203b3a9f2;Parent=352a8ea21b1d3f46;Sampled=1\n    [X-Adsk-Signature] => sha256=abe6f124c2e43a627b39dd9b43f183eb46b8310ef016e205b15474c8f9e2bb73\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 35c10583-4d92-476a-a70e-72f7c5a28541\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 16:37:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"35c10583-4d92-476a-a70e-72f7c5a28541","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55553297758399","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-05T16:22:22.000+0000"},"publishedAt":"2025-08-05T16:37:24.000Z","csn":"5103159758"}
[webhook] [2025-08-05 19:39:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 19:39:42
[webhook] [2025-08-05 19:39:42] [adwsapi_v2.php:36]  Provided signature: sha256=939136018d64060ea92b2999805fad392f454efff07b44e84d88dd9ec6d7f8b7
[webhook] [2025-08-05 19:39:42] [adwsapi_v2.php:37]  Calculated signature: sha256=b0159188fb0f27830bc02a80e9c08759d13b67c24a2933d901cfa5cec6f9fa64
[webhook] [2025-08-05 19:39:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 51282c1a403c5779\n    [X-B3-Traceid] => 68925dfb4f077b9310af24f5514c1bfb\n    [B3] => 68925dfb4f077b9310af24f5514c1bfb-51282c1a403c5779-1\n    [Traceparent] => 00-68925dfb4f077b9310af24f5514c1bfb-51282c1a403c5779-01\n    [X-Amzn-Trace-Id] => Root=1-68925dfb-4f077b9310af24f5514c1bfb;Parent=51282c1a403c5779;Sampled=1\n    [X-Adsk-Signature] => sha256=939136018d64060ea92b2999805fad392f454efff07b44e84d88dd9ec6d7f8b7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 70294a09-8bb9-4cc9-b1b2-46335e9837eb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 19:39:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"70294a09-8bb9-4cc9-b1b2-46335e9837eb","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995907157636","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-05T19:14:34.000+0000"},"publishedAt":"2025-08-05T19:39:39.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:05:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:05:14
[webhook] [2025-08-05 20:05:14] [adwsapi_v2.php:36]  Provided signature: sha256=2b87ef6dc34c6d8cd0f061b1f47aa428f6472c592a6c43309e00efaf330865c7
[webhook] [2025-08-05 20:05:14] [adwsapi_v2.php:37]  Calculated signature: sha256=7e039665b6f4157b0e87f87a009c20f1ca3630e2ef803cc36109134ef1f4c6b7
[webhook] [2025-08-05 20:05:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => eb0e73dd89dfc4a8\n    [X-B3-Traceid] => 689263f8619a771151691c5f595d382a\n    [B3] => 689263f8619a771151691c5f595d382a-eb0e73dd89dfc4a8-1\n    [Traceparent] => 00-689263f8619a771151691c5f595d382a-eb0e73dd89dfc4a8-01\n    [X-Amzn-Trace-Id] => Root=1-689263f8-619a771151691c5f595d382a;Parent=eb0e73dd89dfc4a8;Sampled=1\n    [X-Adsk-Signature] => sha256=2b87ef6dc34c6d8cd0f061b1f47aa428f6472c592a6c43309e00efaf330865c7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754424312434-65995907157636\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:05:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754424312434-65995907157636","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995907157636","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:05:12.434Z"},"publishedAt":"2025-08-05T20:05:12.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:06:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:06:58
[webhook] [2025-08-05 20:06:58] [adwsapi_v2.php:36]  Provided signature: sha256=6482bc11e2e06132c1c8f599258357d463f87ad1ee14ce32fb22c0819e5ffddc
[webhook] [2025-08-05 20:06:58] [adwsapi_v2.php:37]  Calculated signature: sha256=003ecdb400f80c387dc586e72b44fa2438493501cfc80d5944cf7043d0ffb09a
[webhook] [2025-08-05 20:06:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7112c6329b3650f4\n    [X-B3-Traceid] => 6892645f56d21e037c33382307e08a09\n    [B3] => 6892645f56d21e037c33382307e08a09-7112c6329b3650f4-1\n    [Traceparent] => 00-6892645f56d21e037c33382307e08a09-7112c6329b3650f4-01\n    [X-Amzn-Trace-Id] => Root=1-6892645f-56d21e037c33382307e08a09;Parent=7112c6329b3650f4;Sampled=1\n    [X-Adsk-Signature] => sha256=6482bc11e2e06132c1c8f599258357d463f87ad1ee14ce32fb22c0819e5ffddc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754424415837-72359337050211\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:06:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754424415837-72359337050211","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72359337050211","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:06:55.837Z"},"publishedAt":"2025-08-05T20:06:55.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:09:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:09:56
[webhook] [2025-08-05 20:09:56] [adwsapi_v2.php:36]  Provided signature: sha256=694ae2f19218428ac1e83cf7c5f8f1dab2330fd6fb8401c0ee71903e578393fa
[webhook] [2025-08-05 20:09:56] [adwsapi_v2.php:37]  Calculated signature: sha256=7dbf6e568be511c55eb4fe915049d207327290aec4a7a2db3bf0d26a60d51b7a
[webhook] [2025-08-05 20:09:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d3b174890392a92c\n    [X-B3-Traceid] => 689265126ca8b1c562ad1d3532fe343f\n    [B3] => 689265126ca8b1c562ad1d3532fe343f-d3b174890392a92c-1\n    [Traceparent] => 00-689265126ca8b1c562ad1d3532fe343f-d3b174890392a92c-01\n    [X-Amzn-Trace-Id] => Root=1-68926512-6ca8b1c562ad1d3532fe343f;Parent=d3b174890392a92c;Sampled=1\n    [X-Adsk-Signature] => sha256=694ae2f19218428ac1e83cf7c5f8f1dab2330fd6fb8401c0ee71903e578393fa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754424594505-75438658522256\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:09:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754424594505-75438658522256","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75438658522256","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:09:54.505Z"},"publishedAt":"2025-08-05T20:09:54.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:10:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:10:53
[webhook] [2025-08-05 20:10:53] [adwsapi_v2.php:36]  Provided signature: sha256=a9475a94dd2ae980d571996c3ed8c405cfcc2c7be117a7f2ad62471ec793b2c2
[webhook] [2025-08-05 20:10:53] [adwsapi_v2.php:37]  Calculated signature: sha256=718af6e23d4afe3cc1350b1d5569600bb2797e69ca51d4ea61922503e8af3044
[webhook] [2025-08-05 20:10:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 25d0f96cee674985\n    [X-B3-Traceid] => 6892654b39bd6a9d3c9513923ae98b7f\n    [B3] => 6892654b39bd6a9d3c9513923ae98b7f-25d0f96cee674985-1\n    [Traceparent] => 00-6892654b39bd6a9d3c9513923ae98b7f-25d0f96cee674985-01\n    [X-Amzn-Trace-Id] => Root=1-6892654b-39bd6a9d3c9513923ae98b7f;Parent=25d0f96cee674985;Sampled=1\n    [X-Adsk-Signature] => sha256=a9475a94dd2ae980d571996c3ed8c405cfcc2c7be117a7f2ad62471ec793b2c2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754424651700-65995599209008\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:10:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754424651700-65995599209008","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995599209008","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:10:51.700Z"},"publishedAt":"2025-08-05T20:10:51.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:11:28] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:11:28
[webhook] [2025-08-05 20:11:28] [adwsapi_v2.php:36]  Provided signature: sha256=9f4345a47f498b4475eaae10c79650f139d7e4eebde17cbb23277b6aa6a5b59a
[webhook] [2025-08-05 20:11:28] [adwsapi_v2.php:37]  Calculated signature: sha256=fe714b377787c16a1853f46b4ed03c3cd8474596c481adee04e78b3ccb749f49
[webhook] [2025-08-05 20:11:28] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1c24ec2d58120945\n    [X-B3-Traceid] => 6892656e7247e060340fdf610a05b3c8\n    [B3] => 6892656e7247e060340fdf610a05b3c8-1c24ec2d58120945-1\n    [Traceparent] => 00-6892656e7247e060340fdf610a05b3c8-1c24ec2d58120945-01\n    [X-Amzn-Trace-Id] => Root=1-6892656e-7247e060340fdf610a05b3c8;Parent=1c24ec2d58120945;Sampled=1\n    [X-Adsk-Signature] => sha256=9f4345a47f498b4475eaae10c79650f139d7e4eebde17cbb23277b6aa6a5b59a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754424686627-55787120285135\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:11:28] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754424686627-55787120285135","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55787120285135","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:11:26.627Z"},"publishedAt":"2025-08-05T20:11:26.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:15:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:15:22
[webhook] [2025-08-05 20:15:22] [adwsapi_v2.php:36]  Provided signature: sha256=0f8fd6f42244f6a3390495e5b2c22defc722daf16cf871a80163766f05d57060
[webhook] [2025-08-05 20:15:22] [adwsapi_v2.php:37]  Calculated signature: sha256=7cb7bb52153c76a20fed7565e3b22861c2b9434c9ac9d09ac844572a3eb248a2
[webhook] [2025-08-05 20:15:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 016338c6ab15712d\n    [X-B3-Traceid] => 68926658035f7f9b1d421fec26b9e1d6\n    [B3] => 68926658035f7f9b1d421fec26b9e1d6-016338c6ab15712d-1\n    [Traceparent] => 00-68926658035f7f9b1d421fec26b9e1d6-016338c6ab15712d-01\n    [X-Amzn-Trace-Id] => Root=1-68926658-035f7f9b1d421fec26b9e1d6;Parent=016338c6ab15712d;Sampled=1\n    [X-Adsk-Signature] => sha256=0f8fd6f42244f6a3390495e5b2c22defc722daf16cf871a80163766f05d57060\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754424920459-62867699489049\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:15:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754424920459-62867699489049","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62867699489049","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:15:20.459Z"},"publishedAt":"2025-08-05T20:15:20.000Z","csn":"5103159758"}
[webhook] [2025-08-05 20:18:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 20:18:48
[webhook] [2025-08-05 20:18:48] [adwsapi_v2.php:36]  Provided signature: sha256=73373ed7738ad49cd238d651b0c3589142712a4c3287dfdfec38ba5b277ab895
[webhook] [2025-08-05 20:18:48] [adwsapi_v2.php:37]  Calculated signature: sha256=cfae9ecd77c8b3cda04e45df58b4e741061a78ff493ff9e06cb424465f819b49
[webhook] [2025-08-05 20:18:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 47907ab35ca4bef8\n    [X-B3-Traceid] => 689267266d5e8a871870d1f64e70e1a5\n    [B3] => 689267266d5e8a871870d1f64e70e1a5-47907ab35ca4bef8-1\n    [Traceparent] => 00-689267266d5e8a871870d1f64e70e1a5-47907ab35ca4bef8-01\n    [X-Amzn-Trace-Id] => Root=1-68926726-6d5e8a871870d1f64e70e1a5;Parent=47907ab35ca4bef8;Sampled=1\n    [X-Adsk-Signature] => sha256=73373ed7738ad49cd238d651b0c3589142712a4c3287dfdfec38ba5b277ab895\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754425126523-66065226907420\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 20:18:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754425126523-66065226907420","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","paymentStatus":"OPEN","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T20:18:46.523Z"},"publishedAt":"2025-08-05T20:18:46.000Z","csn":"5103159758"}
[webhook] [2025-08-05 22:07:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 22:07:13
[webhook] [2025-08-05 22:07:13] [adwsapi_v2.php:36]  Provided signature: sha256=0e21fbdaae12cfb6fc4a5ce4b5856d37a5076dafafbed1b512fca6a7660deba9
[webhook] [2025-08-05 22:07:13] [adwsapi_v2.php:37]  Calculated signature: sha256=649ef897c1bce4039c018c0ca4b68a59b54385bcb30b1b8aa6f51baca1e3c7a7
[webhook] [2025-08-05 22:07:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fd5662e799e69138\n    [X-B3-Traceid] => 6892808e2f9efcfb1fd6c1b46363959c\n    [B3] => 6892808e2f9efcfb1fd6c1b46363959c-fd5662e799e69138-1\n    [Traceparent] => 00-6892808e2f9efcfb1fd6c1b46363959c-fd5662e799e69138-01\n    [X-Amzn-Trace-Id] => Root=1-6892808e-2f9efcfb1fd6c1b46363959c;Parent=fd5662e799e69138;Sampled=1\n    [X-Adsk-Signature] => sha256=0e21fbdaae12cfb6fc4a5ce4b5856d37a5076dafafbed1b512fca6a7660deba9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754431630646-65995907157636\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 22:07:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754431630646-65995907157636","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995907157636","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T22:07:10.646Z"},"publishedAt":"2025-08-05T22:07:10.000Z","csn":"5103159758"}
[webhook] [2025-08-05 22:08:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-05 22:08:22
[webhook] [2025-08-05 22:08:22] [adwsapi_v2.php:36]  Provided signature: sha256=a5d51d05eb960b51ccdab9bd3f570aae6efd0eb2023c7484932d4459666f2a8d
[webhook] [2025-08-05 22:08:22] [adwsapi_v2.php:37]  Calculated signature: sha256=ba3213c4635548bcd194b899cd83367e9de310498fb7ea385627b308964abad0
[webhook] [2025-08-05 22:08:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 543ac0359c93aaea\n    [X-B3-Traceid] => 689280d3374e3ad875d4c52b6e9310b6\n    [B3] => 689280d3374e3ad875d4c52b6e9310b6-543ac0359c93aaea-1\n    [Traceparent] => 00-689280d3374e3ad875d4c52b6e9310b6-543ac0359c93aaea-01\n    [X-Amzn-Trace-Id] => Root=1-689280d3-374e3ad875d4c52b6e9310b6;Parent=543ac0359c93aaea;Sampled=1\n    [X-Adsk-Signature] => sha256=a5d51d05eb960b51ccdab9bd3f570aae6efd0eb2023c7484932d4459666f2a8d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754431699556-72359337050211\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-05 22:08:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754431699556-72359337050211","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72359337050211","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-05T22:08:19.556Z"},"publishedAt":"2025-08-05T22:08:19.000Z","csn":"5103159758"}
[webhook] [2025-08-06 00:01:01] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 00:01:01
[webhook] [2025-08-06 00:01:01] [adwsapi_v2.php:36]  Provided signature: sha256=25ebcb9481e1a88fe27632fc9741086f51581a6c40c8b808c1258fb05135313d
[webhook] [2025-08-06 00:01:01] [adwsapi_v2.php:37]  Calculated signature: sha256=776b3f46a5b1a64a3359e837c15bed5701c2970177f93abb3a8b23a26e3a4bd9
[webhook] [2025-08-06 00:01:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => de7a7f3a2af95964\n    [X-B3-Traceid] => 68929b3942966ed86699ff10414494fb\n    [B3] => 68929b3942966ed86699ff10414494fb-de7a7f3a2af95964-1\n    [Traceparent] => 00-68929b3942966ed86699ff10414494fb-de7a7f3a2af95964-01\n    [X-Amzn-Trace-Id] => Root=1-68929b39-42966ed86699ff10414494fb;Parent=de7a7f3a2af95964;Sampled=1\n    [X-Adsk-Signature] => sha256=25ebcb9481e1a88fe27632fc9741086f51581a6c40c8b808c1258fb05135313d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6f97e95f-ca28-430d-ad73-810b7e35c98d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 00:01:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"6f97e95f-ca28-430d-ad73-810b7e35c98d","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-06T00:00:58Z"},"publishedAt":"2025-08-06T00:00:59.000Z","country":"GB"}
[webhook] [2025-08-06 00:06:45] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 00:06:45
[webhook] [2025-08-06 00:06:45] [adwsapi_v2.php:36]  Provided signature: sha256=e6e3b76bd6b0557cb45bc401c4632e7f1a4f2e0a0709707a02c01aa950c6d3cd
[webhook] [2025-08-06 00:06:45] [adwsapi_v2.php:37]  Calculated signature: sha256=65443449a3100aa24e6cb366698ec44e816e19bb266bddb4f9fd35522ca73323
[webhook] [2025-08-06 00:06:45] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 764f031474c50c5f\n    [X-B3-Traceid] => 68929c926c1bf12d36134e501da779df\n    [B3] => 68929c926c1bf12d36134e501da779df-764f031474c50c5f-1\n    [Traceparent] => 00-68929c926c1bf12d36134e501da779df-764f031474c50c5f-01\n    [X-Amzn-Trace-Id] => Root=1-68929c92-6c1bf12d36134e501da779df;Parent=764f031474c50c5f;Sampled=1\n    [X-Adsk-Signature] => sha256=e6e3b76bd6b0557cb45bc401c4632e7f1a4f2e0a0709707a02c01aa950c6d3cd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bfc9beb2-9f93-47d9-91b5-2964982d7133\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 00:06:45] [adwsapi_v2.php:57]  Received webhook data: {"id":"bfc9beb2-9f93-47d9-91b5-2964982d7133","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Price_Change","modifiedAt":"2025-08-06T00:06:42Z"},"publishedAt":"2025-08-06T00:06:42.000Z","country":"GB"}
[webhook] [2025-08-06 07:10:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 07:10:47
[webhook] [2025-08-06 07:10:47] [adwsapi_v2.php:36]  Provided signature: sha256=4bf38effbc9008011b4762e377d18673574ff9e5e6b0dc032862844bcea9769c
[webhook] [2025-08-06 07:10:47] [adwsapi_v2.php:37]  Calculated signature: sha256=6fefc8ef0cd61f518f48795b339665abd128584b5d2c0d505547323a7f80760f
[webhook] [2025-08-06 07:10:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 62ead8a303c5eede\n    [X-B3-Traceid] => 6892fff45ce9028c424db8a815d64307\n    [B3] => 6892fff45ce9028c424db8a815d64307-62ead8a303c5eede-1\n    [Traceparent] => 00-6892fff45ce9028c424db8a815d64307-62ead8a303c5eede-01\n    [X-Amzn-Trace-Id] => Root=1-6892fff4-5ce9028c424db8a815d64307;Parent=62ead8a303c5eede;Sampled=1\n    [X-Adsk-Signature] => sha256=4bf38effbc9008011b4762e377d18673574ff9e5e6b0dc032862844bcea9769c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 47800476-58b3-44a0-bc66-784c027bb93f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 07:10:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"47800476-58b3-44a0-bc66-784c027bb93f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74110746359805","status":"Active","message":"subscription status changed.","modifiedAt":"2025-08-06T06:45:42.000+0000"},"publishedAt":"2025-08-06T07:10:44.000Z","csn":"5103159758"}
[webhook] [2025-08-06 08:08:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 08:08:59
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:36]  Provided signature: sha256=465f462330c6f7b4e5abba3e430afdf024ceaeb28f8cc814dd7e354ac63d7b5f
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:37]  Calculated signature: sha256=ced350ef3bbbf09b56e04af5a6be8ef68a542dba5a3cd8ac959905ac87d66914
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1cbf895105169de1\n    [X-B3-Traceid] => 68930d995e7ec3a23270bb41b011ff05\n    [B3] => 68930d995e7ec3a23270bb41b011ff05-1cbf895105169de1-1\n    [Traceparent] => 00-68930d995e7ec3a23270bb41b011ff05-1cbf895105169de1-01\n    [X-Amzn-Trace-Id] => Root=1-68930d99-5e7ec3a23270bb41b011ff05;Parent=1cbf895105169de1;Sampled=1\n    [X-Adsk-Signature] => sha256=465f462330c6f7b4e5abba3e430afdf024ceaeb28f8cc814dd7e354ac63d7b5f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8d4dea33-3a27-4579-9c2d-87ec38ef79da\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"8d4dea33-3a27-4579-9c2d-87ec38ef79da","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983134","transactionId":"24ae45d0-faee-5810-a445-24db468cce3d","quoteStatus":"Order Submitted","message":"Quote# Q-983134 status changed to Order Submitted.","modifiedAt":"2025-08-06T08:08:57.197Z"},"publishedAt":"2025-08-06T08:08:57.000Z","csn":"5103159758"}
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 08:09:00
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:36]  Provided signature: sha256=d857aed51d27abdbba16c68ddfe59fdb00658361bb6c8fab71f01cd569e710d3
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:37]  Calculated signature: sha256=d1e8abbe32d169c9af127f3715bd8022f052ea482c09468dd0b017bc4a190664
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ab58bd6282ae3e47\n    [X-B3-Traceid] => 68930d9ab934cc5e4fc071c0cc744d9d\n    [B3] => 68930d9ab934cc5e4fc071c0cc744d9d-ab58bd6282ae3e47-1\n    [Traceparent] => 00-68930d9ab934cc5e4fc071c0cc744d9d-ab58bd6282ae3e47-01\n    [X-Amzn-Trace-Id] => Root=1-68930d9a-b934cc5e4fc071c0cc744d9d;Parent=ab58bd6282ae3e47;Sampled=1\n    [X-Adsk-Signature] => sha256=d857aed51d27abdbba16c68ddfe59fdb00658361bb6c8fab71f01cd569e710d3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8ddd3324-5463-42ba-828d-2d7f467dda49\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 08:09:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"8ddd3324-5463-42ba-828d-2d7f467dda49","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-983134","transactionId":"24ae45d0-faee-5810-a445-24db468cce3d","quoteStatus":"Ordered","message":"Quote# Q-983134 status changed to Ordered.","modifiedAt":"2025-08-06T08:08:58.438Z"},"publishedAt":"2025-08-06T08:08:58.000Z","csn":"5103159758"}
[webhook] [2025-08-06 08:39:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 08:39:11
[webhook] [2025-08-06 08:39:11] [adwsapi_v2.php:36]  Provided signature: sha256=e5c38e8de3b8d7f3b00415ca5992aee4af078c25c13be7856346d0608c0a1388
[webhook] [2025-08-06 08:39:11] [adwsapi_v2.php:37]  Calculated signature: sha256=47022ce9a2f90124b805992c059f19178c49bf523779b13e865ee241dd5c60e6
[webhook] [2025-08-06 08:39:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ae1da86eca24b69a\n    [X-B3-Traceid] => 689314ac658ace8e313a7c0a08e46f2a\n    [B3] => 689314ac658ace8e313a7c0a08e46f2a-ae1da86eca24b69a-1\n    [Traceparent] => 00-689314ac658ace8e313a7c0a08e46f2a-ae1da86eca24b69a-01\n    [X-Amzn-Trace-Id] => Root=1-689314ac-658ace8e313a7c0a08e46f2a;Parent=ae1da86eca24b69a;Sampled=1\n    [X-Adsk-Signature] => sha256=e5c38e8de3b8d7f3b00415ca5992aee4af078c25c13be7856346d0608c0a1388\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2152a6b9-96e8-438a-9e79-d51ab0477d6f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 08:39:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"2152a6b9-96e8-438a-9e79-d51ab0477d6f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995913110554","status":"Active","quantity":1,"endDate":"2026-08-07","term":"Annual","message":"subscription status,quantity,endDate,term changed.","modifiedAt":"2025-08-06T08:09:06.000+0000"},"publishedAt":"2025-08-06T08:39:08.000Z","csn":"5103159758"}
[webhook] [2025-08-06 08:40:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 08:40:56
[webhook] [2025-08-06 08:40:56] [adwsapi_v2.php:36]  Provided signature: sha256=416c141bfcdf9475baf56432e405d9ba1a93af96c5271d04cef7b34004ba0bba
[webhook] [2025-08-06 08:40:56] [adwsapi_v2.php:37]  Calculated signature: sha256=9e1910cd5e8b4c6e7076db5c5a04e21e706eeb4a38358d61fc54f3a40bb32b5f
[webhook] [2025-08-06 08:40:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0ceb9554f0d1509f\n    [X-B3-Traceid] => 689315157b3598af1dd9afe6438e591e\n    [B3] => 689315157b3598af1dd9afe6438e591e-0ceb9554f0d1509f-1\n    [Traceparent] => 00-689315157b3598af1dd9afe6438e591e-0ceb9554f0d1509f-01\n    [X-Amzn-Trace-Id] => Root=1-68931515-7b3598af1dd9afe6438e591e;Parent=0ceb9554f0d1509f;Sampled=1\n    [X-Adsk-Signature] => sha256=416c141bfcdf9475baf56432e405d9ba1a93af96c5271d04cef7b34004ba0bba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b9ed0d8b-52fd-48b6-ba66-79027f9a22ec\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 08:40:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"b9ed0d8b-52fd-48b6-ba66-79027f9a22ec","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995913110554","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-08-06T08:15:51.000+0000"},"publishedAt":"2025-08-06T08:40:53.000Z","csn":"5103159758"}
[webhook] [2025-08-06 09:03:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 09:03:40
[webhook] [2025-08-06 09:03:40] [adwsapi_v2.php:36]  Provided signature: sha256=dd3baaa06e2b372720e9b97fbf42edc9a8afa0ead8c34b22720ec19511a424ef
[webhook] [2025-08-06 09:03:40] [adwsapi_v2.php:37]  Calculated signature: sha256=2261e4976fb65d11412b6183d4d724683dcae8bd69c1f55ecbf31f8525ff694e
[webhook] [2025-08-06 09:03:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 24ff7897f64db406\n    [X-B3-Traceid] => 68931a695e2dd2b3cd6c7eceb38ab9d9\n    [B3] => 68931a695e2dd2b3cd6c7eceb38ab9d9-24ff7897f64db406-1\n    [Traceparent] => 00-68931a695e2dd2b3cd6c7eceb38ab9d9-24ff7897f64db406-01\n    [X-Amzn-Trace-Id] => Root=1-68931a69-5e2dd2b3cd6c7eceb38ab9d9;Parent=24ff7897f64db406;Sampled=1\n    [X-Adsk-Signature] => sha256=dd3baaa06e2b372720e9b97fbf42edc9a8afa0ead8c34b22720ec19511a424ef\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e2bb6ea7-fa09-4fce-b51c-d4a6fca1795c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 09:03:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"e2bb6ea7-fa09-4fce-b51c-d4a6fca1795c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979595","transactionId":"d03ca35c-709a-51b8-aff1-c2561f346a39","quoteStatus":"Order Submitted","message":"Quote# Q-979595 status changed to Order Submitted.","modifiedAt":"2025-08-06T09:03:37.406Z"},"publishedAt":"2025-08-06T09:03:37.000Z","csn":"5103159758"}
[webhook] [2025-08-06 09:03:42] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 09:03:42
[webhook] [2025-08-06 09:03:42] [adwsapi_v2.php:36]  Provided signature: sha256=6aea4aa116238a39cd3e0a929ba4d5cfccc647c2d85be4c566a37dc034537771
[webhook] [2025-08-06 09:03:42] [adwsapi_v2.php:37]  Calculated signature: sha256=3d74f3be10c1c3b340d1e341e08111ec3a0585504c8795e8fc518fca3732f4df
[webhook] [2025-08-06 09:03:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c48754078c220317\n    [X-B3-Traceid] => 68931a6c1e2c58bff36e796200a067cf\n    [B3] => 68931a6c1e2c58bff36e796200a067cf-c48754078c220317-1\n    [Traceparent] => 00-68931a6c1e2c58bff36e796200a067cf-c48754078c220317-01\n    [X-Amzn-Trace-Id] => Root=1-68931a6c-1e2c58bff36e796200a067cf;Parent=c48754078c220317;Sampled=1\n    [X-Adsk-Signature] => sha256=6aea4aa116238a39cd3e0a929ba4d5cfccc647c2d85be4c566a37dc034537771\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 73202249-8d9d-43f9-8869-50aa098c6ec3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 09:03:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"73202249-8d9d-43f9-8869-50aa098c6ec3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979595","transactionId":"d03ca35c-709a-51b8-aff1-c2561f346a39","quoteStatus":"Ordered","message":"Quote# Q-979595 status changed to Ordered.","modifiedAt":"2025-08-06T09:03:40.168Z"},"publishedAt":"2025-08-06T09:03:40.000Z","csn":"5103159758"}
[webhook] [2025-08-06 09:16:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 09:16:50
[webhook] [2025-08-06 09:16:50] [adwsapi_v2.php:36]  Provided signature: sha256=59e8afa6c083ecf2ab7f71d8ad02ce1bb06af33fbf9345b7299b88392544538a
[webhook] [2025-08-06 09:16:50] [adwsapi_v2.php:37]  Calculated signature: sha256=ecc99fd34ce69aff43c6dca1ac6f9821e5a973abfdadf0caf4477e2d1fa6a371
[webhook] [2025-08-06 09:16:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a0ff8824abcb7d25\n    [X-B3-Traceid] => 68931d7fb9f9a9682f3be3f050744b0a\n    [B3] => 68931d7fb9f9a9682f3be3f050744b0a-a0ff8824abcb7d25-1\n    [Traceparent] => 00-68931d7fb9f9a9682f3be3f050744b0a-a0ff8824abcb7d25-01\n    [X-Amzn-Trace-Id] => Root=1-68931d7f-b9f9a9682f3be3f050744b0a;Parent=a0ff8824abcb7d25;Sampled=1\n    [X-Adsk-Signature] => sha256=59e8afa6c083ecf2ab7f71d8ad02ce1bb06af33fbf9345b7299b88392544538a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 82722048-701e-4345-bee9-61dd23d8a4b5\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 09:16:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"82722048-701e-4345-bee9-61dd23d8a4b5","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-986742","transactionId":"b9f568f8-b50f-5168-996b-09f5aabb7b2e","quoteStatus":"Draft","message":"Quote# Q-986742 status changed to Draft.","modifiedAt":"2025-08-06T09:16:47.602Z"},"publishedAt":"2025-08-06T09:16:47.000Z","csn":"5103159758"}
[webhook] [2025-08-06 09:17:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 09:17:53
[webhook] [2025-08-06 09:17:53] [adwsapi_v2.php:36]  Provided signature: sha256=63f367dfaf41c9ec19d31fa638e72f0469c01a4ed31c5e1d3669350df2dc7bcd
[webhook] [2025-08-06 09:17:53] [adwsapi_v2.php:37]  Calculated signature: sha256=cc92b1c108e64209bea64785b6ca90d023de19b010c8893a4f0ad6d90d3055ab
[webhook] [2025-08-06 09:17:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 92da260aa1ea4eb6\n    [X-B3-Traceid] => 68931dbff1e70396a4086444a8fa42eb\n    [B3] => 68931dbff1e70396a4086444a8fa42eb-92da260aa1ea4eb6-1\n    [Traceparent] => 00-68931dbff1e70396a4086444a8fa42eb-92da260aa1ea4eb6-01\n    [X-Amzn-Trace-Id] => Root=1-68931dbf-f1e70396a4086444a8fa42eb;Parent=92da260aa1ea4eb6;Sampled=1\n    [X-Adsk-Signature] => sha256=63f367dfaf41c9ec19d31fa638e72f0469c01a4ed31c5e1d3669350df2dc7bcd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2965162c-488a-4fda-9f68-029e5d965291\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 09:17:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"2965162c-488a-4fda-9f68-029e5d965291","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-986742","transactionId":"b9f568f8-b50f-5168-996b-09f5aabb7b2e","quoteStatus":"Quoted","message":"Quote# Q-986742 status changed to Quoted.","modifiedAt":"2025-08-06T09:17:50.787Z"},"publishedAt":"2025-08-06T09:17:51.000Z","csn":"5103159758"}
[webhook] [2025-08-06 09:38:52] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 09:38:52
[webhook] [2025-08-06 09:38:52] [adwsapi_v2.php:36]  Provided signature: sha256=c8a96800ea093236370c9f038e6b5e0e285bb6c3cd8f2eca64a545e4ddc39f2f
[webhook] [2025-08-06 09:38:52] [adwsapi_v2.php:37]  Calculated signature: sha256=7b88b16742b4737e35ee000f7b04906472f33a739b2d7cad1e87dab3324d94a5
[webhook] [2025-08-06 09:38:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 38c5f939b0ae2f1d\n    [X-B3-Traceid] => 689322a94fdf0bee7423b1d45fb06130\n    [B3] => 689322a94fdf0bee7423b1d45fb06130-38c5f939b0ae2f1d-1\n    [Traceparent] => 00-689322a94fdf0bee7423b1d45fb06130-38c5f939b0ae2f1d-01\n    [X-Amzn-Trace-Id] => Root=1-689322a9-4fdf0bee7423b1d45fb06130;Parent=38c5f939b0ae2f1d;Sampled=1\n    [X-Adsk-Signature] => sha256=c8a96800ea093236370c9f038e6b5e0e285bb6c3cd8f2eca64a545e4ddc39f2f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6f433523-20e5-42cd-9ea7-ae02154b04cb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 09:38:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"6f433523-20e5-42cd-9ea7-ae02154b04cb","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527238809770","status":"Active","quantity":1,"endDate":"2026-09-01","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-06T09:03:46.000+0000"},"publishedAt":"2025-08-06T09:38:49.000Z","csn":"5103159758"}
[webhook] [2025-08-06 11:24:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 11:24:07
[webhook] [2025-08-06 11:24:07] [adwsapi_v2.php:36]  Provided signature: sha256=dc6357fa3717f42e217beb1e6888e676121ca5f253b003dd85e9f74321bf14d3
[webhook] [2025-08-06 11:24:07] [adwsapi_v2.php:37]  Calculated signature: sha256=15bb450d725c37d4480e177cd9e6d9803e6f15e4a12254c02fb1d1eeabaeb301
[webhook] [2025-08-06 11:24:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 495c67309ad236e6\n    [X-B3-Traceid] => 68933b5492987b109f89d1cb8ac28d67\n    [B3] => 68933b5492987b109f89d1cb8ac28d67-495c67309ad236e6-1\n    [Traceparent] => 00-68933b5492987b109f89d1cb8ac28d67-495c67309ad236e6-01\n    [X-Amzn-Trace-Id] => Root=1-68933b54-92987b109f89d1cb8ac28d67;Parent=495c67309ad236e6;Sampled=1\n    [X-Adsk-Signature] => sha256=dc6357fa3717f42e217beb1e6888e676121ca5f253b003dd85e9f74321bf14d3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 17723b3d-2100-4d5c-a224-1aad0667d203\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 11:24:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"17723b3d-2100-4d5c-a224-1aad0667d203","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987136","transactionId":"9288c7d2-510a-525a-add1-a6df6c8f0b58","quoteStatus":"Draft","message":"Quote# Q-987136 status changed to Draft.","modifiedAt":"2025-08-06T11:24:03.873Z"},"publishedAt":"2025-08-06T11:24:04.000Z","csn":"5103159758"}
[webhook] [2025-08-06 11:25:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 11:25:04
[webhook] [2025-08-06 11:25:04] [adwsapi_v2.php:36]  Provided signature: sha256=373bff18107018a844085357dd449833b04ab660e1561eeb54606cf630ea5b5e
[webhook] [2025-08-06 11:25:04] [adwsapi_v2.php:37]  Calculated signature: sha256=d98f414cefaa13ec34e939a44cf2c00d1e38942aabfef38a86a789b2752d908f
[webhook] [2025-08-06 11:25:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0a86ea2844af6b65\n    [X-B3-Traceid] => 68933b8d55cc5619238c7ec5a3debd85\n    [B3] => 68933b8d55cc5619238c7ec5a3debd85-0a86ea2844af6b65-1\n    [Traceparent] => 00-68933b8d55cc5619238c7ec5a3debd85-0a86ea2844af6b65-01\n    [X-Amzn-Trace-Id] => Root=1-68933b8d-55cc5619238c7ec5a3debd85;Parent=0a86ea2844af6b65;Sampled=1\n    [X-Adsk-Signature] => sha256=373bff18107018a844085357dd449833b04ab660e1561eeb54606cf630ea5b5e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ced0ce94-95d1-4d4f-a4a4-733191f88637\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 11:25:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"ced0ce94-95d1-4d4f-a4a4-733191f88637","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987136","transactionId":"9288c7d2-510a-525a-add1-a6df6c8f0b58","quoteStatus":"Quoted","message":"Quote# Q-987136 status changed to Quoted.","modifiedAt":"2025-08-06T11:25:01.206Z"},"publishedAt":"2025-08-06T11:25:01.000Z","csn":"5103159758"}
[webhook] [2025-08-06 11:37:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 11:37:05
[webhook] [2025-08-06 11:37:05] [adwsapi_v2.php:36]  Provided signature: sha256=874a2cc63060f4f13e4ea96681c9a7c4c99281f6695acffd9ee924c1850a56aa
[webhook] [2025-08-06 11:37:05] [adwsapi_v2.php:37]  Calculated signature: sha256=66013129e5d256bbe4ddde9a8adf8c3e1b3e8ca71e31a5ff0c14016a6c86c268
[webhook] [2025-08-06 11:37:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 17709a0604f99405\n    [X-B3-Traceid] => 68933e5e294af8f6356e59c95da1b42e\n    [B3] => 68933e5e294af8f6356e59c95da1b42e-17709a0604f99405-1\n    [Traceparent] => 00-68933e5e294af8f6356e59c95da1b42e-17709a0604f99405-01\n    [X-Amzn-Trace-Id] => Root=1-68933e5e-294af8f6356e59c95da1b42e;Parent=17709a0604f99405;Sampled=1\n    [X-Adsk-Signature] => sha256=874a2cc63060f4f13e4ea96681c9a7c4c99281f6695acffd9ee924c1850a56aa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8fa2163f-b159-4780-8114-2874957e85b9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 11:37:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"8fa2163f-b159-4780-8114-2874957e85b9","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995913110554","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-06T11:11:54.000+0000"},"publishedAt":"2025-08-06T11:37:03.000Z","csn":"5103159758"}
[webhook] [2025-08-06 11:38:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 11:38:33
[webhook] [2025-08-06 11:38:33] [adwsapi_v2.php:36]  Provided signature: sha256=b85bce315028f9979d8673c1106203adb60e40f84a40e01d5035778e7911f455
[webhook] [2025-08-06 11:38:33] [adwsapi_v2.php:37]  Calculated signature: sha256=4d04458733340b8f5d58ef3446c9426b8a54fc2ea4803f8018446b61062891dc
[webhook] [2025-08-06 11:38:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a09bfbe7c3d3a36a\n    [X-B3-Traceid] => 68933eb774405b960117dbcc578f25cc\n    [B3] => 68933eb774405b960117dbcc578f25cc-a09bfbe7c3d3a36a-1\n    [Traceparent] => 00-68933eb774405b960117dbcc578f25cc-a09bfbe7c3d3a36a-01\n    [X-Amzn-Trace-Id] => Root=1-68933eb7-74405b960117dbcc578f25cc;Parent=a09bfbe7c3d3a36a;Sampled=1\n    [X-Adsk-Signature] => sha256=b85bce315028f9979d8673c1106203adb60e40f84a40e01d5035778e7911f455\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 96184633-a33f-4af5-abc1-e3d81404b848\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 11:38:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"96184633-a33f-4af5-abc1-e3d81404b848","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527238809770","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-06T11:13:24.000+0000"},"publishedAt":"2025-08-06T11:38:31.000Z","csn":"5103159758"}
[webhook] [2025-08-06 11:43:57] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 11:43:57
[webhook] [2025-08-06 11:43:57] [adwsapi_v2.php:36]  Provided signature: sha256=0641470376d855375c31621558f950b219a8cd7f5a7a936e49eb23f0fc1a9768
[webhook] [2025-08-06 11:43:57] [adwsapi_v2.php:37]  Calculated signature: sha256=e1a4a06d44d4f96ccf674d294be35341e10b3eabfdd9f845e1634bf7d8e64cb7
[webhook] [2025-08-06 11:43:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4a863e10d19e4e05\n    [X-B3-Traceid] => 68933ffb6397499222bf1c8308917c1e\n    [B3] => 68933ffb6397499222bf1c8308917c1e-4a863e10d19e4e05-1\n    [Traceparent] => 00-68933ffb6397499222bf1c8308917c1e-4a863e10d19e4e05-01\n    [X-Amzn-Trace-Id] => Root=1-68933ffb-6397499222bf1c8308917c1e;Parent=4a863e10d19e4e05;Sampled=1\n    [X-Adsk-Signature] => sha256=0641470376d855375c31621558f950b219a8cd7f5a7a936e49eb23f0fc1a9768\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => b4e4f7a9-461d-42e8-bba5-3aa0c4e3e98a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 11:43:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"b4e4f7a9-461d-42e8-bba5-3aa0c4e3e98a","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73583050280801","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-06T11:28:53.000+0000"},"publishedAt":"2025-08-06T11:43:55.000Z","csn":"5103159758"}
[webhook] [2025-08-06 12:16:27] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 12:16:27
[webhook] [2025-08-06 12:16:27] [adwsapi_v2.php:36]  Provided signature: sha256=1a2b76d5029f3cda70daebda3ec23fe97cb3b2b914f2b099e41d24570508122b
[webhook] [2025-08-06 12:16:27] [adwsapi_v2.php:37]  Calculated signature: sha256=466bc400a292d87d833500af8967414e0867aaf6066d2aad0a7b92128b5e7a7b
[webhook] [2025-08-06 12:16:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c2ef27120cc87fe4\n    [X-B3-Traceid] => 6893479926a1acea10b1e5494c153c78\n    [B3] => 6893479926a1acea10b1e5494c153c78-c2ef27120cc87fe4-1\n    [Traceparent] => 00-6893479926a1acea10b1e5494c153c78-c2ef27120cc87fe4-01\n    [X-Amzn-Trace-Id] => Root=1-68934799-26a1acea10b1e5494c153c78;Parent=c2ef27120cc87fe4;Sampled=1\n    [X-Adsk-Signature] => sha256=1a2b76d5029f3cda70daebda3ec23fe97cb3b2b914f2b099e41d24570508122b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754482585203-74110746359805\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 12:16:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754482585203-74110746359805","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74110746359805","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T12:16:25.203Z"},"publishedAt":"2025-08-06T12:16:25.000Z","csn":"5103159758"}
[webhook] [2025-08-06 12:28:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 12:28:14
[webhook] [2025-08-06 12:28:14] [adwsapi_v2.php:36]  Provided signature: sha256=416fea692bffc40a1a6fd93c6d21341f590888a9a4fb7cf96821b25316968a40
[webhook] [2025-08-06 12:28:14] [adwsapi_v2.php:37]  Calculated signature: sha256=92764b0ea6882b5369fc492cda183f242369432f8d3f9bcf5b46e16afc896054
[webhook] [2025-08-06 12:28:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f1de44fdb89730f7\n    [X-B3-Traceid] => 68934a5c931c6982f1f0a37413c717ed\n    [B3] => 68934a5c931c6982f1f0a37413c717ed-f1de44fdb89730f7-1\n    [Traceparent] => 00-68934a5c931c6982f1f0a37413c717ed-f1de44fdb89730f7-01\n    [X-Amzn-Trace-Id] => Root=1-68934a5c-931c6982f1f0a37413c717ed;Parent=f1de44fdb89730f7;Sampled=1\n    [X-Adsk-Signature] => sha256=416fea692bffc40a1a6fd93c6d21341f590888a9a4fb7cf96821b25316968a40\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e2ee709e-93ee-4b9a-9904-9fc26d7a0ddd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 12:28:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"e2ee709e-93ee-4b9a-9904-9fc26d7a0ddd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987314","transactionId":"83b1eff0-52c4-5e05-9101-8efd1e4b7adf","quoteStatus":"Draft","message":"Quote# Q-987314 status changed to Draft.","modifiedAt":"2025-08-06T12:28:11.980Z"},"publishedAt":"2025-08-06T12:28:12.000Z","csn":"5103159758"}
[webhook] [2025-08-06 12:34:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 12:34:20
[webhook] [2025-08-06 12:34:20] [adwsapi_v2.php:36]  Provided signature: sha256=b95da7ef3c5b0b1c60ac8beaa24c44b2caf7fc8fc823172f6d73bb364a12952d
[webhook] [2025-08-06 12:34:20] [adwsapi_v2.php:37]  Calculated signature: sha256=0c7e47ba3519f9c704c8219594df62cc9fa5ef191c613301712d4ff4b90e234c
[webhook] [2025-08-06 12:34:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 62bcfe10a0240329\n    [X-B3-Traceid] => 68934bc9e5574d835beac927d7060818\n    [B3] => 68934bc9e5574d835beac927d7060818-62bcfe10a0240329-1\n    [Traceparent] => 00-68934bc9e5574d835beac927d7060818-62bcfe10a0240329-01\n    [X-Amzn-Trace-Id] => Root=1-68934bc9-e5574d835beac927d7060818;Parent=62bcfe10a0240329;Sampled=1\n    [X-Adsk-Signature] => sha256=b95da7ef3c5b0b1c60ac8beaa24c44b2caf7fc8fc823172f6d73bb364a12952d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e66b3362-e1c4-4ae7-bfe8-b6bd14f09dc2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 12:34:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"e66b3362-e1c4-4ae7-bfe8-b6bd14f09dc2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987314","transactionId":"83b1eff0-52c4-5e05-9101-8efd1e4b7adf","quoteStatus":"Cancelled","message":"Quote# Q-987314 status changed to Cancelled.","modifiedAt":"2025-08-06T12:34:17.529Z"},"publishedAt":"2025-08-06T12:34:17.000Z","csn":"5103159758"}
[webhook] [2025-08-06 14:18:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 14:18:02
[webhook] [2025-08-06 14:18:02] [adwsapi_v2.php:36]  Provided signature: sha256=0a8b9f14b51def5b2764d2117b80ab83e40575e4288ec0bacd1e2013c8bd7e2e
[webhook] [2025-08-06 14:18:02] [adwsapi_v2.php:37]  Calculated signature: sha256=23c6b4e7499549c320d8ddf47187bde2dbb224babcfd27b1ef09f0901e6000de
[webhook] [2025-08-06 14:18:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e3a9f2ee45dcd412\n    [X-B3-Traceid] => 68936417255b77dbea6d0899102bb1c0\n    [B3] => 68936417255b77dbea6d0899102bb1c0-e3a9f2ee45dcd412-1\n    [Traceparent] => 00-68936417255b77dbea6d0899102bb1c0-e3a9f2ee45dcd412-01\n    [X-Amzn-Trace-Id] => Root=1-68936417-255b77dbea6d0899102bb1c0;Parent=e3a9f2ee45dcd412;Sampled=1\n    [X-Adsk-Signature] => sha256=0a8b9f14b51def5b2764d2117b80ab83e40575e4288ec0bacd1e2013c8bd7e2e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d81376d4-4884-47fe-bc1c-787fd657d543\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 14:18:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"d81376d4-4884-47fe-bc1c-787fd657d543","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987799","transactionId":"56838d5d-99a5-5236-95ee-ec50ffef22ff","quoteStatus":"Draft","message":"Quote# Q-987799 status changed to Draft.","modifiedAt":"2025-08-06T14:17:59.288Z"},"publishedAt":"2025-08-06T14:17:59.000Z","csn":"5103159758"}
[webhook] [2025-08-06 14:18:44] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 14:18:44
[webhook] [2025-08-06 14:18:44] [adwsapi_v2.php:36]  Provided signature: sha256=53fe2047d007b16a533c2007b42d4a6c563a3b1c3edf782a80cde5763332919f
[webhook] [2025-08-06 14:18:44] [adwsapi_v2.php:37]  Calculated signature: sha256=4d9b267327803e6c4fbbe582f4f7e627812a54db5713d20cb9aa3c55487504f3
[webhook] [2025-08-06 14:18:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6dea05d5bb3dd15b\n    [X-B3-Traceid] => 6893644276ac1317191b80eb55b54bd9\n    [B3] => 6893644276ac1317191b80eb55b54bd9-6dea05d5bb3dd15b-1\n    [Traceparent] => 00-6893644276ac1317191b80eb55b54bd9-6dea05d5bb3dd15b-01\n    [X-Amzn-Trace-Id] => Root=1-68936442-76ac1317191b80eb55b54bd9;Parent=6dea05d5bb3dd15b;Sampled=1\n    [X-Adsk-Signature] => sha256=53fe2047d007b16a533c2007b42d4a6c563a3b1c3edf782a80cde5763332919f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 17e92300-5efe-4b0b-ab84-9ba93f4a1b84\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 14:18:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"17e92300-5efe-4b0b-ab84-9ba93f4a1b84","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987799","transactionId":"56838d5d-99a5-5236-95ee-ec50ffef22ff","quoteStatus":"Quoted","message":"Quote# Q-987799 status changed to Quoted.","modifiedAt":"2025-08-06T14:18:41.796Z"},"publishedAt":"2025-08-06T14:18:42.000Z","csn":"5103159758"}
[webhook] [2025-08-06 14:53:46] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 14:53:46
[webhook] [2025-08-06 14:53:46] [adwsapi_v2.php:36]  Provided signature: sha256=027d439de0e21c2f872b2bcd192d17dc3d5c5cc60b46db58e755a54c41056622
[webhook] [2025-08-06 14:53:46] [adwsapi_v2.php:37]  Calculated signature: sha256=de7f83b812f89754ae9e82e280b4e8b949d99889b24f3330df67b9aac2cef2ab
[webhook] [2025-08-06 14:53:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ac8e2df28ce103c7\n    [X-B3-Traceid] => 68936c781fc2f28fd3cef3386d298a5a\n    [B3] => 68936c781fc2f28fd3cef3386d298a5a-ac8e2df28ce103c7-1\n    [Traceparent] => 00-68936c781fc2f28fd3cef3386d298a5a-ac8e2df28ce103c7-01\n    [X-Amzn-Trace-Id] => Root=1-68936c78-1fc2f28fd3cef3386d298a5a;Parent=ac8e2df28ce103c7;Sampled=1\n    [X-Adsk-Signature] => sha256=027d439de0e21c2f872b2bcd192d17dc3d5c5cc60b46db58e755a54c41056622\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8fc963bb-c9f4-46b2-a6fc-788ccc83c79d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 14:53:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"8fc963bb-c9f4-46b2-a6fc-788ccc83c79d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987942","transactionId":"c8a9097e-03b7-5c60-9732-68736d04551f","quoteStatus":"Draft","message":"Quote# Q-987942 status changed to Draft.","modifiedAt":"2025-08-06T14:53:43.873Z"},"publishedAt":"2025-08-06T14:53:44.000Z","csn":"5103159758"}
[webhook] [2025-08-06 14:54:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 14:54:47
[webhook] [2025-08-06 14:54:47] [adwsapi_v2.php:36]  Provided signature: sha256=7e6323c1c4da6551e2a0d30b071a6f3f759174fccb6eba3f7266cdf68c652fb6
[webhook] [2025-08-06 14:54:47] [adwsapi_v2.php:37]  Calculated signature: sha256=4f2ab57b0e74fee61881c2e1585ab1f3848dcc9971709766500787532110711a
[webhook] [2025-08-06 14:54:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 24a3d3abac924572\n    [X-B3-Traceid] => 68936cb4bd783a66a3e7f3cfc6c5a044\n    [B3] => 68936cb4bd783a66a3e7f3cfc6c5a044-24a3d3abac924572-1\n    [Traceparent] => 00-68936cb4bd783a66a3e7f3cfc6c5a044-24a3d3abac924572-01\n    [X-Amzn-Trace-Id] => Root=1-68936cb4-bd783a66a3e7f3cfc6c5a044;Parent=24a3d3abac924572;Sampled=1\n    [X-Adsk-Signature] => sha256=7e6323c1c4da6551e2a0d30b071a6f3f759174fccb6eba3f7266cdf68c652fb6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1f7abdb4-4733-4e08-8f2d-917b785f7a77\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 14:54:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"1f7abdb4-4733-4e08-8f2d-917b785f7a77","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987942","transactionId":"c8a9097e-03b7-5c60-9732-68736d04551f","quoteStatus":"Quoted","message":"Quote# Q-987942 status changed to Quoted.","modifiedAt":"2025-08-06T14:54:44.346Z"},"publishedAt":"2025-08-06T14:54:44.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:01:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:01:36
[webhook] [2025-08-06 15:01:36] [adwsapi_v2.php:36]  Provided signature: sha256=9ee1dfb7c95b373fd69b8b2f16399c0a6049e87676f8116349230f0bf633629f
[webhook] [2025-08-06 15:01:36] [adwsapi_v2.php:37]  Calculated signature: sha256=692d2aeac1f78e6cc7ab5ce6e250b995f96891d585afc9922efb87d76f3b7f8e
[webhook] [2025-08-06 15:01:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 40dead7f19a188d9\n    [X-B3-Traceid] => 68936e4e1096e3d34f9bd5d9beaff92a\n    [B3] => 68936e4e1096e3d34f9bd5d9beaff92a-40dead7f19a188d9-1\n    [Traceparent] => 00-68936e4e1096e3d34f9bd5d9beaff92a-40dead7f19a188d9-01\n    [X-Amzn-Trace-Id] => Root=1-68936e4e-1096e3d34f9bd5d9beaff92a;Parent=40dead7f19a188d9;Sampled=1\n    [X-Adsk-Signature] => sha256=9ee1dfb7c95b373fd69b8b2f16399c0a6049e87676f8116349230f0bf633629f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 60d4d303-e1df-4208-a8a0-8d29367db132\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:01:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"60d4d303-e1df-4208-a8a0-8d29367db132","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987965","transactionId":"2f1db06e-d9fb-5bab-8d5c-d163a9803d96","quoteStatus":"Draft","message":"Quote# Q-987965 status changed to Draft.","modifiedAt":"2025-08-06T15:01:33.996Z"},"publishedAt":"2025-08-06T15:01:34.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:02:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:02:39
[webhook] [2025-08-06 15:02:39] [adwsapi_v2.php:36]  Provided signature: sha256=026e90ece6e791e9abc235e9b73233fa778923e7a1941ca963b0fc87a595a110
[webhook] [2025-08-06 15:02:39] [adwsapi_v2.php:37]  Calculated signature: sha256=b43131d015b53eed591fcc39bee61b00e02dce8f07a9e5c79a47fe1c52dd2085
[webhook] [2025-08-06 15:02:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c259ae4eb30023c4\n    [X-B3-Traceid] => 68936e8c0b16fc89d7c2deffd29b4628\n    [B3] => 68936e8c0b16fc89d7c2deffd29b4628-c259ae4eb30023c4-1\n    [Traceparent] => 00-68936e8c0b16fc89d7c2deffd29b4628-c259ae4eb30023c4-01\n    [X-Amzn-Trace-Id] => Root=1-68936e8c-0b16fc89d7c2deffd29b4628;Parent=c259ae4eb30023c4;Sampled=1\n    [X-Adsk-Signature] => sha256=026e90ece6e791e9abc235e9b73233fa778923e7a1941ca963b0fc87a595a110\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2c7e1f3f-dfe3-40d0-ad4b-289df90a5127\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:02:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"2c7e1f3f-dfe3-40d0-ad4b-289df90a5127","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979458","transactionId":"99fefe58-8450-5740-bc64-9f52eee08dc4","quoteStatus":"Order Submitted","message":"Quote# Q-979458 status changed to Order Submitted.","modifiedAt":"2025-08-06T15:02:36.207Z"},"publishedAt":"2025-08-06T15:02:36.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:02:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:02:58
[webhook] [2025-08-06 15:02:58] [adwsapi_v2.php:36]  Provided signature: sha256=0541fc27341b037c76da829e4ad54f192b9eec9020b4e118c9a97dfebe65f29a
[webhook] [2025-08-06 15:02:58] [adwsapi_v2.php:37]  Calculated signature: sha256=7d9600305b26409c3fb4c6af73b6531d57599e4b5bd3210e489d9ddc6c936f02
[webhook] [2025-08-06 15:02:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 79e4610633c28cee\n    [X-B3-Traceid] => 68936ea0b90dc267d58fac7fbcba8c4b\n    [B3] => 68936ea0b90dc267d58fac7fbcba8c4b-79e4610633c28cee-1\n    [Traceparent] => 00-68936ea0b90dc267d58fac7fbcba8c4b-79e4610633c28cee-01\n    [X-Amzn-Trace-Id] => Root=1-68936ea0-b90dc267d58fac7fbcba8c4b;Parent=79e4610633c28cee;Sampled=1\n    [X-Adsk-Signature] => sha256=0541fc27341b037c76da829e4ad54f192b9eec9020b4e118c9a97dfebe65f29a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 37483c66-6b50-47d4-b045-825a521d9aa0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:02:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"37483c66-6b50-47d4-b045-825a521d9aa0","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979458","transactionId":"99fefe58-8450-5740-bc64-9f52eee08dc4","quoteStatus":"Ordered","message":"Quote# Q-979458 status changed to Ordered.","modifiedAt":"2025-08-06T15:02:37.768Z"},"publishedAt":"2025-08-06T15:02:56.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:10:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:10:14
[webhook] [2025-08-06 15:10:14] [adwsapi_v2.php:36]  Provided signature: sha256=16bd4e6c0ec69d1e00bf7391c80b89c7cb9b08accc714ccc688360be0895a169
[webhook] [2025-08-06 15:10:14] [adwsapi_v2.php:37]  Calculated signature: sha256=9f0e9255b5a78cdbadfa3fb4f810fda6bd8950feee0f48c8bc479642e680de87
[webhook] [2025-08-06 15:10:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 176f18e4ecf578af\n    [X-B3-Traceid] => 68937053a4b3eeaf9dced5e05b172af4\n    [B3] => 68937053a4b3eeaf9dced5e05b172af4-176f18e4ecf578af-1\n    [Traceparent] => 00-68937053a4b3eeaf9dced5e05b172af4-176f18e4ecf578af-01\n    [X-Amzn-Trace-Id] => Root=1-68937053-a4b3eeaf9dced5e05b172af4;Parent=176f18e4ecf578af;Sampled=1\n    [X-Adsk-Signature] => sha256=16bd4e6c0ec69d1e00bf7391c80b89c7cb9b08accc714ccc688360be0895a169\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c4a97070-9bcb-493f-aad5-f8b346e2894f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:10:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"c4a97070-9bcb-493f-aad5-f8b346e2894f","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-987997","transactionId":"182aea67-7eff-5eea-8abd-8b6a0c7e42b0","quoteStatus":"Draft","message":"Quote# Q-987997 status changed to Draft.","modifiedAt":"2025-08-06T15:10:11.699Z"},"publishedAt":"2025-08-06T15:10:12.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:11:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:11:22
[webhook] [2025-08-06 15:11:22] [adwsapi_v2.php:36]  Provided signature: sha256=60b24fbd2b1b1153e8b558865a4a665d40abb7304b2a5f11454c7f694d982870
[webhook] [2025-08-06 15:11:22] [adwsapi_v2.php:37]  Calculated signature: sha256=d179d8591115a4fb877eec59b96cbbc0a7dc116d173e387717b665bb53070365
[webhook] [2025-08-06 15:11:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d29fc471fd5e7364\n    [X-B3-Traceid] => 68937097d8c11a17ff25e787e0532add\n    [B3] => 68937097d8c11a17ff25e787e0532add-d29fc471fd5e7364-1\n    [Traceparent] => 00-68937097d8c11a17ff25e787e0532add-d29fc471fd5e7364-01\n    [X-Amzn-Trace-Id] => Root=1-68937097-d8c11a17ff25e787e0532add;Parent=d29fc471fd5e7364;Sampled=1\n    [X-Adsk-Signature] => sha256=60b24fbd2b1b1153e8b558865a4a665d40abb7304b2a5f11454c7f694d982870\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 238267cc-62b5-4c07-ae1f-0dede77c0446\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:11:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"238267cc-62b5-4c07-ae1f-0dede77c0446","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988001","transactionId":"6b16c5d9-1a00-52bc-ad64-a4d468d3b383","quoteStatus":"Draft","message":"Quote# Q-988001 status changed to Draft.","modifiedAt":"2025-08-06T15:11:19.764Z"},"publishedAt":"2025-08-06T15:11:20.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:12:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:12:50
[webhook] [2025-08-06 15:12:50] [adwsapi_v2.php:36]  Provided signature: sha256=b0527df85c5400ac2e14366eb2f763897eba1137891aa0fdfac076d62034edf2
[webhook] [2025-08-06 15:12:50] [adwsapi_v2.php:37]  Calculated signature: sha256=b8141148afe60de1f63f783f587db5b4665de275233cd5fe32d29fd43fc0c0bd
[webhook] [2025-08-06 15:12:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3ebe5868135f21a9\n    [X-B3-Traceid] => 689370f0213789e1e11dce7ba7165329\n    [B3] => 689370f0213789e1e11dce7ba7165329-3ebe5868135f21a9-1\n    [Traceparent] => 00-689370f0213789e1e11dce7ba7165329-3ebe5868135f21a9-01\n    [X-Amzn-Trace-Id] => Root=1-689370f0-213789e1e11dce7ba7165329;Parent=3ebe5868135f21a9;Sampled=1\n    [X-Adsk-Signature] => sha256=b0527df85c5400ac2e14366eb2f763897eba1137891aa0fdfac076d62034edf2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a2e7a7ef-c343-4075-a326-6e7cebbb7ffd\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:12:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"a2e7a7ef-c343-4075-a326-6e7cebbb7ffd","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988001","transactionId":"6b16c5d9-1a00-52bc-ad64-a4d468d3b383","quoteStatus":"Quoted","message":"Quote# Q-988001 status changed to Quoted.","modifiedAt":"2025-08-06T15:12:47.906Z"},"publishedAt":"2025-08-06T15:12:48.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:16:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:16:22
[webhook] [2025-08-06 15:16:22] [adwsapi_v2.php:36]  Provided signature: sha256=143998d4e6eb5a9e4370e375ad221a1c612cc963ad514fd5a301a77aff69b69e
[webhook] [2025-08-06 15:16:22] [adwsapi_v2.php:37]  Calculated signature: sha256=9c950a25f6092a6491487768796d5826f6f7a802a82e5d4377b972bb3305c22e
[webhook] [2025-08-06 15:16:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 590dc57d34e05c14\n    [X-B3-Traceid] => 689371c4480a608c7bd039dbf8ab1233\n    [B3] => 689371c4480a608c7bd039dbf8ab1233-590dc57d34e05c14-1\n    [Traceparent] => 00-689371c4480a608c7bd039dbf8ab1233-590dc57d34e05c14-01\n    [X-Amzn-Trace-Id] => Root=1-689371c4-480a608c7bd039dbf8ab1233;Parent=590dc57d34e05c14;Sampled=1\n    [X-Adsk-Signature] => sha256=143998d4e6eb5a9e4370e375ad221a1c612cc963ad514fd5a301a77aff69b69e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 94d9ae3d-ef07-4609-bcb1-79f946a78a7e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:16:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"94d9ae3d-ef07-4609-bcb1-79f946a78a7e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988019","transactionId":"100c11a6-6684-59d2-91af-70ab12875176","quoteStatus":"Draft","message":"Quote# Q-988019 status changed to Draft.","modifiedAt":"2025-08-06T15:16:19.721Z"},"publishedAt":"2025-08-06T15:16:20.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:17:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:17:35
[webhook] [2025-08-06 15:17:35] [adwsapi_v2.php:36]  Provided signature: sha256=4dac6bc6bfffc3d39e2ebf2f14b1c5bec9969431fe012f9cd10479818b314cdd
[webhook] [2025-08-06 15:17:35] [adwsapi_v2.php:37]  Calculated signature: sha256=e49fac6f3d0b950fb3e1975d2b20930e0382429de87d1f67ee5d2d1b2200eb35
[webhook] [2025-08-06 15:17:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 31e04b48e37de2f2\n    [X-B3-Traceid] => 6893720c92964d6b62a7ad832868b0f8\n    [B3] => 6893720c92964d6b62a7ad832868b0f8-31e04b48e37de2f2-1\n    [Traceparent] => 00-6893720c92964d6b62a7ad832868b0f8-31e04b48e37de2f2-01\n    [X-Amzn-Trace-Id] => Root=1-6893720c-92964d6b62a7ad832868b0f8;Parent=31e04b48e37de2f2;Sampled=1\n    [X-Adsk-Signature] => sha256=4dac6bc6bfffc3d39e2ebf2f14b1c5bec9969431fe012f9cd10479818b314cdd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 06489a31-2cdd-4582-bf59-29f2bd846991\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:17:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"06489a31-2cdd-4582-bf59-29f2bd846991","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-988019","transactionId":"100c11a6-6684-59d2-91af-70ab12875176","quoteStatus":"Quoted","message":"Quote# Q-988019 status changed to Quoted.","modifiedAt":"2025-08-06T15:17:32.437Z"},"publishedAt":"2025-08-06T15:17:32.000Z","csn":"5103159758"}
[webhook] [2025-08-06 15:37:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 15:37:56
[webhook] [2025-08-06 15:37:56] [adwsapi_v2.php:36]  Provided signature: sha256=7e0bc6b09f1ec1a50ca4155b97aa64f3c17b57d5c272da16f18b4890aa2befe3
[webhook] [2025-08-06 15:37:56] [adwsapi_v2.php:37]  Calculated signature: sha256=898b108396bd289f83bc9d9f4fdc303857259a14fa1337004006e0aa6bd26596
[webhook] [2025-08-06 15:37:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 88752d71b70dbc18\n    [X-B3-Traceid] => 689376d1478b0bf3257d1d523a5fbf77\n    [B3] => 689376d1478b0bf3257d1d523a5fbf77-88752d71b70dbc18-1\n    [Traceparent] => 00-689376d1478b0bf3257d1d523a5fbf77-88752d71b70dbc18-01\n    [X-Amzn-Trace-Id] => Root=1-689376d1-478b0bf3257d1d523a5fbf77;Parent=88752d71b70dbc18;Sampled=1\n    [X-Adsk-Signature] => sha256=7e0bc6b09f1ec1a50ca4155b97aa64f3c17b57d5c272da16f18b4890aa2befe3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69225417-0b5c-49ea-afb4-f9253f4e7b81\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 15:37:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"69225417-0b5c-49ea-afb4-f9253f4e7b81","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62945623792724","status":"Active","quantity":1,"endDate":"2026-08-19","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-06T15:02:42.000+0000"},"publishedAt":"2025-08-06T15:37:53.000Z","csn":"5103159758"}
[webhook] [2025-08-06 16:03:13] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 16:03:13
[webhook] [2025-08-06 16:03:13] [adwsapi_v2.php:36]  Provided signature: sha256=031213e114277b7050470ed3083c52ee744dca7558f4c3c5500f339a105e16de
[webhook] [2025-08-06 16:03:13] [adwsapi_v2.php:37]  Calculated signature: sha256=df7cfd87913df9373f504afc310ad5e070bc64e6dbd29d694e07b72920df95b2
[webhook] [2025-08-06 16:03:13] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 56b86a0319cf88d3\n    [X-B3-Traceid] => 68937cbe2181f41b2a4ecdd50249e4aa\n    [B3] => 68937cbe2181f41b2a4ecdd50249e4aa-56b86a0319cf88d3-1\n    [Traceparent] => 00-68937cbe2181f41b2a4ecdd50249e4aa-56b86a0319cf88d3-01\n    [X-Amzn-Trace-Id] => Root=1-68937cbe-2181f41b2a4ecdd50249e4aa;Parent=56b86a0319cf88d3;Sampled=1\n    [X-Adsk-Signature] => sha256=031213e114277b7050470ed3083c52ee744dca7558f4c3c5500f339a105e16de\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754496190702-72527238809770\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 16:03:13] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754496190702-72527238809770","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527238809770","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T16:03:10.702Z"},"publishedAt":"2025-08-06T16:03:10.000Z","csn":"5103159758"}
[webhook] [2025-08-06 16:05:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 16:05:47
[webhook] [2025-08-06 16:05:47] [adwsapi_v2.php:36]  Provided signature: sha256=07ebce9780a87c6901ce1e96883b4ad55102fd1e5d52c2a7fbe28004edc1ae46
[webhook] [2025-08-06 16:05:47] [adwsapi_v2.php:37]  Calculated signature: sha256=7129bb6f8ac616a5d21b3d0419ede454a031b4bf0ec645521e376b68c8831575
[webhook] [2025-08-06 16:05:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 58318f1801042249\n    [X-B3-Traceid] => 68937d5910feda75715b69d540730816\n    [B3] => 68937d5910feda75715b69d540730816-58318f1801042249-1\n    [Traceparent] => 00-68937d5910feda75715b69d540730816-58318f1801042249-01\n    [X-Amzn-Trace-Id] => Root=1-68937d59-10feda75715b69d540730816;Parent=58318f1801042249;Sampled=1\n    [X-Adsk-Signature] => sha256=07ebce9780a87c6901ce1e96883b4ad55102fd1e5d52c2a7fbe28004edc1ae46\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754496345264-65995913110554\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 16:05:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754496345264-65995913110554","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995913110554","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T16:05:45.264Z"},"publishedAt":"2025-08-06T16:05:45.000Z","csn":"5103159758"}
[webhook] [2025-08-06 19:39:55] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 19:39:55
[webhook] [2025-08-06 19:39:55] [adwsapi_v2.php:36]  Provided signature: sha256=a5e53748a5396fb59cafc76ebc0009efe2063209c825022ece217334ea617ba6
[webhook] [2025-08-06 19:39:55] [adwsapi_v2.php:37]  Calculated signature: sha256=5af2fb0b64b7850ed4bb0c5ac329a0ebc75b67242ffa54fa286a04e522f1e7d3
[webhook] [2025-08-06 19:39:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 31288e8b741b21d5\n    [X-B3-Traceid] => 6893af88132b72a43e139cb36093e3ca\n    [B3] => 6893af88132b72a43e139cb36093e3ca-31288e8b741b21d5-1\n    [Traceparent] => 00-6893af88132b72a43e139cb36093e3ca-31288e8b741b21d5-01\n    [X-Amzn-Trace-Id] => Root=1-6893af88-132b72a43e139cb36093e3ca;Parent=31288e8b741b21d5;Sampled=1\n    [X-Adsk-Signature] => sha256=a5e53748a5396fb59cafc76ebc0009efe2063209c825022ece217334ea617ba6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cc8fd7a6-43fe-4c13-9d54-34e98cbd1c31\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 19:39:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"cc8fd7a6-43fe-4c13-9d54-34e98cbd1c31","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62945623792724","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-06T19:14:41.000+0000"},"publishedAt":"2025-08-06T19:39:52.000Z","csn":"5103159758"}
[webhook] [2025-08-06 20:01:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 20:01:38
[webhook] [2025-08-06 20:01:38] [adwsapi_v2.php:36]  Provided signature: sha256=62d708ddd219261685faa140ec492ab7e49af71c89b058c1996a896daafc8f2a
[webhook] [2025-08-06 20:01:38] [adwsapi_v2.php:37]  Calculated signature: sha256=12a6b0901da2242c3ab1eadab7489bde07eef627c76e706d979a08b4f06f34c6
[webhook] [2025-08-06 20:01:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d7b421e3a649966e\n    [X-B3-Traceid] => 6893b4a01195b8c8216cd4705f860469\n    [B3] => 6893b4a01195b8c8216cd4705f860469-d7b421e3a649966e-1\n    [Traceparent] => 00-6893b4a01195b8c8216cd4705f860469-d7b421e3a649966e-01\n    [X-Amzn-Trace-Id] => Root=1-6893b4a0-1195b8c8216cd4705f860469;Parent=d7b421e3a649966e;Sampled=1\n    [X-Adsk-Signature] => sha256=62d708ddd219261685faa140ec492ab7e49af71c89b058c1996a896daafc8f2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754510496500-62945623792724\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 20:01:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754510496500-62945623792724","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62945623792724","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T20:01:36.500Z"},"publishedAt":"2025-08-06T20:01:36.000Z","csn":"5103159758"}
[webhook] [2025-08-06 20:16:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 20:16:00
[webhook] [2025-08-06 20:16:00] [adwsapi_v2.php:36]  Provided signature: sha256=b3f80afe3d9fb53951e569a598c75433a449708025735a57ddd5d411e3253917
[webhook] [2025-08-06 20:16:00] [adwsapi_v2.php:37]  Calculated signature: sha256=ea69c367d0a3eb69eb19311c527e6c1d7e28cd1e2e063114a068f6d936731843
[webhook] [2025-08-06 20:16:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 305df8e1e6ebc6c4\n    [X-B3-Traceid] => 6893b7fc7fbedd7d3a9e87a437020a7b\n    [B3] => 6893b7fc7fbedd7d3a9e87a437020a7b-305df8e1e6ebc6c4-1\n    [Traceparent] => 00-6893b7fc7fbedd7d3a9e87a437020a7b-305df8e1e6ebc6c4-01\n    [X-Amzn-Trace-Id] => Root=1-6893b7fc-7fbedd7d3a9e87a437020a7b;Parent=305df8e1e6ebc6c4;Sampled=1\n    [X-Adsk-Signature] => sha256=b3f80afe3d9fb53951e569a598c75433a449708025735a57ddd5d411e3253917\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754511356349-72527238809770\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 20:16:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754511356349-72527238809770","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527238809770","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T20:15:56.349Z"},"publishedAt":"2025-08-06T20:15:56.000Z","csn":"5103159758"}
[webhook] [2025-08-06 20:21:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 20:21:51
[webhook] [2025-08-06 20:21:51] [adwsapi_v2.php:36]  Provided signature: sha256=ac8dda6d4d0caf6a91ac68ae7b56420bccddbb970b7c1c183389089153eaeb6a
[webhook] [2025-08-06 20:21:51] [adwsapi_v2.php:37]  Calculated signature: sha256=fb7300ac8c613e7c574b89f54b6bd564f3346412e971db2598ed336d0bf5fddb
[webhook] [2025-08-06 20:21:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 93d19bcc1929bea4\n    [X-B3-Traceid] => 6893b95d29dc9075582e5b74565aff8f\n    [B3] => 6893b95d29dc9075582e5b74565aff8f-93d19bcc1929bea4-1\n    [Traceparent] => 00-6893b95d29dc9075582e5b74565aff8f-93d19bcc1929bea4-01\n    [X-Amzn-Trace-Id] => Root=1-6893b95d-29dc9075582e5b74565aff8f;Parent=93d19bcc1929bea4;Sampled=1\n    [X-Adsk-Signature] => sha256=ac8dda6d4d0caf6a91ac68ae7b56420bccddbb970b7c1c183389089153eaeb6a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754511709535-65995913110554\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 20:21:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754511709535-65995913110554","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65995913110554","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T20:21:49.535Z"},"publishedAt":"2025-08-06T20:21:49.000Z","csn":"5103159758"}
[webhook] [2025-08-06 22:04:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 22:04:12
[webhook] [2025-08-06 22:04:12] [adwsapi_v2.php:36]  Provided signature: sha256=05252bf95c11752a58813f9ff6f344e55aca8152388c8bc36442e2423db550c9
[webhook] [2025-08-06 22:04:12] [adwsapi_v2.php:37]  Calculated signature: sha256=ca8765527e2175519c8955e0548622f3a6fc88240ffdea5638acaebc5cdb55bd
[webhook] [2025-08-06 22:04:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f718444afd0a480e\n    [X-B3-Traceid] => 6893d15a5703ab1f6965dd4a5ae82f8f\n    [B3] => 6893d15a5703ab1f6965dd4a5ae82f8f-f718444afd0a480e-1\n    [Traceparent] => 00-6893d15a5703ab1f6965dd4a5ae82f8f-f718444afd0a480e-01\n    [X-Amzn-Trace-Id] => Root=1-6893d15a-5703ab1f6965dd4a5ae82f8f;Parent=f718444afd0a480e;Sampled=1\n    [X-Adsk-Signature] => sha256=05252bf95c11752a58813f9ff6f344e55aca8152388c8bc36442e2423db550c9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754517850151-62945623792724\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 22:04:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754517850151-62945623792724","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"62945623792724","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-06T22:04:10.151Z"},"publishedAt":"2025-08-06T22:04:10.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:00:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:00:49
[webhook] [2025-08-06 23:00:49] [adwsapi_v2.php:36]  Provided signature: sha256=c38f1f3cfb7b723de4d011c257543c0e027e06c571ac9b124568843dcb55ccca
[webhook] [2025-08-06 23:00:49] [adwsapi_v2.php:37]  Calculated signature: sha256=0f496dae44914d9cc465b989ec56b01c006d677b47a28b9042606505cec62045
[webhook] [2025-08-06 23:00:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d88327f107815828\n    [X-B3-Traceid] => 6893de9f415a8dc6fbd601abe2ef723e\n    [B3] => 6893de9f415a8dc6fbd601abe2ef723e-d88327f107815828-1\n    [Traceparent] => 00-6893de9f415a8dc6fbd601abe2ef723e-d88327f107815828-01\n    [X-Amzn-Trace-Id] => Root=1-6893de9f-415a8dc6fbd601abe2ef723e;Parent=d88327f107815828;Sampled=1\n    [X-Adsk-Signature] => sha256=c38f1f3cfb7b723de4d011c257543c0e027e06c571ac9b124568843dcb55ccca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f846e0dc-66ef-42e0-b7bd-316e05f2d5ae\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:00:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"f846e0dc-66ef-42e0-b7bd-316e05f2d5ae","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-910478","transactionId":"149e034e-7417-5457-8f8f-63abe15b8a5c","quoteStatus":"Expired","message":"Quote# Q-910478 status changed to Expired.","modifiedAt":"2025-08-06T23:00:30.339Z"},"publishedAt":"2025-08-06T23:00:47.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:01:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:01:41
[webhook] [2025-08-06 23:01:41] [adwsapi_v2.php:36]  Provided signature: sha256=269df0eaa38eb0bff1e0cb25a76b064ce90da4592aef63484b6a668d64478dc0
[webhook] [2025-08-06 23:01:41] [adwsapi_v2.php:37]  Calculated signature: sha256=7f40ab4b202b2a1f197f303c5d822cde4e6f27d67b5f28324d88f9143dbfca05
[webhook] [2025-08-06 23:01:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => be63a2cc467dca8e\n    [X-B3-Traceid] => 6893ded3585f00c518931dff4bbcb25e\n    [B3] => 6893ded3585f00c518931dff4bbcb25e-be63a2cc467dca8e-1\n    [Traceparent] => 00-6893ded3585f00c518931dff4bbcb25e-be63a2cc467dca8e-01\n    [X-Amzn-Trace-Id] => Root=1-6893ded3-585f00c518931dff4bbcb25e;Parent=be63a2cc467dca8e;Sampled=1\n    [X-Adsk-Signature] => sha256=269df0eaa38eb0bff1e0cb25a76b064ce90da4592aef63484b6a668d64478dc0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754521299076-561-62088102\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:01:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754521299076-561-62088102","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"561-62088102","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-06T23:01:39.076Z"},"publishedAt":"2025-08-06T23:01:39.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:01:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:01:48
[webhook] [2025-08-06 23:01:48] [adwsapi_v2.php:36]  Provided signature: sha256=046403d137225904e9474549cbd79ee86124674197bec7cb193b6ad0a0e82405
[webhook] [2025-08-06 23:01:48] [adwsapi_v2.php:37]  Calculated signature: sha256=41f5e06c03e14205decdc685281207943d5092a289209d79c95e60b65bd8d539
[webhook] [2025-08-06 23:01:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1b394bff2c8ef88c\n    [X-B3-Traceid] => 6893ded92b38d5f301732b1669fba038\n    [B3] => 6893ded92b38d5f301732b1669fba038-1b394bff2c8ef88c-1\n    [Traceparent] => 00-6893ded92b38d5f301732b1669fba038-1b394bff2c8ef88c-01\n    [X-Amzn-Trace-Id] => Root=1-6893ded9-2b38d5f301732b1669fba038;Parent=1b394bff2c8ef88c;Sampled=1\n    [X-Adsk-Signature] => sha256=046403d137225904e9474549cbd79ee86124674197bec7cb193b6ad0a0e82405\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754521305817-574-93530292\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:01:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754521305817-574-93530292","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-93530292","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-06T23:01:45.817Z"},"publishedAt":"2025-08-06T23:01:45.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:01:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:01:49
[webhook] [2025-08-06 23:01:49] [adwsapi_v2.php:36]  Provided signature: sha256=d4aacd4aa25e2f28eba43d8d3db4ef9c66d34382dba41dcf32e3284f8fca311a
[webhook] [2025-08-06 23:01:49] [adwsapi_v2.php:37]  Calculated signature: sha256=2b7bfacb36cfddccef40d041ac88680873e393774db6150b567c4a02faa17877
[webhook] [2025-08-06 23:01:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 887d7a9ec8208b52\n    [X-B3-Traceid] => 6893dedb7dcb7ff02a92a40753cff279\n    [B3] => 6893dedb7dcb7ff02a92a40753cff279-887d7a9ec8208b52-1\n    [Traceparent] => 00-6893dedb7dcb7ff02a92a40753cff279-887d7a9ec8208b52-01\n    [X-Amzn-Trace-Id] => Root=1-6893dedb-7dcb7ff02a92a40753cff279;Parent=887d7a9ec8208b52;Sampled=1\n    [X-Adsk-Signature] => sha256=d4aacd4aa25e2f28eba43d8d3db4ef9c66d34382dba41dcf32e3284f8fca311a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754521307292-574-93544051\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:01:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754521307292-574-93544051","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-93544051","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-06T23:01:47.292Z"},"publishedAt":"2025-08-06T23:01:47.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:02:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:02:35
[webhook] [2025-08-06 23:02:35] [adwsapi_v2.php:36]  Provided signature: sha256=38f4ca74311da90b999cf18640d5d10745e218c079b3b33a2a1f4dc2bd2ab1a2
[webhook] [2025-08-06 23:02:35] [adwsapi_v2.php:37]  Calculated signature: sha256=4ad5a274fe630392041d273d73fe8d0e75aaa48c7c8be827570489b5fa9c1dca
[webhook] [2025-08-06 23:02:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a474834a095745e9\n    [X-B3-Traceid] => 6893df094a8978f84063c99b3352f752\n    [B3] => 6893df094a8978f84063c99b3352f752-a474834a095745e9-1\n    [Traceparent] => 00-6893df094a8978f84063c99b3352f752-a474834a095745e9-01\n    [X-Amzn-Trace-Id] => Root=1-6893df09-4a8978f84063c99b3352f752;Parent=a474834a095745e9;Sampled=1\n    [X-Adsk-Signature] => sha256=38f4ca74311da90b999cf18640d5d10745e218c079b3b33a2a1f4dc2bd2ab1a2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754521353629-572-19399186\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:02:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754521353629-572-19399186","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-19399186","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-06T23:02:33.629Z"},"publishedAt":"2025-08-06T23:02:33.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:02:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:02:53
[webhook] [2025-08-06 23:02:53] [adwsapi_v2.php:36]  Provided signature: sha256=6ef922c2a1c5232f92d941a6d1332871d0dd7ac9abd495ac56f06426fbd53c55
[webhook] [2025-08-06 23:02:53] [adwsapi_v2.php:37]  Calculated signature: sha256=3db673b1fe03e8e76067d73ee7eebeb5f7079041dbe0c367469683daea472671
[webhook] [2025-08-06 23:02:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0783c057b77aec48\n    [X-B3-Traceid] => 6893df1b31944fe36aaf43c12d8c1dfc\n    [B3] => 6893df1b31944fe36aaf43c12d8c1dfc-0783c057b77aec48-1\n    [Traceparent] => 00-6893df1b31944fe36aaf43c12d8c1dfc-0783c057b77aec48-01\n    [X-Amzn-Trace-Id] => Root=1-6893df1b-31944fe36aaf43c12d8c1dfc;Parent=0783c057b77aec48;Sampled=1\n    [X-Adsk-Signature] => sha256=6ef922c2a1c5232f92d941a6d1332871d0dd7ac9abd495ac56f06426fbd53c55\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754521371718-574-93532668\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:02:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754521371718-574-93532668","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"574-93532668","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-06T23:02:51.718Z"},"publishedAt":"2025-08-06T23:02:51.000Z","csn":"5103159758"}
[webhook] [2025-08-06 23:03:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-06 23:03:25
[webhook] [2025-08-06 23:03:25] [adwsapi_v2.php:36]  Provided signature: sha256=79e0ce76bed2507403cdc7232774d2c343464a93cad45dccc4ec8d086d694543
[webhook] [2025-08-06 23:03:25] [adwsapi_v2.php:37]  Calculated signature: sha256=c58b1ef5ff0ebf74556149e552c9c14ccabd0e148121c41f72a6bb4923c9f2f6
[webhook] [2025-08-06 23:03:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a89e8d47fd10cff4\n    [X-B3-Traceid] => 6893df3b32e8bb6a4eb1e8cd227adb9f\n    [B3] => 6893df3b32e8bb6a4eb1e8cd227adb9f-a89e8d47fd10cff4-1\n    [Traceparent] => 00-6893df3b32e8bb6a4eb1e8cd227adb9f-a89e8d47fd10cff4-01\n    [X-Amzn-Trace-Id] => Root=1-6893df3b-32e8bb6a4eb1e8cd227adb9f;Parent=a89e8d47fd10cff4;Sampled=1\n    [X-Adsk-Signature] => sha256=79e0ce76bed2507403cdc7232774d2c343464a93cad45dccc4ec8d086d694543\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754521403817-569-59178474\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-06 23:03:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754521403817-569-59178474","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"569-59178474","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-06T23:03:23.817Z"},"publishedAt":"2025-08-06T23:03:23.000Z","csn":"5103159758"}
[webhook] [2025-08-07 00:00:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-07 00:00:30
[webhook] [2025-08-07 00:00:30] [adwsapi_v2.php:36]  Provided signature: sha256=5d732474cd9bda228dd82aca6dfe7862430b6bffeebc45df5aff53ca272d3830
[webhook] [2025-08-07 00:00:30] [adwsapi_v2.php:37]  Calculated signature: sha256=c26f645940342e8b1e1c8bda9bd6d3acb6bddae258b3eba49d2345466f687152
[webhook] [2025-08-07 00:00:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ec67f03906c965ce\n    [X-B3-Traceid] => 6893ec9b6ce419216637a31e191b43f5\n    [B3] => 6893ec9b6ce419216637a31e191b43f5-ec67f03906c965ce-1\n    [Traceparent] => 00-6893ec9b6ce419216637a31e191b43f5-ec67f03906c965ce-01\n    [X-Amzn-Trace-Id] => Root=1-6893ec9b-6ce419216637a31e191b43f5;Parent=ec67f03906c965ce;Sampled=1\n    [X-Adsk-Signature] => sha256=5d732474cd9bda228dd82aca6dfe7862430b6bffeebc45df5aff53ca272d3830\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c6eff6d0-0439-4b11-8d6f-6215093295fb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-07 00:00:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"c6eff6d0-0439-4b11-8d6f-6215093295fb","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-07T00:00:27Z"},"publishedAt":"2025-08-07T00:00:27.000Z","country":"GB"}
[webhook] [2025-08-07 09:07:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-07 09:07:50
[webhook] [2025-08-07 09:07:50] [adwsapi_v2.php:36]  Provided signature: sha256=d33684e0e700206e894aa6f2116e2af401611fbf7ebe80ec507e9c77e88c1dd9
[webhook] [2025-08-07 09:07:50] [adwsapi_v2.php:37]  Calculated signature: sha256=7f33c87b008f97248ca53860057503899addd7d65fa53b16f15dfaf54636b9eb
[webhook] [2025-08-07 09:07:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0cbeb071d5de1758\n    [X-B3-Traceid] => 68946ce426f3626c6817d43a2b26cb3b\n    [B3] => 68946ce426f3626c6817d43a2b26cb3b-0cbeb071d5de1758-1\n    [Traceparent] => 00-68946ce426f3626c6817d43a2b26cb3b-0cbeb071d5de1758-01\n    [X-Amzn-Trace-Id] => Root=1-68946ce4-26f3626c6817d43a2b26cb3b;Parent=0cbeb071d5de1758;Sampled=1\n    [X-Adsk-Signature] => sha256=d33684e0e700206e894aa6f2116e2af401611fbf7ebe80ec507e9c77e88c1dd9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6771bda3-064e-4f8a-aa90-55b08e27654e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-07 09:07:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"6771bda3-064e-4f8a-aa90-55b08e27654e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"65468891106219","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-07T08:47:42.000+0000"},"publishedAt":"2025-08-07T09:07:48.000Z","csn":"5103159758"}
[webhook] [2025-08-07 09:08:11] [adwsapi_v2.php:20]  Webhook request received at 2025-08-07 09:08:11
[webhook] [2025-08-07 09:08:11] [adwsapi_v2.php:36]  Provided signature: sha256=383fcf90b82ed4c6bc579b7c65cc082db834b43997c49f9a6bf2bf5d43ed270d
[webhook] [2025-08-07 09:08:11] [adwsapi_v2.php:37]  Calculated signature: sha256=64200726d54d2176f88f599071d84c705ca68b21decd8429ab547be90fb693f3
[webhook] [2025-08-07 09:08:11] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7c8fbc3129eeacdb\n    [X-B3-Traceid] => 68946cf9107ec943790bb01526d67959\n    [B3] => 68946cf9107ec943790bb01526d67959-7c8fbc3129eeacdb-1\n    [Traceparent] => 00-68946cf9107ec943790bb01526d67959-7c8fbc3129eeacdb-01\n    [X-Amzn-Trace-Id] => Root=1-68946cf9-107ec943790bb01526d67959;Parent=7c8fbc3129eeacdb;Sampled=1\n    [X-Adsk-Signature] => sha256=383fcf90b82ed4c6bc579b7c65cc082db834b43997c49f9a6bf2bf5d43ed270d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => baeb6884-3a20-463c-8696-4c92c455e840\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-07 09:08:11] [adwsapi_v2.php:57]  Received webhook data: {"id":"baeb6884-3a20-463c-8696-4c92c455e840","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72527238809770","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-08-07T08:48:06.000+0000"},"publishedAt":"2025-08-07T09:08:09.000Z","csn":"5103159758"}
[webhook] [2025-08-07 12:58:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-07 12:58:19
[webhook] [2025-08-07 12:58:19] [adwsapi_v2.php:36]  Provided signature: sha256=6d91d2e7166208d54b2983668ee99200e8e58c7558a35ed6e2b196dc56c3d92b
[webhook] [2025-08-07 12:58:19] [adwsapi_v2.php:37]  Calculated signature: sha256=ad1cb7bceb5ad76eb49860fc61d7ea0b10d11f6373b55ed8557a05139dbbde70
[webhook] [2025-08-07 12:58:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b98992e29aa72a61\n    [X-B3-Traceid] => 6894a2e8c5c31ef05845aefd685ea4a7\n    [B3] => 6894a2e8c5c31ef05845aefd685ea4a7-b98992e29aa72a61-1\n    [Traceparent] => 00-6894a2e8c5c31ef05845aefd685ea4a7-b98992e29aa72a61-01\n    [X-Amzn-Trace-Id] => Root=1-6894a2e8-c5c31ef05845aefd685ea4a7;Parent=b98992e29aa72a61;Sampled=1\n    [X-Adsk-Signature] => sha256=6d91d2e7166208d54b2983668ee99200e8e58c7558a35ed6e2b196dc56c3d92b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 41253b2a-d0c1-4da9-930e-f7ff5ab43906\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-07 12:58:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"41253b2a-d0c1-4da9-930e-f7ff5ab43906","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-990570","transactionId":"bad6bd08-c710-54e2-ac20-dc066233b82a","quoteStatus":"Draft","message":"Quote# Q-990570 status changed to Draft.","modifiedAt":"2025-08-07T12:58:16.825Z"},"publishedAt":"2025-08-07T12:58:17.000Z","csn":"5103159758"}
[webhook] [2025-08-07 12:58:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-07 12:58:49
[webhook] [2025-08-07 12:58:49] [adwsapi_v2.php:36]  Provided signature: sha256=b2afbd38ee35bafb4c160ebd7b842916b51965b9bcad2839faed36440fb39ed9
[webhook] [2025-08-07 12:58:49] [adwsapi_v2.php:37]  Calculated signature: sha256=792dd7819c352cca9e3e5e99929c0521e5371f941f9c26689ac277c644036535
[webhook] [2025-08-07 12:58:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e61446b88caefbab\n    [X-B3-Traceid] => 6894a307f720f22e5ecf9e122250978d\n    [B3] => 6894a307f720f22e5ecf9e122250978d-e61446b88caefbab-1\n    [Traceparent] => 00-6894a307f720f22e5ecf9e122250978d-e61446b88caefbab-01\n    [X-Amzn-Trace-Id] => Root=1-6894a307-f720f22e5ecf9e122250978d;Parent=e61446b88caefbab;Sampled=1\n    [X-Adsk-Signature] => sha256=b2afbd38ee35bafb4c160ebd7b842916b51965b9bcad2839faed36440fb39ed9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1601ddb3-9423-4bbd-930e-1026e722138d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-07 12:58:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"1601ddb3-9423-4bbd-930e-1026e722138d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-990570","transactionId":"bad6bd08-c710-54e2-ac20-dc066233b82a","quoteStatus":"Quoted","message":"Quote# Q-990570 status changed to Quoted.","modifiedAt":"2025-08-07T12:58:46.847Z"},"publishedAt":"2025-08-07T12:58:47.000Z","csn":"5103159758"}
