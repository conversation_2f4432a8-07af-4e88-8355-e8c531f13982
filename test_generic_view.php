<?php
/**
 * Test the generic view functionality
 * This demonstrates how the generic view system works with any datatable
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use edge\edge;
use data_table;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generic View System Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Generic View System Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">About This Test</h2>
            <p class="text-gray-600 mb-4">
                This page demonstrates the generic view system that can display detailed views of any datatable row 
                without requiring custom templates. Click the "View" button on any row to see the generic view in action.
            </p>
            <div class="bg-blue-50 border border-blue-200 rounded p-4">
                <h3 class="font-medium text-blue-900 mb-2">Features Demonstrated:</h3>
                <ul class="text-blue-800 text-sm space-y-1">
                    <li>• Automatic field categorization (primary, contact, system fields)</li>
                    <li>• Smart formatting for emails, URLs, booleans</li>
                    <li>• Collapsible sections for better organization</li>
                    <li>• Print-friendly styling</li>
                    <li>• Copy-to-clipboard functionality</li>
                </ul>
            </div>
        </div>

        <!-- Sample Data Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Sample Employee Data</h2>
                <p class="text-sm text-gray-500">Click "View" on any row to see the generic view display</p>
            </div>
            <div class="p-6">
                <?php
                // Sample data with various field types to demonstrate the generic view
                $sample_data = [
                    [
                        'id' => 1,
                        'employee_id' => 'EMP001',
                        'name' => 'John Doe',
                        'email' => '<EMAIL>',
                        'phone' => '************',
                        'department' => 'Engineering',
                        'position' => 'Senior Developer',
                        'salary' => 85000,
                        'hire_date' => '2020-03-15',
                        'is_active' => true,
                        'manager_id' => 5,
                        'address1' => '123 Main Street',
                        'address2' => 'Apt 4B',
                        'city' => 'San Francisco',
                        'state' => 'CA',
                        'postal_code' => '94105',
                        'country' => 'USA',
                        'emergency_contact_name' => 'Jane Doe',
                        'emergency_contact_phone' => '************',
                        'created_at' => '2020-03-15 09:00:00',
                        'updated_at' => '2024-01-15 14:30:00'
                    ],
                    [
                        'id' => 2,
                        'employee_id' => 'EMP002',
                        'name' => 'Jane Smith',
                        'email' => '<EMAIL>',
                        'phone' => '************',
                        'department' => 'Marketing',
                        'position' => 'Marketing Manager',
                        'salary' => 75000,
                        'hire_date' => '2019-08-22',
                        'is_active' => true,
                        'manager_id' => 3,
                        'address1' => '456 Oak Avenue',
                        'address2' => '',
                        'city' => 'Los Angeles',
                        'state' => 'CA',
                        'postal_code' => '90210',
                        'country' => 'USA',
                        'emergency_contact_name' => 'Bob Smith',
                        'emergency_contact_phone' => '************',
                        'created_at' => '2019-08-22 10:15:00',
                        'updated_at' => '2024-01-10 11:20:00'
                    ],
                    [
                        'id' => 3,
                        'employee_id' => 'EMP003',
                        'name' => 'Bob Johnson',
                        'email' => '<EMAIL>',
                        'phone' => '************',
                        'department' => 'Sales',
                        'position' => 'Sales Representative',
                        'salary' => 65000,
                        'hire_date' => '2021-11-08',
                        'is_active' => false,
                        'manager_id' => 4,
                        'address1' => '789 Pine Street',
                        'address2' => 'Suite 200',
                        'city' => 'Seattle',
                        'state' => 'WA',
                        'postal_code' => '98101',
                        'country' => 'USA',
                        'emergency_contact_name' => 'Alice Johnson',
                        'emergency_contact_phone' => '************',
                        'created_at' => '2021-11-08 13:45:00',
                        'updated_at' => '2023-12-20 16:10:00'
                    ],
                    [
                        'id' => 4,
                        'employee_id' => 'EMP004',
                        'name' => 'Alice Williams',
                        'email' => '<EMAIL>',
                        'phone' => '************',
                        'department' => 'Human Resources',
                        'position' => 'HR Specialist',
                        'salary' => 70000,
                        'hire_date' => '2018-05-14',
                        'is_active' => true,
                        'manager_id' => null,
                        'address1' => '321 Elm Drive',
                        'address2' => '',
                        'city' => 'Portland',
                        'state' => 'OR',
                        'postal_code' => '97201',
                        'country' => 'USA',
                        'emergency_contact_name' => 'Charlie Williams',
                        'emergency_contact_phone' => '************',
                        'created_at' => '2018-05-14 08:30:00',
                        'updated_at' => '2024-01-05 09:45:00'
                    ]
                ];

                // Define columns including the generic view action button
                $columns = [
                    [
                        'label' => 'ID',
                        'field' => 'id',
                        'filter' => false,
                        'extra_parameters' => ''
                    ],
                    [
                        'label' => 'Employee ID',
                        'field' => 'employee_id',
                        'filter' => true,
                        'extra_parameters' => ''
                    ],
                    [
                        'label' => 'Name',
                        'field' => 'name',
                        'filter' => true,
                        'extra_parameters' => ''
                    ],
                    [
                        'label' => 'Email',
                        'field' => 'email',
                        'filter' => true,
                        'extra_parameters' => ''
                    ],
                    [
                        'label' => 'Department',
                        'field' => 'department',
                        'filter' => true,
                        'extra_parameters' => ''
                    ],
                    [
                        'label' => 'Position',
                        'field' => 'position',
                        'filter' => true,
                        'extra_parameters' => ''
                    ],
                    [
                        'label' => 'Status',
                        'field' => 'is_active',
                        'filter' => false,
                        'content' => function($item) {
                            $status = $item['is_active'] ? 'Active' : 'Inactive';
                            $class = $item['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                            return "<span class='px-2 py-1 text-xs font-medium rounded-full {$class}'>{$status}</span>";
                        }
                    ],
                    [
                        'label' => 'Actions',
                        'content' => function($item) {
                            return Edge::render('action-generic-view-button', [
                                'field_value' => $item['id'],
                                'item' => $item,
                                'table_name' => 'employees_test', // This would be your actual table name
                                'id_field' => 'id',
                                'icon' => '👁️',
                                'label' => 'View'
                            ]);
                        }
                    ]
                ];

                try {
                    $table_result = data_table::process_data_table(
                        ['columns' => $columns],
                        $sample_data,
                        '', // No db_table since we're using hardcoded data
                        'test_generic_view_callback', // Callback function
                        [], // No replacements
                        [], // No criteria
                        [], // No hidden columns
                        false, // Not just body
                        false, // Not just rows
                        false, // Not just table
                        false, // No HTMX OOB
                        null, // No total count
                        'generic_view_test_table' // Table name for storage
                    );
                    
                    echo $table_result;
                    
                } catch (Exception $e) {
                    echo '<div class="bg-red-50 border border-red-200 rounded p-4">';
                    echo '<p class="text-red-800">Error generating table: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-white rounded-lg shadow p-6 mt-8">
            <h2 class="text-xl font-semibold mb-4">How to Use in Your Own Tables</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Method 1: Using Column Manager</h3>
                    <ol class="list-decimal list-inside text-gray-600 space-y-1">
                        <li>Open the column manager for your datatable</li>
                        <li>Find the column where you want to add the view button</li>
                        <li>Click "Add Action Button"</li>
                        <li>Fill in: Template: <code class="bg-gray-100 px-1 rounded">action-generic-view-button</code>, Field: your ID field, Icon: 👁️</li>
                    </ol>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Method 2: Manual Column Definition</h3>
                    <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto"><code>[
    'label' => 'Actions',
    'content' => function($item) {
        return Edge::render('action-generic-view-button', [
            'field_value' => $item['id'],
            'item' => $item,
            'table_name' => 'your_table_name',
            'id_field' => 'id'
        ]);
    }
]</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Container for the generic view -->
    <div id="modal-container"></div>

    <!-- Global Modal (if using the system modal) -->
    <div x-data="{ showModal: false }" x-show="showModal" x-cloak 
         class="fixed inset-0 z-50 overflow-y-auto" 
         @keydown.escape.window="showModal = false">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" 
                 x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" 
                 x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" 
                 class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
                 @click="showModal = false"></div>

            <div x-show="showModal" x-transition:enter="ease-out duration-300" 
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" 
                 x-transition:leave="ease-in duration-200" 
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
                 class="inline-block w-full max-w-6xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
                
                <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Record Details</h3>
                    <button @click="showModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div id="modal_body" class="max-h-96 overflow-y-auto">
                    <!-- Content will be loaded here via HTMX -->
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php
/**
 * Callback function for the test table
 */
function test_generic_view_callback($criteria = []) {
    // This would normally filter/search your data based on criteria
    // For this test, we'll just return the same data
    return test_generic_view_data();
}

function test_generic_view_data() {
    // Return the same sample data (in a real scenario, this would query your database)
    $sample_data = [
        [
            'id' => 1,
            'employee_id' => 'EMP001',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '************',
            'department' => 'Engineering',
            'position' => 'Senior Developer',
            'salary' => 85000,
            'hire_date' => '2020-03-15',
            'is_active' => true,
            'manager_id' => 5,
            'address1' => '123 Main Street',
            'address2' => 'Apt 4B',
            'city' => 'San Francisco',
            'state' => 'CA',
            'postal_code' => '94105',
            'country' => 'USA',
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '************',
            'created_at' => '2020-03-15 09:00:00',
            'updated_at' => '2024-01-15 14:30:00'
        ],
        [
            'id' => 2,
            'employee_id' => 'EMP002',
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '************',
            'department' => 'Marketing',
            'position' => 'Marketing Manager',
            'salary' => 75000,
            'hire_date' => '2019-08-22',
            'is_active' => true,
            'manager_id' => 3,
            'address1' => '456 Oak Avenue',
            'address2' => '',
            'city' => 'Los Angeles',
            'state' => 'CA',
            'postal_code' => '90210',
            'country' => 'USA',
            'emergency_contact_name' => 'Bob Smith',
            'emergency_contact_phone' => '************',
            'created_at' => '2019-08-22 10:15:00',
            'updated_at' => '2024-01-10 11:20:00'
        ],
        [
            'id' => 3,
            'employee_id' => 'EMP003',
            'name' => 'Bob Johnson',
            'email' => '<EMAIL>',
            'phone' => '************',
            'department' => 'Sales',
            'position' => 'Sales Representative',
            'salary' => 65000,
            'hire_date' => '2021-11-08',
            'is_active' => false,
            'manager_id' => 4,
            'address1' => '789 Pine Street',
            'address2' => 'Suite 200',
            'city' => 'Seattle',
            'state' => 'WA',
            'postal_code' => '98101',
            'country' => 'USA',
            'emergency_contact_name' => 'Alice Johnson',
            'emergency_contact_phone' => '************',
            'created_at' => '2021-11-08 13:45:00',
            'updated_at' => '2023-12-20 16:10:00'
        ],
        [
            'id' => 4,
            'employee_id' => 'EMP004',
            'name' => 'Alice Williams',
            'email' => '<EMAIL>',
            'phone' => '************',
            'department' => 'Human Resources',
            'position' => 'HR Specialist',
            'salary' => 70000,
            'hire_date' => '2018-05-14',
            'is_active' => true,
            'manager_id' => null,
            'address1' => '321 Elm Drive',
            'address2' => '',
            'city' => 'Portland',
            'state' => 'OR',
            'postal_code' => '97201',
            'country' => 'USA',
            'emergency_contact_name' => 'Charlie Williams',
            'emergency_contact_phone' => '************',
            'created_at' => '2018-05-14 08:30:00',
            'updated_at' => '2024-01-05 09:45:00'
        ]
    ];

    $columns = [
        [
            'label' => 'ID',
            'field' => 'id',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Employee ID',
            'field' => 'employee_id',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Name',
            'field' => 'name',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Email',
            'field' => 'email',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Department',
            'field' => 'department',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Position',
            'field' => 'position',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Status',
            'field' => 'is_active',
            'filter' => false,
            'content' => function($item) {
                $status = $item['is_active'] ? 'Active' : 'Inactive';
                $class = $item['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                return "<span class='px-2 py-1 text-xs font-medium rounded-full {$class}'>{$status}</span>";
            }
        ],
        [
            'label' => 'Actions',
            'content' => function($item) {
                return Edge::render('action-generic-view-button', [
                    'field_value' => $item['id'],
                    'item' => $item,
                    'table_name' => 'employees_test',
                    'id_field' => 'id',
                    'icon' => '👁️',
                    'label' => 'View'
                ]);
            }
        ]
    ];

    return data_table::process_data_table(
        ['columns' => $columns],
        $sample_data,
        '',
        'test_generic_view_callback',
        [],
        [],
        [],
        false,
        false,
        false,
        false,
        null,
        'generic_view_test_table'
    );
}
?>
