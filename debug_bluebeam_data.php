<?php
/**
 * Debug Bluebeam Data
 * Check what data exists for EDM London in the Bluebeam table
 */

// Minimal bootstrap
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

try {
    startup_sequence::start($path,$schema);
    
    echo "=== Debug Bluebeam Data for EDM London ===\n";
    
    $table_name = 'autobooks_Bluebeam1514_data';
    
    // Check if table exists
    if (!database::tableExists($table_name)) {
        echo "❌ Table {$table_name} does not exist!\n";
        exit(1);
    }
    
    echo "✅ Table {$table_name} exists\n";
    
    // Get table structure
    echo "\n=== Table Structure ===\n";
    $columns = database::select("DESCRIBE {$table_name}");
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    
    // Search for EDM London entries
    echo "\n=== Searching for EDM London ===\n";
    
    $search_queries = [
        "SELECT * FROM {$table_name} WHERE name LIKE '%EDM%' LIMIT 5",
        "SELECT * FROM {$table_name} WHERE name LIKE '%London%' LIMIT 5", 
        "SELECT * FROM {$table_name} WHERE name LIKE '%edm%' LIMIT 5",
        "SELECT DISTINCT name FROM {$table_name} WHERE name LIKE '%EDM%' OR name LIKE '%London%' OR name LIKE '%edm%' LIMIT 10"
    ];
    
    foreach ($search_queries as $query) {
        echo "\nQuery: {$query}\n";
        $results = database::select($query);
        
        if (empty($results)) {
            echo "No results found\n";
        } else {
            foreach ($results as $result) {
                if (isset($result['name'])) {
                    echo "Found: {$result['name']}\n";
                } else {
                    echo "Result: " . json_encode($result) . "\n";
                }
            }
        }
    }
    
    // Get some sample data
    echo "\n=== Sample Data (first 5 records) ===\n";
    $sample_data = database::select("SELECT * FROM {$table_name} LIMIT 5");
    
    foreach ($sample_data as $i => $record) {
        echo "\nRecord " . ($i + 1) . ":\n";
        foreach ($record as $field => $value) {
            echo "  {$field}: {$value}\n";
        }
    }
    
    // Test the unified field mapper
    echo "\n=== Testing Unified Field Mapper ===\n";
    if (!empty($sample_data)) {
        $test_record = $sample_data[0];
        echo "Testing with record: " . json_encode($test_record) . "\n";
        
        $normalized = \system\unified_field_mapper::normalize_entry($test_record, $table_name);
        echo "Normalized result:\n";
        foreach ($normalized as $field => $value) {
            echo "  {$field}: {$value}\n";
        }
    }
    
    // Check what the subscription matcher would find
    echo "\n=== Testing Subscription Matcher ===\n";
    $matcher = new subscription_matcher();
    $csv_entries = $matcher->get_all_csv_entries(['limit' => 10]);
    
    echo "Found " . count($csv_entries) . " total CSV entries\n";
    
    $bluebeam_entries = array_filter($csv_entries, function($entry) use ($table_name) {
        return ($entry['source_table'] ?? '') === $table_name;
    });
    
    echo "Found " . count($bluebeam_entries) . " Bluebeam entries\n";
    
    if (!empty($bluebeam_entries)) {
        echo "Sample Bluebeam entries:\n";
        $count = 0;
        foreach ($bluebeam_entries as $entry) {
            if ($count >= 3) break;
            echo "Entry " . ($count + 1) . ":\n";
            echo "  Company: " . ($entry['company_name'] ?? $entry['endcust_name'] ?? $entry['name'] ?? 'NOT_SET') . "\n";
            echo "  Email: " . ($entry['email_address'] ?? $entry['endcust_primary_admin_email'] ?? 'NOT_SET') . "\n";
            echo "  Product: " . ($entry['product_name'] ?? 'NOT_SET') . "\n";
            $count++;
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
?>
