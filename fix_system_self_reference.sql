-- Fix self-referencing navigation entries
-- This fixes entries where parent_path equals route_key (causing infinite loops)

-- Fix the system entry that has parent_path = 'system' and route_key = 'system'
UPDATE autobooks_navigation 
SET parent_path = 'root', updated_at = NOW() 
WHERE route_key = 'system' AND parent_path = 'system';

-- Fix any other self-referencing entries
UPDATE autobooks_navigation 
SET parent_path = 'root', updated_at = NOW() 
WHERE parent_path = route_key AND parent_path != 'root';

-- Check for any remaining circular references
SELECT id, route_key, name, parent_path, 
       CASE 
           WHEN parent_path = route_key THEN 'SELF-REFERENCE'
           WHEN parent_path LIKE CONCAT('%', route_key, '%') THEN 'CIRCULAR-REFERENCE'
           ELSE 'OK'
       END as status
FROM autobooks_navigation 
WHERE parent_path != 'root' 
ORDER BY status DESC, parent_path, route_key;
